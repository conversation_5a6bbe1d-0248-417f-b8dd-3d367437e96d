package com.ly.titc.pms.member.com.utils;

import com.ly.titc.common.constants.Constant;

import java.math.BigDecimal;
import java.util.StringJoiner;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @classname CommonUtil
 * @descrition
 * @since 2019/11/26 14:45
 */
public class CommonUtil {

  /**
   * concat key
   *
   * @param args
   * @return
   */
  public static String concat(Object... args) {

    if (null == args || args.length == 0) {
      return "";
    }
    StringJoiner sj = new StringJoiner(Constant.STRING_LINE_UNDER);
    for (Object arg : args) {
      sj.add(String.valueOf(arg));
    }
    return sj.toString();
  }

  /**
   * concat key
   *
   * @param args
   * @return
   */
  public static String concatWithDelimiter(CharSequence delimiter, Object... args) {

    if (null == args || args.length == 0) {
      return "";
    }
    StringJoiner sj = new StringJoiner(delimiter);
    for (Object arg : args) {
      sj.add(String.valueOf(arg));
    }
    return sj.toString();
  }

}
