package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：ChangeTypeEnum
 * @Date：2024-11-21 17:04
 * @Filename：ChangeTypeEnum
 */
public enum ChangeTypeEnum {

    //等级变化类型，1注册; 2升级；3保级成功；4保级失败; 5手动处理; 6迁移数据
    REGISTER(1, "注册"),
    UPGRADE(2, "升级"),
    SUCCESS(3, "保级成功"),
    FAIL(4, "保级失败"),
    MANUAL(5, "手动处理"),
    MIGRATION(6, "迁移数据");

    private Integer type;
    private String desc;

    ChangeTypeEnum(Integer type, String desc) {
        this.type = type;
        this.desc = desc;
    }

    public Integer getType() {
        return type;
    }

    public String getDesc() {
        return desc;
    }

    public static String getDescByType(Integer type) {
        for (ChangeTypeEnum value : ChangeTypeEnum.values()) {
            if (value.getType().equals(type)) {
                return value.getDesc();
            }
        }
        return null;
    }

    public static Integer getTypeByDesc(String desc) {
        for (ChangeTypeEnum value : ChangeTypeEnum.values()) {
            if (value.getDesc().equals(desc)) {
                return value.getType();
            }
        }
        return null;
    }
}
