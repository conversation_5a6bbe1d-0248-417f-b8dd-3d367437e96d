package com.ly.titc.pms.member.com.enums;

/**
 * @Author：rui
 * @name：Ethnicity
 * @Date：2025-1-16 11:30
 * @Filename：Ethnicity
 */
public enum Ethnicity {
    AC("阿昌族", "AC"),
    BA("白族", "BA"),
    BL("布朗族", "BL"),
    BN("保安族", "BN"),
    BY("布依族", "BY"),
    CS("朝鲜族", "CS"),
    DA("傣族", "DA"),
    DE("德昂族", "DE"),
    DO("侗族", "DO"),
    DR("独龙族", "DR"),
    DU("达斡尔族", "DU"),
    DX("东乡族", "DX"),
    EW("鄂温克族", "EW"),
    GI("京族", "GI"),
    GL("仡佬族", "GL"),
    GS("高山族", "GS"),
    HA("汉族", "HA"),
    HN("哈尼族", "HN"),
    HU("回族", "HU"),
    HZ("赫哲族", "HZ"),
    JN("基诺族", "JN"),
    JP("景颇族", "JP"),
    KG("柯尔克孜族", "KG"),
    KZ("哈萨克族", "KZ"),
    LB("珞巴族", "LB"),
    LH("拉祜族", "LH"),
    LI("黎族", "LI"),
    LS("傈僳族", "LS"),
    MA("满族", "MA"),
    MB("门巴族", "MB"),
    MG("蒙古族", "MG"),
    MH("苗族", "MH"),
    ML("仫佬族", "ML"),
    MN("毛南族", "MN"),
    NU("怒族", "NU"),
    NX("纳西族", "NX"),
    OR("鄂伦春族", "OR"),
    PM("普米族", "PM"),
    QI("羌族", "QI"),
    RS("俄罗斯族", "RS"),
    SH("畲族", "SH"),
    SL("撒拉族", "SL"),
    SU("水族", "SU"),
    TA("塔吉克族", "TA"),
    TJ("土家族", "TJ"),
    TT("塔塔尔族", "TT"),
    TU("土族", "TU"),
    UG("维吾尔族", "UG"),
    UZ("乌孜别克族", "UZ"),
    VA("佤族", "VA"),
    XB("锡伯族", "XB"),
    YA("瑶族", "YA"),
    YG("裕固族", "YG"),
    YI("彝族", "YI"),
    ZA("藏族", "ZA"),
    ZH("壮族", "ZH");

    private final String label;
    private final String value;

    Ethnicity(String label, String value) {
        this.label = label;
        this.value = value;
    }

    public String getLabel() {
        return label;
    }

    public String getValue() {
        return value;
    }

    public static Ethnicity fromValue(String value) {
        for (Ethnicity ethnicity : Ethnicity.values()) {
            if (ethnicity.getValue().equals(value)) {
                return ethnicity;
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }

    public static String getLabelByValue(String value) {
        for (Ethnicity ethnicity : Ethnicity.values()) {
            if (ethnicity.getValue().equals(value)) {
                return ethnicity.getLabel();
            }
        }
        throw new IllegalArgumentException("Unknown value: " + value);
    }
}

