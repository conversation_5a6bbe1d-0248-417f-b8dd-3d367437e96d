package com.ly.titc.pms.member.dubbo.provider.converter;

import com.ly.titc.pms.member.dal.entity.po.MemberProfileAddressInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileInvoiceHeaderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileOccupantsInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileTagInfo;
import com.ly.titc.pms.member.dubbo.entity.request.profile.AddMemberTagReq;
import com.ly.titc.pms.member.dubbo.entity.request.profile.SaveCommonAddressReq;
import com.ly.titc.pms.member.dubbo.entity.request.profile.SaveCommonInvoiceHeaderReq;
import com.ly.titc.pms.member.dubbo.entity.request.profile.SaveCommonOccupantsReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileAddressResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileInvoiceHeaderResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileOccupantsResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileTagResp;
import com.ly.titc.pms.member.mediator.entity.dto.profile.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/6 10:52
 */
@Mapper(componentModel = "spring")
public interface MemberProfileConverter {

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    SaveMemberProfileAddressDto convertReqToPo(SaveCommonAddressReq req);

    MemberProfileAddressResp convertPoToResp(MemberProfileAddressInfoDto memberProfileAddressInfo);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    SaveMemberProfileInvoiceHeaderDto convertReqToPo(SaveCommonInvoiceHeaderReq req);

    MemberProfileInvoiceHeaderResp convertPoToResp(MemberProfileInvoiceHeaderInfoDto memberProfileInvoiceHeaderInfo);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    SaveMemberProfileOccupantsDto convertReqToPo(SaveCommonOccupantsReq req);

    MemberProfileOccupantsResp convertPoToResp(MemberProfileOccupantsInfoDto memberProfileOccupantsInfo);

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    SaveMemberProfileTagDto convertReqToPo(AddMemberTagReq req);

    MemberProfileTagResp convertPoToResp(MemberProfileTagInfoDto memberProfileTagInfo);
}
