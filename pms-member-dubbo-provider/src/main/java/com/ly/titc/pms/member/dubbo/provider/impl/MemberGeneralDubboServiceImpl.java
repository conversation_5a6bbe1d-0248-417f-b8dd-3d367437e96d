package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.general.QueryMemberIdentityReq;
import com.ly.titc.pms.member.dubbo.entity.request.general.QueryMemberSourceReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberGeneralCardConfigResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberGeneralSourceResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberGeneralDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberGeneralDubboServiceImpl
 * @Date：2024-12-3 20:30
 * @Filename：MemberGeneralDubboServiceImpl
 */
@Slf4j
@Validated
@DubboService
public class MemberGeneralDubboServiceImpl implements MemberGeneralDubboService {

    @Override
    public Response<MemberGeneralSourceResp> queryMemberSource(QueryMemberSourceReq req) {
                return null;
    }

    @Override
    public Response<List<MemberGeneralCardConfigResp>> queryMemberIdentity(QueryMemberIdentityReq req) {
        return null;
    }
}
