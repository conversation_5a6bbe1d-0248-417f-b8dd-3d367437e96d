package com.ly.titc.pms.member.dubbo.provider.impl;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileAddressInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileInvoiceHeaderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileOccupantsInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileTagInfo;
import com.ly.titc.pms.member.dubbo.entity.request.profile.*;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileAddressResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileInvoiceHeaderResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileOccupantsResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileTagResp;
import com.ly.titc.pms.member.dubbo.interfaces.MemberProfileDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberProfileConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.*;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.member.mediator.service.MemberProfileMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员档案Dubbo服务实现
 *
 * <AUTHOR>
 * @date 2024/11/6 10:51
 */
@Slf4j
@Validated
@DubboService
public class MemberProfileDubboServiceImpl implements MemberProfileDubboService {

    @Resource(type = MemberProfileMedService.class)
    private MemberProfileMedService memberProfileMedService;
    @Resource(type = MemberMedService.class)
    private MemberMedService memberMedService;
    @Resource(type = MemberProfileConverter.class)
    private MemberProfileConverter memberProfileConverter;

    @Override
    public Response<String> saveCommonAddress(SaveCommonAddressReq req) {
        SaveMemberProfileAddressDto info = memberProfileConverter.convertReqToPo(req);
        memberProfileMedService.saveCommonAddress(info);
        return Response.success(null);
    }

    @Override
    public Response<String> deleteCommonAddress(DeleteCommonAddressReq req) {
        Long addressNo = req.getAddressNo();
        Integer masterType = req.getMasterType();
        String masterCode = req.getMasterCode(), memberNo = req.getMemberNo(), operator = req.getOperator();
        memberProfileMedService.deleteCommonAddress(masterType, masterCode, memberNo, addressNo, operator);
        return Response.success(null);
    }

    @Override
    public Response<List<MemberProfileAddressResp>> listCommonAddress(ListCommonAddressReq req) {
        String masterCode = req.getMasterCode(), memberNo = req.getMemberNo(), trackingId = req.getTrackingId();
        Integer masterType = req.getMasterType();
        List<MemberProfileAddressInfoDto> memberProfileAddressInfos = memberProfileMedService.listCommonAddress(masterType, masterCode, memberNo, null);
        List<MemberProfileAddressResp> addressInfos = memberProfileAddressInfos.stream().map(memberProfileAddressInfo -> memberProfileConverter.convertPoToResp(memberProfileAddressInfo)).collect(Collectors.toList());
        return Response.success(addressInfos);
    }


    @Override
    public Response<String> saveCommonInvoiceHeader(SaveCommonInvoiceHeaderReq req) {
        SaveMemberProfileInvoiceHeaderDto info = memberProfileConverter.convertReqToPo(req);
        memberProfileMedService.saveCommonInvoiceHeader(info);
        return Response.success(null);
    }

    @Override
    public Response<String> deleteCommonInvoiceHeader(DeleteCommonInvoiceHeaderReq req) {
        Long invoiceHeaderNo = req.getInvoiceHeaderNo();
        Integer masterType = req.getMasterType();
        String masterCode = req.getMasterCode(), memberNo = req.getMemberNo(), operator = req.getOperator();
        memberProfileMedService.deleteCommonInvoiceHeader(masterType, masterCode, memberNo, invoiceHeaderNo, operator);
        return Response.success(null);
    }

    @Override
    public Response<List<MemberProfileInvoiceHeaderResp>> listCommonInvoiceHeader(ListCommonInvoiceHeaderReq req) {
        String masterCode = req.getMasterCode(), memberNo = req.getMemberNo(), trackingId = req.getTrackingId();
        Integer masterType = req.getMasterType();
        List<MemberProfileInvoiceHeaderInfoDto> memberProfileInvoiceHeaderInfos = memberProfileMedService.listCommonInvoiceHeader(masterType, masterCode, memberNo, null);
        List<MemberProfileInvoiceHeaderResp> invoiceHeaders = memberProfileInvoiceHeaderInfos.stream().map(memberProfileInvoiceHeaderInfo -> memberProfileConverter.convertPoToResp(memberProfileInvoiceHeaderInfo)).collect(Collectors.toList());
        return Response.success(invoiceHeaders);
    }

    @Override
    public Response<String> saveCommonOccupants(SaveCommonOccupantsReq req) {
        SaveMemberProfileOccupantsDto info = memberProfileConverter.convertReqToPo(req);
        memberProfileMedService.saveCommonOccupants(info);
        return Response.success(null);
    }

    @Override
    public Response<String> deleteCommonOccupants(DeleteCommonOccupantsReq req) {
        Long occupantsNo = req.getOccupantsNo();
        Integer masterType = req.getMasterType();
        String masterCode = req.getMasterCode(), memberNo = req.getMemberNo(), operator = req.getOperator();
        memberProfileMedService.deleteCommonOccupants(masterType, masterCode, memberNo, occupantsNo, operator);
        return Response.success(null);
    }

    @Override
    public Response<List<MemberProfileOccupantsResp>> listCommonOccupants(ListCommonOccupantsReq req) {
        String masterCode = req.getMasterCode(), memberNo = req.getMemberNo(), trackingId = req.getTrackingId();
        Integer masterType = req.getMasterType();
        List<MemberProfileOccupantsInfoDto> memberProfileOccupantsInfos = memberProfileMedService.listCommonOccupants(masterType, masterCode, memberNo, null);
        List<MemberProfileOccupantsResp> occupants = memberProfileOccupantsInfos.stream().map(memberProfileOccupantsInfo -> memberProfileConverter.convertPoToResp(memberProfileOccupantsInfo)).collect(Collectors.toList());
        return Response.success(occupants);
    }

    @Override
    public Response<String> addMemberTag(AddMemberTagReq req) {
        SaveMemberProfileTagDto info = memberProfileConverter.convertReqToPo(req);
        memberProfileMedService.addMemberTag(info);
        return Response.success(null);
    }

    @Override
    public Response<String> deleteMemberTag(DeleteMemberTagReq req) {
        Long tagNo = req.getTagNo();
        Integer masterType = req.getMasterType();
        String masterCode = req.getMasterCode(), memberNo = req.getMemberNo(), operator = req.getOperator(), trackingId = req.getTrackingId();
        memberProfileMedService.deleteMemberTag(masterType, masterCode, memberNo, tagNo, operator);
        return Response.success(null);
    }

    @Override
    public Response<List<MemberProfileTagResp>> listMemberTag(ListMemberTagReq req) {
        Integer masterType = req.getMasterType();
        String masterCode = req.getMasterCode(), memberNo = req.getMemberNo();
        List<MemberProfileTagInfoDto> memberProfileTagInfos = memberProfileMedService.listMemberTag(masterType, masterCode, memberNo);
        List<MemberProfileTagResp> memberTags = memberProfileTagInfos.stream().map(memberProfileTagInfo -> memberProfileConverter.convertPoToResp(memberProfileTagInfo)).collect(Collectors.toList());
        return Response.success(memberTags);
    }
}
