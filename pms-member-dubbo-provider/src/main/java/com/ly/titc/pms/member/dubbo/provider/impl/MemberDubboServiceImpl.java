package com.ly.titc.pms.member.dubbo.provider.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.dubbo.entity.request.member.*;
import com.ly.titc.pms.member.dubbo.entity.request.register.MemberRegisterReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberCardInfoResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberInfoResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRegisterResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberWithCardInfoResp;
import com.ly.titc.pms.member.dubbo.enums.ConsistencyEnum;
import com.ly.titc.pms.member.dubbo.interfaces.MemberDubboService;
import com.ly.titc.pms.member.dubbo.provider.converter.MemberConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.dubbo.config.annotation.DubboService;
import org.springframework.validation.annotation.Validated;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.ly.titc.pms.member.com.enums.RespCodeEnum.MEMBER_10011;

/**
 * 会员Dubbo服务实现
 *
 * <AUTHOR>
 * @date 2024/10/29 13:47
 */
@Slf4j
@Validated
@DubboService
public class MemberDubboServiceImpl implements MemberDubboService {

    @Resource(type = MemberConverter.class)
    private MemberConverter memberConverter;

    @Resource(type = MemberMedService.class)
    private MemberMedService memberMedService;

    @Resource(type = MemberCardMedService.class)
    private MemberCardMedService memberCardMedService;

    @Override
    public Response<MemberRegisterResp> register(MemberRegisterReq request) {
        RegisterMemberDto dto = memberConverter.convertReqToDto(request);
        RegisterMemberResultDto registerResult = memberMedService.register(dto);
        return Response.success(memberConverter.convertDtoToResp(registerResult));
    }

    @Override
    public Response<MemberInfoResp> getByMemberNo(GetByMemberNoReq request) {
        MemberDetailDto memberDetailDto = memberMedService.getDetailByMemberNo(request.getMasterType(), request.getMasterCode(), request.getMemberNo());
        return Response.success(memberConverter.convertDtoToResp(memberDetailDto));
    }

    @Override
    public Response<MemberWithCardInfoResp> getDetailByMemberNo(GetByMemberNoReq request) {
        Integer masterType = request.getMasterType();
        String masterCode = request.getMasterCode(), memberNo = request.getMemberNo();

        MemberDetailDto memberDetailDto = memberMedService.getDetailByMemberNo(masterType, masterCode, memberNo);
        if (memberDetailDto == null) {
            return Response.success(null);
        }

        List<MemberCardInfoDto> memberCardInfos = memberCardMedService.listByMemberNo(masterType, masterCode, memberDetailDto.getMemberNo());
        return Response.success(assembleMemberWithCardInfoResp(memberDetailDto, memberCardInfos));
    }

    @Override
    public Response<Pageable<MemberWithCardInfoResp>> pageMember(PageMemberReq request) {
        PageMemberParamDto pageMemberParamBo = memberConverter.convertReqToBo(request);
        Pageable<MemberDetailDto> pageable = null;
        if (request.getQueryMode().equals(ConsistencyEnum.FINAL)) {
            pageable = memberMedService.pageMemberByFinalMode(pageMemberParamBo);
        } else {
            pageable = memberMedService.pageMemberByStrongMode(pageMemberParamBo);
        }
        return Response.success(PageableUtil.convert(pageable, this::assembleMemberWithCardInfoResp));
    }

    @Override
    public Response<List<MemberWithCardInfoResp>> listByMobiles(ListByMobileReq request) {
        Integer masterType = request.getMasterType(), state = request.getState();
        String masterCode = request.getMasterCode();
        List<String> mobiles = request.getMobiles();

        List<MemberDetailDto> memberInfos = memberMedService.listByMobiles(masterType, masterCode, mobiles, state);
        if (CollectionUtils.isEmpty(memberInfos)) {
            return Response.success(Lists.newArrayList());
        }

        List<MemberWithCardInfoResp> memberWithCardInfos = assembleMemberWithCardInfoResp(memberInfos);
        return Response.success(memberWithCardInfos);
    }

    @Override
    public Response<List<MemberWithCardInfoResp>> listByIdNos(ListByIdNoReq request) {
        Integer masterType = request.getMasterType(), state = request.getState();
        String masterCode = request.getMasterCode();
        Integer idType = request.getIdType();
        List<String> idNos = request.getIdNos();

        List<MemberDetailDto> memberInfos = memberMedService.listByIdNos(masterType, masterCode, idType, idNos, state);
        if (CollectionUtils.isEmpty(memberInfos)) {
            return Response.success(Lists.newArrayList());
        }

        List<MemberWithCardInfoResp> memberWithCardInfos = assembleMemberWithCardInfoResp(memberInfos);
        return Response.success(memberWithCardInfos);
    }

    @Override
    public Response<List<MemberWithCardInfoResp>> listByCardNos(ListByCardNoReq request) {
        Integer masterType = request.getMasterType(), state = request.getState();
        String masterCode = request.getMasterCode();
        List<String> cardNos = request.getCardNos();

        List<MemberDetailDto> memberInfos = memberMedService.listByCardNos(masterType, masterCode, cardNos, state);
        if (CollectionUtils.isEmpty(memberInfos)) {
            return Response.success(Lists.newArrayList());
        }

        List<MemberWithCardInfoResp> memberWithCardInfos = assembleMemberWithCardInfoResp(memberInfos);
        return Response.success(memberWithCardInfos);
    }

    @Override
    public Response<List<MemberWithCardInfoResp>> listByMemberNos(ListByMemberNoReq request) {
        Integer masterType = request.getMasterType();
        String masterCode = request.getMasterCode();
        List<String> memberNos = request.getMemberNos();

        List<MemberDetailDto> memberInfos = memberMedService.listByMemberNos(masterType, masterCode, memberNos);
        if (CollectionUtils.isEmpty(memberInfos)) {
            return Response.success(Lists.newArrayList());
        }

        List<MemberWithCardInfoResp> memberWithCardInfos = assembleMemberWithCardInfoResp(memberInfos);
        return Response.success(memberWithCardInfos);
    }

    @Override
    public Response<String> updateBaseInfo(UpdateBaseInfoReq request) {
        Integer masterType = request.getMasterType();
        String masterCode = request.getMasterCode();
        String trackingId = request.getTrackingId();
        String memberNo = request.getMemberNo();

        MemberDetailDto memberDetailDto = memberMedService.getDetailByMemberNo(masterType, masterCode, memberNo);
        if (Objects.isNull(memberDetailDto)) {
            log.info("this member is not exist!!trackingId:{};memberNo:{}", trackingId, memberNo);
            return Response.set(MEMBER_10011, null);
        }
        memberConverter.convertReqToPo(request, memberDetailDto);
        // 转换会员信息
        MemberInfo memberInfo = memberConverter.convertDtoToPo(memberDetailDto);
        MemberExtendInfo memberExtendInfo = memberConverter.convertDtoToExtendPo(memberDetailDto);
        MemberContactInfo memberContactInfo = memberConverter.convertDtoToPo(memberDetailDto.getMemberContactInfo());
        // 填充数据
        memberExtendInfo.setMemberNo(memberInfo.getMemberNo());
        memberContactInfo.setMemberNo(memberInfo.getMemberNo());

        UpdateMemberInfoDto dto = memberConverter.convertPoToDto(memberInfo, memberExtendInfo, memberContactInfo);
        memberMedService.updateMember(dto);

        return Response.success(memberNo);
    }

    @Override
    public Response<String> cancelByMemberNo(DeleteByMemberNoReq request) {
        Integer masterType = request.getMasterType();
        String masterCode = request.getMasterCode(),
                memberNo = request.getMemberNo(),
                operator = request.getOperator();

        memberMedService.cancelMember(masterType, masterCode, memberNo, operator);
        return Response.success(memberNo);
    }

    /**
     * 组装对象
     *
     * @param MemberDetailDto
     * @param memberCardInfos
     * @return
     */
    private MemberWithCardInfoResp assembleMemberWithCardInfoResp(MemberDetailDto MemberDetailDto, List<MemberCardInfoDto> memberCardInfos) {
        MemberInfoResp memberInfoResp = memberConverter.convertDtoToResp(MemberDetailDto);
        List<MemberCardInfoResp> cardInfos = memberCardInfos.stream().map(cardInfo -> memberConverter.convertPoToResp(cardInfo)).collect(Collectors.toList());
        MemberWithCardInfoResp memberWithCardInfoResp = new MemberWithCardInfoResp();
        memberWithCardInfoResp.setMemberInfo(memberInfoResp).setMemberCardInfos(cardInfos);
        return memberWithCardInfoResp;
    }

    /**
     * 组装对象
     *
     * @param memberInfos
     * @return
     */
    private List<MemberWithCardInfoResp> assembleMemberWithCardInfoResp(List<MemberDetailDto> memberInfos) {
        return memberInfos.stream().map(this::assembleMemberWithCardInfoResp).collect(Collectors.toList());
    }

    /**
     * 组装对象
     *
     * @param memberInfo
     * @return
     */
    private MemberWithCardInfoResp assembleMemberWithCardInfoResp(MemberDetailDto memberInfo) {
        MemberInfoResp memberInfoResp = memberConverter.convertDtoToResp(memberInfo);
        List<MemberCardInfoDto> cardInfos = memberInfo.getMemberCardInfos();
        List<MemberCardInfoResp> cardInfoResps = cardInfos.stream().map(cardInfo -> memberConverter.convertPoToResp(cardInfo)).collect(Collectors.toList());
        MemberWithCardInfoResp memberWithCardInfoResp = new MemberWithCardInfoResp();
        memberWithCardInfoResp.setMemberInfo(memberInfoResp).setMemberCardInfos(cardInfoResps);
        return memberWithCardInfoResp;
    }

}
