package com.ly.titc.pms.member.dubbo.entity.request.member;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.common.annotation.LegalNormalDate;
import com.ly.titc.common.annotation.LegalPhoneNumber;
import com.ly.titc.pms.member.dubbo.entity.request.register.MemberContactInfoReq;
import com.ly.titc.pms.member.dubbo.enums.*;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 修改会员信息入参
 *
 * <AUTHOR>
 * @title: UpdateBaseInfoReq
 * @projectName pms-member
 * @description: 修改会员信息入参
 * @date 2023/10/12 11:36
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class UpdateBaseInfoReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员编号
     */
    @NotNull(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 自定义会员号
     * <p>支持传入外部会员编号</p>
     */
    private String customizeMemberNo;

    /**
     * 会员手机号
     */
    @LegalPhoneNumber(message = "手机号不合法")
    private String mobile;

    /**
     * 证件类型
     */
    @LegalEnum(target = IdTypeEnum.class, methodName = "getType", message = "证件类型不合法")
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 会员昵称
     */
    private String nickName;

    /**
     * 会员真实姓名
     */
    private String realName;

    /**
     * 会员英文名
     */
    private String enName;

    /**
     * 性别1：男；2：女
     */
    @LegalEnum(target = GenderEnum.class, methodName = "getType", message = "性别不合法")
    private Integer gender;

    /**
     * 生日
     */
    @LegalNormalDate(message = "生日日期不合法[pattern:yyyy-MM-dd]")
    private String birthday;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 民族
     */
    private String nation;

    /**
     * 语言
     */
    private String language;

    /**
     * 车牌号
     */
    private String numberPlate;

    /**
     * 会员状态
     */
    @LegalEnum(target = MemberStateEnum.class, methodName = "getState", message = "会员状态不合法")
    private Integer state = MemberStateEnum.VALID.getState();

    /**
     * 备注
     */
    private String remark;

    /**
     * 联系方式
     */
    private MemberContactInfoReq memberContactInfo;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;


}
