package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.profile.*;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileAddressResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileInvoiceHeaderResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileOccupantsResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberProfileTagResp;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员档案Dubbo服务
 *
 * <AUTHOR>
 * @date 2024/10/28 16:04
 */
public interface MemberProfileDubboService {

    /**
     * 保存会员常用地址
     *
     * @param req
     * @return
     */
    Response<String> saveCommonAddress(@Valid SaveCommonAddressReq req);

    /**
     * 删除会员常用地址
     *
     * @param req
     * @return
     */
    Response<String> deleteCommonAddress(@Valid DeleteCommonAddressReq req);

    /**
     * 查询会员常用地址
     *
     * @param req
     * @return
     */
    Response<List<MemberProfileAddressResp>> listCommonAddress(@Valid ListCommonAddressReq req);

    /**
     * 保存常用发票抬头
     *
     * @param req
     * @return
     */
    Response<String> saveCommonInvoiceHeader(@Valid SaveCommonInvoiceHeaderReq req);

    /**
     * 删除常用发票抬头
     *
     * @param req
     * @return
     */
    Response<String> deleteCommonInvoiceHeader(@Valid DeleteCommonInvoiceHeaderReq req);

    /**
     * 查询常用发票抬头
     *
     * @param req
     * @return
     */
    Response<List<MemberProfileInvoiceHeaderResp>> listCommonInvoiceHeader(@Valid ListCommonInvoiceHeaderReq req);

    /**
     * 保存常用入住人
     *
     * @param req
     * @return
     */
    Response<String> saveCommonOccupants(@Valid SaveCommonOccupantsReq req);

    /**
     * 删除常用入住人
     *
     * @param req
     * @return
     */
    Response<String> deleteCommonOccupants(@Valid DeleteCommonOccupantsReq req);

    /**
     * 查询常用入住人
     *
     * @param req
     * @return
     */
    Response<List<MemberProfileOccupantsResp>> listCommonOccupants(@Valid ListCommonOccupantsReq req);

    /**
     * 添加会员标签
     *
     * @param req
     * @return
     */
    Response<String> addMemberTag(@Valid AddMemberTagReq req);

    /**
     * 删除会员标签
     *
     * @param req
     * @return
     */
    Response<String> deleteMemberTag(@Valid DeleteMemberTagReq req);

    /**
     * 查询会员标签
     *
     * @param req
     * @return
     */
    Response<List<MemberProfileTagResp>> listMemberTag(@Valid ListMemberTagReq req);
}
