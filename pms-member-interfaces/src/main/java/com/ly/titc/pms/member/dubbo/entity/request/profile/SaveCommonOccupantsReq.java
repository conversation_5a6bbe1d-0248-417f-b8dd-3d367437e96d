package com.ly.titc.pms.member.dubbo.entity.request.profile;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.common.annotation.LegalPhoneNumber;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.enums.IdTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 保存常用入住人
 *
 * <AUTHOR>
 * @date 2024/11/6 11:13
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveCommonOccupantsReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员编号
     */
    @NotEmpty(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String name;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @LegalPhoneNumber(message = "手机号不合法")
    private String mobile;

    /**
     * 证件号分类
     */
    @NotNull(message = "证件分类不能为空")
    @LegalEnum(target = IdTypeEnum.class, methodName = "getType", message = "证件类型不合法")
    private Integer idType;

    /**
     * 证件号
     */
    @NotBlank(message = "证件号不能为空")
    private String idNo;

    /**
     * 排序
     */
    @NotNull(message = "排序不能为空")
    private Integer sort;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;
}
