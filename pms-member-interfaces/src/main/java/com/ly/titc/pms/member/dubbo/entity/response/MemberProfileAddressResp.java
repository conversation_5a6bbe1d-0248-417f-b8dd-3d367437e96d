package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;

/**
 * 会员常用地址
 *
 * <AUTHOR>
 * @date 2024/11/6 10:47
 */
@Data
public class MemberProfileAddressResp {

    /**
     * 常用地址编号
     */
    private Long addressNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 省id
     */
    private Long provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域id
     */
    private Long districtId;

    /**
     * 区域
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 排序
     */
    private Integer sort;


}
