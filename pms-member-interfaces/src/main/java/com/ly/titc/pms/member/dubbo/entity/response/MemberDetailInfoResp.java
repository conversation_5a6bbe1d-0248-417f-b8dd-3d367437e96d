package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname MemberResp
 * @descrition 会员信息返回体
 * @since 2023/6/7 13:52
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class MemberDetailInfoResp extends MemberInfoResp {

    /**
     * 会员联系方式
     */
    private MemberContactInfoResp memberContactInfo;

    /**
     * 会员卡
     */
    private List<MemberCardInfoResp> memberCardInfos;
}
