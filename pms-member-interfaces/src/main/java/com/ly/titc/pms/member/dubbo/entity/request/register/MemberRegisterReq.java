package com.ly.titc.pms.member.dubbo.entity.request.register;

import com.ly.titc.common.annotation.*;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.dubbo.entity.request.card.MemberCardInfoReq;
import com.ly.titc.pms.member.dubbo.enums.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 会员注册会员请求体
 *
 * <AUTHOR>
 * @classname MobileRegisterReq
 * @descrition 手机号注册会员请求体
 * @since 2023/6/7 13:51
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class MemberRegisterReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 自定义会员号
     * <p>支持传入外部会员编号</p>
     */
    private String customizeMemberNo;

    /**
     * 会员手机号
     */
    @NotEmpty(message = "手机号不能为空")
    @LegalPhoneNumber(message = "手机号不合法")
    private String mobile;

    /**
     * 证件类型;
     */
    @LegalEnum(target = IdTypeEnum.class, methodName = "getType", message = "证件类型不合法")
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 会员昵称
     */
    private String nickName;

    /**
     * 会员真实姓名
     */
    @NotBlank(message = "会员真实姓名不能为空")
    private String realName;

    /**
     * 会员英文名
     */
    private String enName;

    /**
     * 性别1：男；2：女
     */
    @LegalEnum(target = GenderEnum.class, methodName = "getType", message = "性别不合法")
    private Integer gender;

    /**
     * 生日
     */
    @LegalNormalDate(message = "生日日期不合法[pattern:yyyy-MM-dd]")
    private String birthday;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 民族
     */
    private String nation;

    /**
     * 语言
     */
    private String language;

    /**
     * 车牌号
     */
    private String numberPlate;

    /**
     * 会员来源
     */
    @NotNull(message = "会员来源不能为空")
    @LegalEnum(target = PlatformChannelEnum.class, methodName = "getPlatformChannel", message = "会员来源不合法")
    private String source;

    /**
     * 注册门店类型 集团:BLOC;门店:HOTEL
     */
    @LegalEnum(target = HotelTypeEnum.class, methodName = "getType", message = "注册门店类型不合法")
    private String registerHotelType;

    /**
     * 注册门店(集团编号; 酒店编号)
     */
    private String registerHotel;

    /**
     * 销售员
     */
    private String salesman;

    /**
     * 会员状态
     */
    @LegalEnum(target = MemberStateEnum.class, methodName = "getState", message = "会员状态不合法")
    private Integer state = MemberStateEnum.VALID.getState();

    /**
     * 备注
     */
    private String remark;

    /**
     * 联系方式
     */
    private MemberContactInfoReq memberContactInfo;

    /**
     * 会员卡
     */
    @NotNull(message = "会员卡信息不能为空")
    @Valid
    private MemberCardInfoReq memberCardInfo;

    /**
     * 注册时间，不传则默认时间
     */
    @LegalNormalDateTime(message = "注册日期不合法[pattern:yyyy-MM-dd HH:mm:ss]")
    private String registerDate;

    /**
     * 操作人
     */
    @NotEmpty(message = "操作人不能为空")
    private String operator;

}
