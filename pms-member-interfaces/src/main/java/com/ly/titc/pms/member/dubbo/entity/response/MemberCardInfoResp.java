package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;
import lombok.experimental.Accessors;

import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @title: MemberCardInfoDto
 * @projectName pms-member
 * @description: 会员卡信息
 * @date 2023/11/16 15:46
 */
@Data
@Accessors(chain = true)
public class MemberCardInfoResp {

    /**
     * 会员卡类型
     */
    private Integer cardType;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 会员卡等级名称
     */
    private String cardLevelName;

    /**
     * 会员卡生效时间
     */
    private String effectBeginDate;

    /**
     * 会员卡失效时间
     */
    private String effectEndDate;

    /**
     * 状态 0 无效 1 有效 2 停用
     */
    private Integer state;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
