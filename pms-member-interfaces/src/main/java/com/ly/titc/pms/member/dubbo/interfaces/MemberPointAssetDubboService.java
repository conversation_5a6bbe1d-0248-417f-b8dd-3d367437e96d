package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.ReceiveMemberPointReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberRecordOPResultResp;

import javax.validation.Valid;

/**
 * <AUTHOR>
 * @classname
 * @descrition 会员积分资产
 * @since 2024-12-29 13:12
 */
public interface MemberPointAssetDubboService {

    /**
     * 获得积分 (赠送，发放，调整加积分)
     */
    Response<MemberRecordOPResultResp> receive(@Valid ReceiveMemberPointReq req);


    /**
     * 获得积分后撤回
     */
    Response<MemberRecordOPResultResp> receiveRollback(@Valid ReceiveMemberPointReq req);

    /**
     * 根据业务单号或者积分记录号查询积分消费记录
     */



}
