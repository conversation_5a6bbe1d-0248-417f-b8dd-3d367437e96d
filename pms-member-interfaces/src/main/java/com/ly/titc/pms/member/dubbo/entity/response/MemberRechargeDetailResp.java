package com.ly.titc.pms.member.dubbo.entity.response;

import java.math.BigDecimal;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @classname MemberRechargeDetailResp
 * @descrition 会员充值明细信息返回体
 * <AUTHOR>
 * @since 2023/8/16 10:18
 */
@Data
@Accessors(chain = true)
public class MemberRechargeDetailResp {

  /**
   * 会员编号
   */
  private String memberNo;

  /**
   * 会员归属编码(会员号-会员归属类别-会员归属值)
   */
  private String memberAffiliationCode;

  /**
   * 总金额
   */
  private BigDecimal totalAmount;

  /**
   * 充值金额
   */
  private BigDecimal rechargeAmount;

  /**
   * 赠送金额
   */
  private BigDecimal presentAmount;

  /**
   * 当前可用金额
   */
  private BigDecimal availableAmount;

  /**
   * 已使用金额
   */
  private BigDecimal usedAmount;

  /**
   * 过期金额
   */
  private BigDecimal expiredAmount;

  /**
   * 冻结金额
   */
  private BigDecimal frozenAmount;

  /**
   * 过期时间,格式 yyyy-MM-dd
   */
  private String expireDate;

  /**
   * 充值状态;0:不可用;1:可用;2:已过期
   */
  private Integer state;

}
