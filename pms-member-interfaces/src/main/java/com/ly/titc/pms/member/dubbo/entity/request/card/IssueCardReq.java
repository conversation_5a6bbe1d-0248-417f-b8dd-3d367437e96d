package com.ly.titc.pms.member.dubbo.entity.request.card;

import com.ly.titc.cc.entity.request.BaseReq;
import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 发放会员卡
 *
 * <AUTHOR>
 * @date 2024/10/31 10:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors
public class IssueCardReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属类型不能为空")
    @LegalEnum(target = MasterTypeEnum.class, methodName = "getType", message = "归属类型不合法")
    private Integer masterType;

    /**
     * 归属值
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 会员号
     */
    @NotBlank(message = "会员号不能为空")
    private String memberNo;

    /**
     * 会员卡
     */
    @NotNull(message = "会员卡信息不能为空")
    @Valid
    private MemberCardInfoReq memberCardInfo;

    /**
     * 操作人
     */
    @NotBlank(message = "操作人不能为空")
    private String operator;
}
