package com.ly.titc.pms.member.dubbo.entity.request.general;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：SelectMemberSourceReq
 * @Date：2024-12-3 19:59
 * @Filename：SelectMemberSourceReq
 */
@Data
public class QueryMemberSourceReq extends BaseReq {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @NotNull(message = "归属不能为空")
    private Integer masterType;

    /**
     * 归属值
     */
    @NotBlank(message = "归属值不能为空")
    private String masterCode;

    /**
     * 名称-模糊搜索
     */
    private String name;

}
