package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.card.GenerateCardNoReq;
import com.ly.titc.pms.member.dubbo.entity.request.card.IssueCardReq;
import com.ly.titc.pms.member.dubbo.entity.request.card.UpdateCardInfoReq;
import com.ly.titc.pms.member.dubbo.entity.request.card.UpdateCardLevelReq;

import javax.validation.Valid;

/**
 * 会员卡dubbo服务
 *
 * <AUTHOR>
 * @date 2024/10/25 14:00
 */
public interface MemberCardDubboService {

    /**
     * 发放会员卡
     *
     * @param request
     * @return
     */
    Response<String> issueCard(@Valid IssueCardReq request);

    /**
     * 会员卡信息更新
     *
     * @param request
     * @return
     */
    Response<String> updateCardInfo(@Valid UpdateCardInfoReq request);

    /**
     * 会员卡等级变更
     *
     * @param request
     * @return
     */
    Response<String> updateCardLevel(@Valid UpdateCardLevelReq request);

    /**
     * 生成卡号
     *
     * @param request
     * @return
     */
    Response<String> generateCardNo(@Valid GenerateCardNoReq request);

}
