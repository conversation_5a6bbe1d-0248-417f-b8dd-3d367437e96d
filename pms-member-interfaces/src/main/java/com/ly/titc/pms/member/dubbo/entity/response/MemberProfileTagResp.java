package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 会员标签
 *
 * <AUTHOR>
 * @date 2024/11/6 10:47
 */
@Data
public class MemberProfileTagResp {

    /**
     * 标签编号
     */
    private Long tagNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 标签ID
     */
    private Integer tagId;

    /**
     * 标签分类
     */
    private Integer tagType;

    /**
     * 打标分类: 1:手动标记 2:系统标记
     */
    private Integer markType;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 排序
     */
    private Integer sort;

}
