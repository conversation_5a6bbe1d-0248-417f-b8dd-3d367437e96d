package com.ly.titc.pms.member.dubbo.entity.response;

import lombok.Data;

/**
 * 会员常用发票抬头
 *
 * <AUTHOR>
 * @date 2024/11/6 10:47
 */
@Data
public class MemberProfileInvoiceHeaderResp {

    /**
     * 发票抬头编号
     */
    private Long invoiceHeaderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 发票类型；1:个人;2:企业;3:非企业性单位
     */
    private Long invoiceType;

    /**
     * 发票抬头
     */
    private String headerName;

    /**
     * 税号
     */
    private String taxCode;

    /**
     * 是否需要增值税专用发票
     */
    private Integer needSpecialInvoice;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 公司电话
     */
    private String companyTel;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 排序
     */
    private Integer sort;

}
