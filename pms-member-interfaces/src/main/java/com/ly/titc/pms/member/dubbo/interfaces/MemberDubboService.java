package com.ly.titc.pms.member.dubbo.interfaces;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.dubbo.entity.request.member.*;
import com.ly.titc.pms.member.dubbo.entity.request.register.MemberRegisterReq;
import com.ly.titc.pms.member.dubbo.entity.response.MemberRegisterResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberInfoResp;
import com.ly.titc.pms.member.dubbo.entity.response.MemberWithCardInfoResp;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员dubbo服务
 *
 * <AUTHOR>
 * @date 2024/10/25 13:59
 */
public interface MemberDubboService {

    /**
     * 会员注册
     *
     * @param request
     * @return
     */
    Response<MemberRegisterResp> register(@Valid MemberRegisterReq request);

    /**
     * 查询会员信息
     *
     * @param request
     * @return
     */
    Response<MemberInfoResp> getByMemberNo(@Valid GetByMemberNoReq request);

    /**
     * 查询会员和会员卡信息
     *
     * @param request
     * @return
     */
    Response<MemberWithCardInfoResp> getDetailByMemberNo(@Valid GetByMemberNoReq request);

    /**
     * 分页查询会员信息
     *
     * @param request
     * @return
     */
    Response<Pageable<MemberWithCardInfoResp>> pageMember(@Valid PageMemberReq request);

    /**
     * 根据手机号查询会员
     *
     * @param request
     * @return
     */
    Response<List<MemberWithCardInfoResp>> listByMobiles(@Valid ListByMobileReq request);

    /**
     * 根据证件号查询会员
     *
     * @param request
     * @return
     */
    Response<List<MemberWithCardInfoResp>> listByIdNos(@Valid ListByIdNoReq request);

    /**
     * 根据会员卡号查询会员
     *
     * @param request
     * @return
     */
    Response<List<MemberWithCardInfoResp>> listByCardNos(@Valid ListByCardNoReq request);

    /**
     * 根据会员卡号查询会员
     *
     * @param request
     * @return
     */
    Response<List<MemberWithCardInfoResp>> listByMemberNos(@Valid ListByMemberNoReq request);

    /**
     * 修改会员基本信息
     *
     * @param request
     * @return
     */
    Response<String> updateBaseInfo(@Valid UpdateBaseInfoReq request);

    /**
     * 根据会员号注销
     *
     * @param request
     * @return
     */
    Response<String> cancelByMemberNo(@Valid DeleteByMemberNoReq request);

}
