<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.ly.titc</groupId>
  <artifactId>pms-member-interfaces</artifactId>
  <version>1.0.1-SNAPSHOT</version>
  <packaging>jar</packaging>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <!--springboot-->
    <spring-boot-dependencies.version>1.0.1-SNAPSHOT</spring-boot-dependencies.version>
    <fastjson.version>2.0.32</fastjson.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.ly.titc</groupId>
        <artifactId>springboot-dependencies-pom</artifactId>
        <version>${spring-boot-dependencies.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <dependencies>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>common</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
      <scope>provided</scope>
    </dependency>
      <dependency>
          <groupId>com.ly.titc</groupId>
          <artifactId>pms-member-asset-interfaces</artifactId>
          <version>1.0.0-SNAPSHOT</version>
          <scope>compile</scope>
      </dependency>
  </dependencies>

  <distributionManagement>
    <repository>
      <id>mvn-zkt-release</id>
      <name>Releases</name>
      <url>https://nexus.17usoft.com/repository/mvn-zkt-release/</url>
    </repository>
    <snapshotRepository>
      <id>mvn-zkt-snapshots</id>
      <name>Snapshot</name>
      <url>https://nexus.17usoft.com/repository/mvn-zkt-snapshot/</url>
    </snapshotRepository>
  </distributionManagement>
  <repositories>
    <!-- &lt;!&ndash; ly.com maven mirror &ndash;&gt;-->
    <repository>
      <id>lyRepository</id>
      <name>lyRepository</name>
      <url>https://nexus.17usoft.com/repository/mvn-all/</url>
      <layout>default</layout>
      <releases>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </releases>
      <snapshots>
        <enabled>true</enabled>
        <updatePolicy>always</updatePolicy>
      </snapshots>
    </repository>
  </repositories>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>2.17</version>
          <configuration>
            <skip>true</skip>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-release-plugin</artifactId>
        <version>2.5.1</version>
        <configuration>
          <autoVersionSubmodules>true</autoVersionSubmodules>
          <useReleaseProfile>false</useReleaseProfile>
          <releaseProfiles>release</releaseProfiles>
          <goals>deploy</goals>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>8</source>
          <target>8</target>
        </configuration>
      </plugin>
      <!-- 生成sources源码包的插件 -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
        <version>2.1.2</version>
        <executions>
          <execution>
            <id>attach-sources</id>
            <goals>
              <goal>jar</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>