<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
  xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <artifactId>pms-member-parent</artifactId>
    <groupId>com.ly.titc</groupId>
    <version>1.0.0</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>pms-member-gateway</artifactId>

  <dependencies>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-biz</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-facade</artifactId>
      <version>1.0.0</version>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-web</artifactId>
    </dependency>
    <dependency>
      <groupId>com.github.lianjiatech</groupId>
      <artifactId>retrofit-spring-boot-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.turbomq</groupId>
      <artifactId>turbomq-client</artifactId>
    </dependency>
    <dependency>
      <groupId>com.ly.tcbase</groupId>
      <artifactId>cache</artifactId>
    </dependency>
    <!-- mapStruct-lombok -->
    <dependency>
      <groupId>org.projectlombok</groupId>
      <artifactId>lombok-mapstruct-binding</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-starter-test</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.amazonaws</groupId>
      <artifactId>aws-java-sdk-s3</artifactId>
    </dependency>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>pms-member-mediator</artifactId>
      <version>1.0.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.ly.titc</groupId>
      <artifactId>oauth-client</artifactId>
    </dependency>
  </dependencies>

  <build>
    <finalName>titc.java.pms.member.workbench.gateway</finalName>
    <resources>
      <resource>
        <directory>src/main/resources</directory>
        <excludes>
          <exclude>test/*</exclude>
          <exclude>qa/*</exclude>
          <exclude>uat/*</exclude>
          <exclude>stage/*</exclude>
          <exclude>product/*</exclude>
        </excludes>
      </resource>
      <resource>
        <directory>src/main/resources/${package.environment}</directory>
        <filtering>true</filtering>
      </resource>
    </resources>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <version>2.3.0.RELEASE</version>
        <configuration>
          <mainClass>com.ly.titc.pms.member.gateway.GatewayApplication</mainClass>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.8.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <showWarnings>true</showWarnings>
          <encoding>UTF-8</encoding>
          <annotationProcessorPaths>
            <!-- 引入lombok-->
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok</artifactId>
              <version>${lombok.version}</version>
            </path>
            <!-- 引入mapstruct-processor-->
            <path>
              <groupId>org.mapstruct</groupId>
              <artifactId>mapstruct-processor</artifactId>
              <version>${mapstruct.version}</version>
            </path>
            <path>
              <groupId>org.projectlombok</groupId>
              <artifactId>lombok-mapstruct-binding</artifactId>
              <version>${lombok-mapstruct-binding.version}</version>
            </path>
          </annotationProcessorPaths>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.github.shalousun</groupId>
        <artifactId>smart-doc-maven-plugin</artifactId>
        <version>2.2.1</version>
        <configuration>
          <!--指定生成文档的使用的配置文件,配置文件放在自己的项目中-->
          <configFile>./src/main/resources/${package.environment}/smart-doc.json</configFile>
          <!--指定项目名称-->
          <projectName>PMS在线文档api-doc</projectName>
          <!--smart-doc实现自动分析依赖树加载第三方依赖的源码，如果一些框架依赖库加载不到导致报错，这时请使用excludes排除掉-->
          <excludes>
            <!--格式为：groupId:artifactId;参考如下-->
            <exclude>com.alibaba:fastjson</exclude>
          </excludes>
          <!--自1.0.8版本开始，插件提供includes支持,配置了includes后插件会按照用户配置加载而不是自动加载，因此使用时需要注意-->
          <!--smart-doc能自动分析依赖树加载所有依赖源码，原则上会影响文档构建效率，因此你可以使用includes来让插件加载你配置的组件-->
          <includes>
            <!--格式为：groupId:artifactId;参考如下-->
            <include>com.alibaba:fastjson</include>
          </includes>
        </configuration>
        <executions>
          <execution>
            <!--如果不需要在执行编译时启动smart-doc，则将phase注释掉-->
            <phase>compile</phase>
            <goals>
              <!--smart-doc提供了html、openapi、markdown等goal，可按需配置-->
              <goal>html</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>