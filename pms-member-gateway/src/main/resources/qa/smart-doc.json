{"//": "在解析到常量时自动替换为具体的值;如：http://localhost:8080/testConstants/+ApiVersion.VERSION中的ApiVersion.VERSION会被替换", "serverUrl": "https://bapi-qa.zktsoft.com/pms-member", "isStrict": false, "allInOne": true, "outPath": "target/classes/static/doc", "coverOld": true, "packageFilters": "com.ly.titc.pms.member.gateway.controller", "style": "xt256", "createDebugPage": true, "allInOneDocFileName": "index.html", "md5EncryptedHtmlName": false, "projectName": "PMS在线文档api-doc", "skipTransientField": true, "showAuthor": true, "requestExample": true, "responseExample": true, "requestParamsTable": true, "responseParamsTable": true, "requestFieldToUnderline": false, "responseFieldToUnderline": false, "inlineEnum": true, "recursionLimit": 7, "displayActualType": false, "//urlSuffix": ".do", "appKey": "20201216788835306945118208", "appToken": "c16931fa6590483fb7a4e85340fcbfef", "secret": "<PERSON><PERSON>B9Q0UqujVxnfi@.I#V&tUUYZR", "openUrl": "http://localhost:7700/api", "debugEnvName": "测试环境", "debugEnvUrl": "http://127.0.0.1", "ignoreRequestParams": ["org.springframework.ui.ModelMap"], "dataDictionaries": [{"//": "数据字典对象的描述信息字典", "title": "状态", "enumClassName": "com.ly.titc.pms.member.com.enums.MemberStateEnum", "codeField": "state", "descField": "desc"}], "errorCodeDictionaries": [{"//": "数据字典对象的描述信息字典", "title": "title", "enumClassName": "com.ly.titc.pms.member.com.enums.RespCodeEnum", "codeField": "code", "descField": "desc"}], "revisionLogs": [{"//": "文档修订时间", "version": "1.0", "revisionTime": "2023-09-26 17:00"}], "apiConstants": [{"constantsClassName": "com.ly.titc.common.constants.Constant"}]}