server:
  name: titc.java.pms.member.workbench.gateway
  port: 8080
  shutdown: graceful
  servlet:
    context-path: /pms-member

######spring
spring:
  ###client名称：属性的值命名格式 是 ${一站式应用标识(appUk)}
  application:
    name: titc.java.pms.member.workbench.gateway
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
  http:
    encoding:
      charset: UTF-8
      force: true
      enabled: true

#### retrofit 全局配置
retrofit:
  # 连接池配置
  pool:
    # cc连接池配置
    kam:
      # 最大空闲连接数
      max-idle-connections: 10
      # 连接保活时间(秒)
      keep-alive-second: 300
  # 全局连接超时时间
  global-connect-timeout-ms: 60_000
  # 全局读取超时时间
  global-read-timeout-ms: 15_000
  # 全局写入超时时间
  global-write-timeout-ms: 60_000
  # 全局完整调用超时时间
  global-call-timeout-ms: 0

####dal 自定义配置
dal:
  #dbName
  dbName: TETitcPmsMemberDCDB
  #初始化连接
  initialSize: 5
  #最大连接数量
  maxActive: 300
  #最小空闲连接
  minIdle: 5
  #超时等待时间以毫秒为单位
  maxWait: 5000
  #指明是否在从池中取出连接前进行检验,如果检验失败
  testOnBorrow: false
  #指明是否在归还到池中前进行检验
  testOnReturn: false
  #指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
  testWhileIdle: true
  #在空闲连接回收器线程运行期间休眠的时间值,以毫秒为单位
  #timeBetweenEvictionRunsMillis: 60000
  #连接在池中保持空闲而不被空闲连接回收器线程 1000 * 60 * 3
  #minEvictableIdleTimeMillis: 180000
  #连接的最大存活时间，如果连接的最大时间大于maxEvictableIdleTimeMillis，则无视最小连接数强制回收
  #maxEvictableIdleTimeMillis: 360000
  #当建立新连接时被发送给JDBC驱动的连接参数
  connectionProperties[allowMultiQueries]: true

###mybatis-plus 配置
mybatis-plus:
  global-config:
    db-config:
      id-type: auto
      field-strategy: not_empty
      table-underline: true
      db-type: mysql
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  mapper-locations: classpath:mapper/*Mapper.xml
  type-aliases-package: com.ly.titc.pms.member.dal.entity

###旅智框架配置
titc:
  ###dal mapper scan
  dal-scan-package: com.ly.titc.pms.member.dal.dao*
  dal-sql-log:
    enable: true
  ###分布式自旋锁使用
  cache:
    project-name: titc.java.pms.member.workbench.gateway
    cache-items:
      - cache-name: pms.member.gateway.redis.instance
        isDefault: true

###turbomq config
turbomq:
  ##生产组
  producer:
    pms-member:
      group: titc_pms_member_gateway_producer_group
      ##QA环境nameserver地址
      nameserver: mqnameserver.qa.17usoft.com:9876

  ###应用配置
project:
  account: ecrm
  pwd: ecrm886688
  enName: ecrm

### dubbo consumer 配置
dubbo:
  scan:
    basePackages: com.ly.titc.ihotel.mediator
  consumer:
    protocol: dsf
    check: false
    retries: 0
    timeout: 6000
  registry:
    ###naming字段根据具体环境调整
    ###qa:tcbase_java_dsf_backend.sz.qa_default,
    ###stage: tcbase_java_dsf_backend.sz.stage_default,
    ###pro: tcbase_java_dsf_backend.sz.product_default
    address: dsf://sz.name.vip.17usoft.com?report-metadata=true&path=/naming/getNaming&naming=tcbase_java_dsf_backend.sz.qa_default
  application:
    service-discovery:
      migration: FORCE_APPLICATION
    enable-file-cache: false

###图片上传配置
ceph:
  bucket: zkt
  accessKey: FM5YGTH9W6KD28IEZJSW
  secretKey: uaCkRmZl5OmrYKKlzjfdOievACwyQtXIzwBaAI97