package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-25 16:00
 */
@Data
@Accessors(chain = true)
public class CouponStatisticsResponse {

    /**
     * 全部状态优惠券数量
     */
    private Integer allStateCouponNum;

    /**
     * 未使用优惠券数量
     */
    private Integer unUsedCouponNum;

    /**
     * 已使用优惠券数量
     */
    private Integer usedCouponNum;

    /**
     * 已作废优惠券数量
     */
    private Integer invalidCouponNum;

    /**
     * 已过期优惠券数量
     */
    private Integer expiredCouponNum;
}
