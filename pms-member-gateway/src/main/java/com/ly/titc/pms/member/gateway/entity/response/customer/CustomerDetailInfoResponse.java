package com.ly.titc.pms.member.gateway.entity.response.customer;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 客户详情
 *
 * <AUTHOR>
 * @date 2024/12/11 20:07
 */
@Data
public class CustomerDetailInfoResponse {

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 酒店编号
     */
    private String hotelCode;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员等级
     */
    private Integer cardLevel;

    /**
     * 会员等级
     */
    private String cardLevelName;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 证件号分类
     */
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 性别 1:男;2:女;3:保密
     */
    private Integer gender;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 当前在住房间
     */
    private String roomNo;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 民族
     */
    private String nation;

    /**
     * 车牌号
     */
    private String numberPlate;

    /**
     * 协议单位编号
     */
    private String agreementNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 语言
     */
    private String language;

    /**
     * 邮箱
     */
    private String email;

    /**
     * QQ号
     */
    private String qq;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 国家id
     */
    private Integer countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域id
     */
    private Integer districtId;

    /**
     * 区域
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

    /**
     * 是否是黑名单用户
     */
    private boolean isBlack;

}
