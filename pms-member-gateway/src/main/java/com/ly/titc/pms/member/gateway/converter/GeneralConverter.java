package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.ehr.entity.response.UserDivisionResp;
import com.ly.titc.mdm.entity.request.region.ListRegionByLevelReq;
import com.ly.titc.mdm.entity.response.area.AreaTreeResp;
import com.ly.titc.mdm.entity.response.brand.SelectBrandResp;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.region.RegionResp;
import com.ly.titc.mdm.enums.HotelOpenStateEnum;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberTagConfigInfoResp;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.com.enums.MemberTagEnum;
import com.ly.titc.pms.member.gateway.entity.request.general.ListRegionByLevelRequest;
import com.ly.titc.pms.member.gateway.entity.response.general.*;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberTagConfigInfoResponse;
import com.ly.titc.pms.member.mediator.entity.dto.general.DictDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberGeneralCardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberSourceDto;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberGeneralConverter
 * @Date：2024-12-4 17:02
 * @Filename：MemberGeneralConverter
 */
@Mapper(componentModel = "spring")
public interface GeneralConverter extends BaseConverter {

    List<MemberGeneralCardConfigResponse> convert(List<MemberGeneralCardConfigDto> list);

    List<MemberSourceResponse> convertMemberSource(List<MemberSourceDto> list);

    List<DictResponse> convertDict(List<DictDto> list);


    default List<BrandInfoResponse> convertBrandListResponses(List<SelectBrandResp> selectBrands, UserDivisionResp userDivisionResp){
        return selectBrands.stream()
                .filter(item-> userDivisionResp.isBrandCheckAll()
                        || (!CollectionUtils.isEmpty(userDivisionResp.getUserBrandList()) && userDivisionResp.getUserBrandList().contains(item.getBrandCode())))
                .map(this::convertBrandListResponse).collect(Collectors.toList());
    }
    BrandInfoResponse convertBrandListResponse(SelectBrandResp resp);

    default List<AreaTreeResponse> convertAreaTreesResponses(List<AreaTreeResp> listResp,UserDivisionResp userDivisionResp){
        if(CollectionUtils.isEmpty(listResp)){
            return Collections.emptyList();
        }
        return listResp.stream().map(item-> {
            AreaTreeResponse areaTreeResponse = convertAreaTreesResponse(item, userDivisionResp);
            areaTreeResponse.setChildren(convertAreaTreesResponses(item.getChildren(),userDivisionResp));
            return areaTreeResponse;
        }).collect(Collectors.toList());
    }

    @Mapping(target = "enabled", expression = "java(convertAreaDivision(resp,userDivisionResp))")
    @Mapping(target = "children",ignore = true)
    AreaTreeResponse convertAreaTreesResponse(AreaTreeResp resp,UserDivisionResp userDivisionResp);

    default Boolean convertAreaDivision(AreaTreeResp resp,UserDivisionResp userDivisionResp){
        return userDivisionResp.isAreaCheckAll()
                || (!CollectionUtils.isEmpty(userDivisionResp.getUserAreaList()) && userDivisionResp.getUserAreaList().contains(String.valueOf(resp.getAreaId()))
        );
    }

    default List<HotelInfoResponse> convertHotelInfoResponse(List<HotelBaseInfoResp> hotelBaseInfos, UserInfoDto user) {
        List<HotelInfoResponse> result = new ArrayList<>();
        if(CollectionUtils.isEmpty(hotelBaseInfos)){
            return result;
        }
        hotelBaseInfos.stream().parallel().forEach(hotelBaseInfo -> {
            if (!Objects.equals(hotelBaseInfo.getOpenState(), HotelOpenStateEnum.SUSPENDED.getOpenState())
                    && !Objects.equals(hotelBaseInfo.getOpenState(), HotelOpenStateEnum.CLOSED.getOpenState())) {
                HotelInfoResponse response = new HotelInfoResponse();
                response.setEnabled(user.getIsAdmin()
                        ? Boolean.TRUE
                        : CollectionUtils.isEmpty(user.getHotelCodes())
                        ? Boolean.FALSE
                        : user.getHotelCodes().contains(hotelBaseInfo.getHotelCode()) ? Boolean.TRUE : Boolean.FALSE);
                response.setHotelName(hotelBaseInfo.getHotelName());
                response.setHotelCode(hotelBaseInfo.getHotelCode());
                response.setRegionId(isValidRegion(hotelBaseInfo.getDistrictId()) ? hotelBaseInfo.getDistrictId()
                        : isValidRegion(hotelBaseInfo.getCityId()) ? hotelBaseInfo.getCityId()
                        : isValidRegion(hotelBaseInfo.getProvinceId()) ? hotelBaseInfo.getProvinceId() : Constant.ZERO);
                result.add(response);
            }
        });
        return result;
    }

    default List<RegionHotelTreeResponse> assembleRegionHotelTree(List<RegionResp> listRegionTree,
                                                                  Map<Integer,List<HotelInfoResponse>> regionAndHotelInfoMap,
                                                                  UserDivisionResp userDivisionResp) {
        if(CollectionUtils.isEmpty(listRegionTree)){
            return Collections.emptyList();
        }
        return listRegionTree.stream().map(regionInfo -> {
            RegionHotelTreeResponse response = new RegionHotelTreeResponse();
            response.setRegionId(regionInfo.getId());
            response.setName(regionInfo.getName());
            response.setEnabled(userDivisionResp.isRegionCheckAll()
                    || (!CollectionUtils.isEmpty(userDivisionResp.getUserRegionList()) && userDivisionResp.getUserRegionList().contains(String.valueOf(regionInfo.getId())))
            );
            response.setHotelInfos(regionAndHotelInfoMap.getOrDefault(regionInfo.getId(), Collections.emptyList()));
            response.setChildren(assembleRegionHotelTree(regionInfo.getChildren(), regionAndHotelInfoMap,userDivisionResp));
            return response;
        }).filter(response-> !CollectionUtils.isEmpty(response.getChildren()) || !CollectionUtils.isEmpty(response.getHotelInfos())).collect(Collectors.toList());
    }

    default List<RegionTreeResponse> assembleRegionTree(List<RegionResp> listRegionTree,Integer level,UserDivisionResp userDivisionResp) {
        if(CollectionUtils.isEmpty(listRegionTree)){
            return Collections.emptyList();
        }
        return listRegionTree.stream().map(regionInfo -> {
            RegionTreeResponse response = new RegionTreeResponse();
            response.setRegionId(regionInfo.getId());
            response.setName(regionInfo.getName());
            response.setEnabled(userDivisionResp.isRegionCheckAll()
                    || (!CollectionUtils.isEmpty(userDivisionResp.getUserRegionList()) && userDivisionResp.getUserRegionList().contains(String.valueOf(regionInfo.getId())))
            );
            if(regionInfo.getLevel() < level){
                response.setChildren(assembleRegionTree(regionInfo.getChildren(),level,userDivisionResp));
            }
            return response;
        }).collect(Collectors.toList());
    }

    default boolean isValidRegion(Integer regionId) {
        return null != regionId && regionId > 0;
    }

    default List<MemberGeneralTagConfigInfoResponse> convertMemberGeneralTagConfigInfoResponse(List<MemberTagConfigInfoResp> list) {
        Map<Integer, List<MemberTagConfigInfoResp> > map = list.stream().collect(Collectors.groupingBy(MemberTagConfigInfoResp::getType));
        return map.entrySet().stream().map(item -> {
            MemberGeneralTagConfigInfoResponse response = new MemberGeneralTagConfigInfoResponse();
            response.setType(item.getKey());
            response.setName(MemberTagEnum.getDescByType(item.getKey()));
            response.setMemberTags(convertMemberTagConfigInfoResp(item.getValue()));
            return response;
        }).collect(Collectors.toList());
    }

    List<MemberTagConfigInfoResponse> convertMemberTagConfigInfoResp(List<MemberTagConfigInfoResp> list);

    ListRegionByLevelReq convertRegionReq(ListRegionByLevelRequest request);

    RegionResponse convertRegionResponse(RegionResp resp);

    List<RegionResponse> convertRegionResponse(List<RegionResp> resps);
}
