package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.MemberStoreConsumeReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.MemberStoreFreezeReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.UnfreezeConsumeRecordNoReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeCalResp;
import com.ly.titc.pms.member.gateway.converter.AssetConverter;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreConsumeCalRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreConsumeRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreFreezeRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreUnFreezeRequest;
import com.ly.titc.pms.member.gateway.entity.response.asset.MemberStoreConsumeCalResponse;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.MemberStoreOpDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 储值操作
 *
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 15:32
 */
@Slf4j
@RestController
@RequestMapping("/member/store/op")
public class MemberStoreAssetOpController {
    @Resource
    private MemberStoreOpDecorator  storeOpDecorator;

    @Resource
    private AssetConverter converter;


    /**
     * 储值消费
     */
    @PostMapping("/consume")
    public Response<Boolean> consume(@Valid @RequestBody MemberStoreConsumeRequest request){
        MemberStoreConsumeReq req = converter.convert(request);
        storeOpDecorator.consumeStore(req);
        return Response.success(true);

    }

    /**
     * 储值消费预计算
     */
    @PostMapping("/consumeCal")
    public Response<MemberStoreConsumeCalResponse> consumeCal(@Valid @RequestBody MemberStoreConsumeCalRequest request){
        MemberStoreConsumeReq req = converter.convert(request);
        MemberStoreConsumeCalResp calResp = storeOpDecorator.consumeStoreCal(req);
        return Response.success(converter.convert(calResp));

    }


    /**
     * 储值冻结
     */
    @PostMapping("/freeze")
    public Response<Boolean> freeze(@Valid @RequestBody MemberStoreFreezeRequest request){
        MemberStoreFreezeReq req= converter.convert(request);
        storeOpDecorator.freeze(req);
        return Response.success(true);

    }


    /**
     * 解冻
     */
    @PostMapping("/unFreeze")
    public Response<Boolean> unFreeze(@Valid @RequestBody MemberStoreUnFreezeRequest request){
        UnfreezeConsumeRecordNoReq req = converter.convert(request);
        storeOpDecorator.unfreeze(req);
        return Response.success(true);

    }






}
