package com.ly.titc.pms.member.gateway.entity.response.cashier;

import com.ly.titc.pms.member.mediator.entity.dto.cashier.DisplayPayChannelStateDto;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-2 15:10
 */
@Data
@Accessors(chain = true)
public class PayCashierConfigResponse {
    private Long id;

    /**
     * 主体（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 主体code
     */
    private String masterCode;

    /**
     * 集团组code
     */
    private String clubCode;

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 收银产品 PMSPAY(PMS相关收银业务),WXBOOKINGJSPAY(微订房公众号支付),WXBOOKINGAPPLETPAY(微订房小程序支付)
     */
    private String cashierProductCode;

    /**
     * 收银场景 MEMBER_REGISTER(会员注册),MEMBER_PURCHASE_CARD(会员购卡),MEMBER_RECHARGE(会员充值).....
     */
    private List<String> cashierScenes;

    /**
     * 收银来源 PUB(集团组) BLOC (集团)，HOTEL(门店)
     */
    private List<String> cashierSources;


    /**
     * 真实收款方 1集团 2 门店 3 优先门店收款
     */
    private Integer payeeType;

    /**
     * 外显支付渠道 AGGPAY聚合支付,银行卡（POSCARD）,CASH(现金), CHARGE(挂账)
     */
    private List<DisplayPayChannelStateDto> displayPayChannelDtos;


}
