package com.ly.titc.pms.member.gateway.entity.request.asset.store;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * 储值解冻
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-20 10:33
 */
@Data
@Accessors(chain = true)
public class MemberStoreUnFreezeRequest extends BaseRequest {

    @NotEmpty(message = "会员号不能为空")
    private String memberNo;


    /**
     * 冻结记录号
     */
    @NotEmpty(message = "冻结记录号不能为空")
    private String consumeRecordNo;

    /**
     * 解冻原因
     */
    private String unfreezeReason;


}
