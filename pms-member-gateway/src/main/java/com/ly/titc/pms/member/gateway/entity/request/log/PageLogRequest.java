package com.ly.titc.pms.member.gateway.entity.request.log;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;

/**
 * @Author：rui
 * @name：PageLogRequest
 * @Date：2023-11-21 17:25
 * @Filename：PageLogRequest
 */
@Data
public class PageLogRequest extends PageBaseRequest {

    /**
     * 模块id
     */
    private Integer moduleId;

    /**
     * 行为id
     */
    private Integer actionId;

    /**
     * 操作对象名称
     */
    private String name;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 结束时间
     */
    private String endDate;

    /**
     * 操作内容，支持模糊
     */
    private String record;

    /**
     * 查询条件-操作人
     */
    private String logOperator;

    /**
     * ip
     */
    private String ip;
}
