package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-7 9:57
 */
@Data
@Accessors(chain = true)
public class PageCouponBatchDetailsRequest extends PageBaseRequest {

    /**
     * 客户编号
     */
    private String ownerCustomerNo;
    /**
     * 会员卡号
     */
    private String ownerMemberCardNo;


    @NotBlank
    private String batchCode;
}
