package com.ly.titc.pms.member.gateway.entity.request.member.card;

import com.ly.titc.pms.member.gateway.entity.request.member.DeleteBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：DeleteRuleRequest
 * @Date：2024-11-15 14:26
 * @Filename：DeleteRuleRequest
 */
@Data
public class DeletePrivilegeRequest extends DeleteBaseRequest {

    /**
     * 权益名称
     */
    @NotBlank(message = "权益名称不能为空")
    private String name;
}
