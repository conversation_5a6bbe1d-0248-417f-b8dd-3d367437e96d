package com.ly.titc.pms.member.gateway.controller.spm.coupon;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.gateway.converter.CouponCommonConverter;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.*;
import com.ly.titc.pms.member.mediator.rpc.dubbo.coupon.CouponCommonDecorator;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 优惠券通用接口
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Slf4j
@RestController
@RequestMapping("/coupon/common")
public class CouponCommonController {

    @Resource(type = CouponCommonDecorator.class)
    private CouponCommonDecorator couponCommonDecorator;
    @Resource(type = CouponCommonConverter.class)
    private CouponCommonConverter couponCommonConverter;


    /**
     * 优惠券类型
     */
    @PostMapping("/type/list")
    public Response<List<CouponTypeInfoResponse>> getCouponTypeInfo(){
        List<CouponTypeResp> couponTypeInfos = couponCommonDecorator.getCouponTypeInfo();
        return Response.success(couponCommonConverter.convertCouponTypeInfoResponses(couponTypeInfos));
    }

    /**
     * 优惠券模版状态
     */
    @PostMapping("/templateState/list")
    public Response<List<CouponTemplateStateResponse>> getCouponTemplateStateInfo(){
        List<CouponTemplateStateResp> couponTemplateStateInfos = couponCommonDecorator.getCouponTemplateStateInfo();
        return Response.success(couponCommonConverter.convertCouponTemplateStateResponses(couponTemplateStateInfos));
    }

    /**
     * 来源客户端信息
     */
    @PostMapping("/sourceClient/list")
    public Response<List<CouponSourceClientResponse>> getCouponSourceClientInfo(){
        List<CouponSourceClientResp> couponSourceClientInfos = couponCommonDecorator.getCouponSourceClientInfo();
        return Response.success(couponCommonConverter.convertCouponSourceClientResponses(couponSourceClientInfos));
    }

    /**
     * 券值类型信息
     */
    @PostMapping("/couponValueType/list")
    public Response<List<CouponValueTypeResponse>> getCouponValueTypeInfo(){
        List<CouponValueTypeResp> couponValueTypeInfos = couponCommonDecorator.getCouponValueTypeInfo();
        return Response.success(couponCommonConverter.convertCouponValueTypeResponses(couponValueTypeInfos));
    }

    /**
     * 入账项目类型
     */
    @PostMapping("/postingType/list")
    public Response<List<CouponPostingTypeResponse>> getCouponPostingTypeInfo(){
        List<CouponPostingTypeResp> couponPostingTypeInfos = couponCommonDecorator.getCouponPostingTypeInfo();
        return Response.success(couponCommonConverter.convertCouponPostingTypeResponses(couponPostingTypeInfos));
    }

    /**
     * 核销方式
     */
    @PostMapping("/redeemType/list")
    public Response<List<CouponRedeemTypeResponse>> getCouponRedeemTypeInfo(){
        List<CouponRedeemTypeResp> couponRedeemTypeInfos = couponCommonDecorator.getCouponRedeemTypeInfo();
        return Response.success(couponCommonConverter.convertCouponRedeemTypeResponses(couponRedeemTypeInfos));
    }
}
