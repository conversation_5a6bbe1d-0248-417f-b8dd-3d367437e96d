package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-6 19:30
 */
@Data
@Accessors(chain = true)
public class PageCouponBatchRequest extends PageBaseRequest {


    /**
     * 券类型
     */
    private Integer couponType;

    /**
     * 券名称
     * 模糊搜索
     */
    private String couponName;

    /**
     * 发放开始时间
     * 格式 yyyy-MM-dd HH:mm:ss
     */

    private String grantStartTime;
    /**
     * 发放结束时间
     * 格式 yyyy-MM-dd HH:mm:ss
     */
    private String grantEndTime;

    /**
     * 发放主体类型 1-集团 2-门店 3-艺龙会
     */
    private Integer masterType;
    /**
     * 生成主体门店时，必选
     * 酒店code
     */
    private String hotelCode;

    /**
     * 是否跨店使用
     */
    private Integer crossShop;

    /**
     * 操作人
     * 模糊搜索
     */
    private String operator;


}
