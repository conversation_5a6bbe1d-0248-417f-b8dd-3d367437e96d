package com.ly.titc.pms.member.gateway.entity.request.room;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SearchRoomTypeListRequest extends BaseRequest {

    /**
     * 房型名称或编码
     */
    private String roomTypeFuzzyNameOrCode;

    /**
     * 酒店名称 或 酒店code
     */
    private String hotelFuzzyNameOrCode;

    /**
     * 状态
     */
    private Integer state;
}
