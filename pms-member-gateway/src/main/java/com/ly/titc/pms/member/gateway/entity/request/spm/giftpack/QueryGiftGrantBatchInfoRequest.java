package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-28 19:53
 */
@Data
@Accessors(chain = true)
public class QueryGiftGrantBatchInfoRequest {

    @NotBlank(message = "集团编号不能为空")
    private String blocCode;

    @NotBlank(message = "礼包发放批次号不能为空")
    private String grantBatchNo;
}
