package com.ly.titc.pms.member.gateway.entity.request.customer;

import com.ly.titc.common.annotation.LegalPhoneNumber;
import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 * @date 2024/12/13 11:38
 */
@Data
public class CheckExistByMobileRequest extends BaseRequest {

    /**
     * 酒店编码
     */
    @NotBlank(message = "酒店编码不能为空")
    private String hotelCode;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @LegalPhoneNumber(message = "客户手机号不合法")
    private String mobile;

}
