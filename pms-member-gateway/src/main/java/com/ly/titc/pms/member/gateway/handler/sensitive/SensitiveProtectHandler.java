package com.ly.titc.pms.member.gateway.handler.sensitive;

import com.ly.titc.pms.member.gateway.manager.SensitiveProtectManager;

/**
 * 敏感保护
 *
 * <AUTHOR>
 * @date 2024/12/4 14:46
 */
public abstract class SensitiveProtectHandler {

    public SensitiveProtectHandler() {
        SensitiveProtectManager.putInstance(fieldType(), this);
    }

    /**
     * 字段类型（手机号、证件号、邮箱）
     *
     * @return
     */
    public abstract String fieldType();

    /**
     * 敏感字段处理
     *
     * @param field
     * @return
     */
    public abstract String handle(String field);

}
