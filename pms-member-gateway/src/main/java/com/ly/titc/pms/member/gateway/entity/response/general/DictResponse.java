package com.ly.titc.pms.member.gateway.entity.response.general;

import com.ly.titc.pms.member.mediator.entity.dto.general.DictItemDto;
import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：DticResponse
 * @Date：2024-12-4 17:17
 * @Filename：DticResponse
 */
@Data
public class DictResponse {

    /**
     * 名称
     */
    private String name;

    /**
     * 值
     */
    private String value;

    /**
     * 子集
     */
    private List<DictItemResponse> items;
}
