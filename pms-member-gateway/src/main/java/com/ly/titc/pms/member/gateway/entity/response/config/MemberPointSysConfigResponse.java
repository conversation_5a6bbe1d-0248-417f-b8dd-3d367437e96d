package com.ly.titc.pms.member.gateway.entity.response.config;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-24 13:43
 */
@Data
@Accessors(chain = true)
public class MemberPointSysConfigResponse {
    private Long id;

    /**
     * 积分有效期
     */
    private Integer pointLimit;


    /**
     * 积分有效期单位 年 YEAR 月:MONTH 日; DAY
     */
    private String pointLimitUnit;

    /**
     * 长期有效 0 否 1 是
     */
    private Integer pointLimitLong;
}
