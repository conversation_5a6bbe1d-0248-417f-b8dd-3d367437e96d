package com.ly.titc.pms.member.gateway.controller.file;


import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.CephUtil;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.util.UUID;

/**
 *  图片相关
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2023-10-17 19:48
 */

@Slf4j
@RestController
@RequestMapping("/image")
public class ImageController {

    @Value("${ceph.bucket}")
    private String bucket;
    @Value("${ceph.accessKey}")
    private String accessKey;
    @Value("${ceph.secretKey}")
    private String secretKey;


    /**
     * 图片上传
     * @param image
     * @return
     * @throws IOException
     */
    @PostMapping("/uploadImage")
    public Response<String> uploadImage(@RequestParam("image") MultipartFile image) throws IOException {
        if (null == image) {
            log.warn("request param illegal");
            return Response.set(RespCodeEnum.CODE_400, null);
        }
        try {
            String url = CephUtil.getUrlWithPublicRead(bucket, accessKey, secretKey, CommonConstant.IMAGE_PATH+ UUID.randomUUID()+ image.getOriginalFilename(), image.getBytes());
            log.info("图片上传，url:{}", url);
            if(null == url || "".equals(url)) {
                log.warn("图片上传失败，图片地址为空");
                return Response.set(RespCodeEnum.CODE_500, null);
            }
            return Response.success(url.replace("http://tcstore1.17usoft.com", "https://oss.17usoft.com"));
        }catch (Exception e){
            log.warn("图片上传失败，系统异常, message:{}", e.getMessage());
            return Response.set(RespCodeEnum.CODE_500, null);
        }
    }
}
