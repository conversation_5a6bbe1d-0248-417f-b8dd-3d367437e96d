package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-28
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageCouponApplicableRatePlanRequest extends PageBaseRequest {

    /**
     * 优惠券编码不能为空
     */
    @NotBlank(message = "优惠券模版编码不能为空")
    private String templateCode;

    /**
     * 优惠券模版版本不能为空
     */
    @NotBlank(message = "优惠券模版版本不能为空")
    private String version;

    /**
     * 酒店名称或编码
     */
    private String hotelNameOrCode;

    /**
     * 价格方案名称或编码
     */
    private String ratePlanNameOrCode;
}
