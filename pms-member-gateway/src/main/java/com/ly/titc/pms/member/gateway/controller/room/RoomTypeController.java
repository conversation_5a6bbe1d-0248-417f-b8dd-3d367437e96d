package com.ly.titc.pms.member.gateway.controller.room;

import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.mdm.entity.response.hotel.room.type.RoomTypeBaseInfoResp;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.member.gateway.converter.RoomTypeConverter;
import com.ly.titc.pms.member.gateway.entity.request.room.SearchRoomTypeListRequest;
import com.ly.titc.pms.member.gateway.entity.response.room.RoomTypeInfoResponse;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.RoomTypeDecorator;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 房型管理
 *
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-12
 */

@RestController
@RequestMapping("/roomType")
public class RoomTypeController {
    @Resource(type = HotelDecorator.class)
    private HotelDecorator hotelDecorator;
    @Resource(type = RoomTypeDecorator.class)
    private RoomTypeDecorator roomTypeDecorator;
    @Resource(type = RoomTypeConverter.class)
    private RoomTypeConverter roomTypeConverter;

    @PostMapping("/search/list")
    public Response<List<RoomTypeInfoResponse>> searchRoomTypeList(@RequestBody @Valid SearchRoomTypeListRequest request){
        List<Long> hotelVids = new ArrayList<>();
        UserInfoDto user = UserInfoThreadHolder.getUser();
        String blocCode = user.getBlocCode();
        if(!StringUtils.isEmpty(request.getHotelFuzzyNameOrCode())){
            List<PageHotelsResp> pageHotels = hotelDecorator.selectHotelsByFuzzy(
                    blocCode,request.getHotelFuzzyNameOrCode(),request.getState(), UserThreadHolder.getTrackingId());
            if(!CollectionUtils.isEmpty(pageHotels)){
                hotelVids = pageHotels.stream().map(PageHotelsResp::getHotelVid).distinct().collect(Collectors.toList());
            }
        }
        List<RoomTypeBaseInfoResp> roomTypeBaseInfoResps =
                roomTypeDecorator.selectRoomTypesByFuzzy(blocCode, hotelVids, request.getHotelFuzzyNameOrCode(), request.getState());
        return Response.success(roomTypeConverter.convertRoomTypeInfoResponses(roomTypeBaseInfoResps));
    }

}
