package com.ly.titc.pms.member.gateway;


import com.alibaba.fastjson.serializer.SerializerFeature;
import com.alibaba.fastjson.support.config.FastJsonConfig;
import com.alibaba.fastjson.support.spring.FastJsonHttpMessageConverter;
import com.github.lianjiatech.retrofit.spring.boot.annotation.RetrofitScan;
import com.ly.titc.common.config.WebCommonConfig;
import com.ly.titc.common.jackson.JacksonConfig;
import com.ly.titc.common.mybatis.MyBatisPlusConfig;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.http.HttpMessageConverters;
import org.springframework.context.annotation.*;
import org.springframework.http.converter.HttpMessageConverter;

/**
 * <AUTHOR>
 * @ClassName: GatewayApplication
 * @Description: 启动类
 * @date 2019年5月13日
 */
@SpringBootApplication
@ComponentScan(basePackages = {
        "com.ly.titc.pms.member",
        "com.ly.titc.springboot",
        "com.ly.titc.oauth.client",
        "com.ly.titc.cc"}, excludeFilters = {@ComponentScan.Filter(type = FilterType.REGEX, pattern = "com.ly.titc.cc.api.*")})
@RetrofitScan("com.ly.titc.pms.member.facade")
@PropertySource("classpath:dsf_application.properties")
@Import({JacksonConfig.class})
public class GatewayApplication {

    /**
     * run
     *
     * @param args
     */
    public static void main(String[] args) {
        SpringApplication.run(GatewayApplication.class, args);
    }

    /**
     * fastjson converter
     *
     * @return
     */
    @Bean
    public HttpMessageConverters fastJsonHttpMessageConverters() {
        // 1.定义一个converters转换消息的对象
        FastJsonHttpMessageConverter fastConverter = new FastJsonHttpMessageConverter();
        // 2.添加fastjson的配置信息，比如: 是否需要格式化返回的json数据
        FastJsonConfig fastJsonConfig = new FastJsonConfig();
        fastJsonConfig.setSerializerFeatures(
                SerializerFeature.WriteMapNullValue,
                SerializerFeature.DisableCircularReferenceDetect,
                SerializerFeature.WriteNullStringAsEmpty,
                SerializerFeature.WriteNullListAsEmpty,
                SerializerFeature.WriteNullNumberAsZero);
        // 3.在converter中添加配置信息
        fastConverter.setFastJsonConfig(fastJsonConfig);
        // 4.将converter赋值给HttpMessageConverter
        HttpMessageConverter<?> converter = fastConverter;
        // 5.返回HttpMessageConverters对象
        return new HttpMessageConverters(converter);
    }
}
