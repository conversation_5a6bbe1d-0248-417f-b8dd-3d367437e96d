package com.ly.titc.pms.member.gateway.converter;

import com.alibaba.fastjson.JSON;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberRelatedConfigResp;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.com.enums.GenderEnum;
import com.ly.titc.pms.member.com.enums.IdTypeEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberContactInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberExtendInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.HotelTypeEnum;
import com.ly.titc.pms.member.gateway.entity.request.member.*;
import com.ly.titc.pms.member.gateway.entity.response.MemberRelatedConfigResponse;
import com.ly.titc.pms.member.gateway.entity.response.RegisterMemberResultResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberCardInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberContactResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberResponse;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.CheckWhetherBlacklistDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberConverter
 * @Date：2024-11-18 17:26
 * @Filename：MemberConverter
 */
@Mapper(componentModel = "spring")
public interface MemberConverter extends BaseConverter {

    @Mappings({
            @Mapping(target = "masterType", source = "masterType"),
            @Mapping(target = "masterCode", source = "masterCode"),
    })
    PageMemberParamDto convertPageMemberParamBO(PageMemberRequest request, Integer masterType, String masterCode);

    List<MemberResponse> convertMemberResponseList(List<MemberDetailDto> dtoList);

    default List<MemberResponse> convertMemberResponseList(List<MemberDetailDto> dtoList,
                                                           List<MemberCardConfigResp> cardConfigList,
                                                           Map<String, HotelBaseInfoResp> hotelMap,
                                                           Map<String, Integer> checkInMap) {
        List<MemberResponse> memberResponseList = convertMemberResponseList(dtoList);
        if (CollectionUtils.isEmpty(memberResponseList)) {
            return memberResponseList;
        }
        Map<Long, MemberCardConfigResp> cardConfigMap = cardConfigList.stream().collect(Collectors.toMap(MemberCardConfigResp::getId, item -> item));
        for (MemberResponse memberResponse : memberResponseList) {
            memberResponse.setIdTypeStr(IdTypeEnum.getNameByType(memberResponse.getIdType()));
            memberResponse.setGenderStr(GenderEnum.getNameByType(memberResponse.getGender()));
            memberResponse.setCheckInCount(checkInMap.getOrDefault(memberResponse.getMemberNo(), 0));
            for (MemberCardInfoResponse memberCardInfo : memberResponse.getMemberCardInfos()) {
                MemberCardConfigResp memberCardConfigResp = cardConfigMap.get(memberCardInfo.getCardId());
                if (Objects.nonNull(memberCardConfigResp) && CollectionUtils.isNotEmpty(memberCardConfigResp.getCardLevelConfigs())) {
                    Map<Integer, MemberCardLevelConfigInfoResp> cardLevelMap = memberCardConfigResp.getCardLevelConfigs().stream()
                            .collect(Collectors.toMap(MemberCardLevelConfigInfoResp::getCardLevel, Function.identity()));
                    memberCardInfo.setCardName(memberCardConfigResp.getCardName());
                    MemberCardLevelConfigInfoResp memberCardLevelConfigInfo = cardLevelMap.get(memberCardInfo.getCardLevel());
                    if (memberCardLevelConfigInfo != null) {
                        memberCardInfo.setCardLevelName(memberCardLevelConfigInfo.getCardLevelName());
                        memberCardInfo.setValidPeriod(memberCardLevelConfigInfo.getValidPeriod());
                    }
                }
            }
            if (memberResponse.getRegisterHotelType().equals(HotelTypeEnum.HOTEL.getType())) {
                if (Objects.nonNull(hotelMap.get(memberResponse.getRegisterHotel()))) {
                    memberResponse.setRegisterHotelName(hotelMap.get(memberResponse.getRegisterHotel()).getHotelName());
                } else {
                    memberResponse.setRegisterHotelName("");
                }
            } else {
                memberResponse.setRegisterHotelName("集团");
            }
            for (MemberCardInfoResponse memberCardInfo : memberResponse.getMemberCardInfos()) {
                if (memberCardInfo.getMasterType().equals(MasterTypeEnum.BLOC.getType())) {
                    memberCardInfo.setMasterName("集团");
                } else {
                    if (Objects.nonNull(hotelMap.get(memberCardInfo.getMasterCode()))) {
                        memberCardInfo.setMasterName(hotelMap.get(memberCardInfo.getMasterCode()).getHotelName());
                    } else {
                        memberCardInfo.setMasterName("");
                    }
                }
            }
        }
        return memberResponseList;
    }

    RegisterMemberDto convertRegisterMemberDto(MemberRegisterRequest request);

    @Mappings({
            @Mapping(target = "memberContactInfo", source = "memberContactInfo")
    })
    MemberResponse convertMemberResponse(MemberDetailDto dto);

    MemberContactResponse convertMemberResponse(MemberContactInfoDto dto);

    MemberCardInfoResponse convertMemberResponse(MemberCardInfoDto dto);

    default UpdateMemberInfoDto convertUpdateMemberInfoDto(SaveMemberRequest request) {
        UpdateMemberInfoDto dto = new UpdateMemberInfoDto();
        dto.setMemberInfo(convertMemberInfoDto(request.getMemberInfo(), request.getOperator()));
        dto.setMemberExtendInfo(convertMemberExtendInfo(request.getMemberExtendInfo(), request.getOperator()));
        dto.setMemberContactInfo(convertMemberContactInfo(request.getMemberContactInfo(), request.getOperator()));
        return dto;
    }

    @Mapping(target = "modifyUser", source = "operator")
    MemberInfo convertMemberInfoDto(MemberInfoRequest request, String operator);

    MemberExtendInfo convertMemberExtendInfo(MemberExtendInfoRequest request, String operator);

    MemberContactInfo convertMemberContactInfo(MemberContactInfoRequest request, String operator);

    @Mapping(target = "modifyUser", source = "operator")
    MemberCardInfoDto convertMemberCardInfoDto(MemberCardInfoRequest request, String operator);

    RegisterMemberResultResponse convertRegisterMemberResultResponse(RegisterMemberResultDto dto);

    default MemberRelatedConfigResponse convertMemberRegisterCheckConfigResponse(MemberRelatedConfigResp resp) {
        if (resp == null) {
            return null;
        }

        MemberRelatedConfigResponse memberRegisterCheckConfigResponse = new MemberRelatedConfigResponse();

        if (resp.getId() != null) {
            memberRegisterCheckConfigResponse.setId(String.valueOf(resp.getId()));
        }
        memberRegisterCheckConfigResponse.setMasterType(resp.getMasterType());
        memberRegisterCheckConfigResponse.setMasterCode(resp.getMasterCode());
        memberRegisterCheckConfigResponse.setParams(JSON.parseArray(resp.getContent(), String.class));
        if (StringUtils.isNotEmpty(resp.getContent())) {
            memberRegisterCheckConfigResponse.setType(resp.getType());
        }
        memberRegisterCheckConfigResponse.setCreateUser(resp.getCreateUser());
        memberRegisterCheckConfigResponse.setModifyUser(resp.getModifyUser());
        memberRegisterCheckConfigResponse.setGmtCreate(localDatetime2String(resp.getGmtCreate()));
        memberRegisterCheckConfigResponse.setGmtModified(localDatetime2String(resp.getGmtModified()));

        return memberRegisterCheckConfigResponse;
    }

    MemberResponse convertMemberResponse(MemberInfoDto dto);

}
