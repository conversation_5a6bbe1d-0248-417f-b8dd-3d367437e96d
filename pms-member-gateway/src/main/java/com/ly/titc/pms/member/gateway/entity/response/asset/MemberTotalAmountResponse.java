package com.ly.titc.pms.member.gateway.entity.response.asset;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:28
 */
@Data
@Accessors(chain = true)
public class MemberTotalAmountResponse {

    /**
     *  会员总储值（本金余额+赠送余额）
     */
    private BigDecimal totalAmount;

    /**
     * 累计充值储值本金
     */
    private BigDecimal totalCapitalAmount;

    /**
     * 累计充值礼金
     */
    private BigDecimal totalGiftAmount;


    /**
     * 总余额（本金余额+赠送余额）
     */
    private BigDecimal totalBalance;

    /**
     * 总本金余额
     */
    private BigDecimal totalCapitalBalance;

    /**
     * 总赠送余额
     */
    private BigDecimal totalGiftBalance;


    /**
     * 总过期礼金金额
     */
    private BigDecimal totalExpireGiftAmount;

    /**
     *  总使用储值（本金余额+赠送余额）
     */
    private BigDecimal totalUsedAmount;

    /**
     * 总使用本金
     */
    private BigDecimal totalUsedCapitalAmount;

    /**
     * 总使用礼金金额
     */
    private BigDecimal totalUsedGiftAmount;

    /**
     *  总冻结储值（本金余额+赠送余额）
     */
    private BigDecimal totalFreezeAmount;

    /**
     * 总冻结本金
     */
    private BigDecimal totalFreezeCapitalAmount;

    /**
     * 总冻结礼金金额
     */
    private BigDecimal totalFreezeGiftAmount;

}
