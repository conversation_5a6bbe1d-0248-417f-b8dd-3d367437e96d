package com.ly.titc.pms.member.gateway.entity.request.member.card;

import com.ly.titc.pms.member.gateway.entity.request.member.DeleteBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：DeleteMemberCardConfigRequest
 * @Date：2024-11-13 15:38
 * @Filename：DeleteMemberCardConfigRequest
 */
@Data
public class DeleteMemberCardConfigRequest extends DeleteBaseRequest {

    /**
     * 卡id
     */
    @NotBlank(message = "卡id不能为空")
    private String cardId;

    /**
     * 卡名称
     */
    @NotBlank(message = "卡名称不能为空")
    private String cardName;
}
