package com.ly.titc.pms.member.gateway.entity.request.customer;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.common.annotation.LegalNormalDate;
import com.ly.titc.common.annotation.LegalPhoneNumber;
import com.ly.titc.pms.customer.dubbo.enums.GenderEnum;
import com.ly.titc.pms.customer.dubbo.enums.IdTypeEnum;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 客户注册
 *
 * <AUTHOR>
 * @date 2024/12/13 13:57
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class RegisterCustomerRequest extends BaseRequest {

    /**
     * 姓名
     */
    @NotBlank(message = "姓名不能为空")
    private String realName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @LegalPhoneNumber(message = "客户手机号不合法")
    private String mobile;

    /**
     * 证件号分类
     */
    @LegalEnum(target = IdTypeEnum.class, methodName = "getType", message = "证件类型不合法")
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 性别 1:男;2:女;3:保密
     */
    @LegalEnum(target = GenderEnum.class, methodName = "getType", message = "性别不合法")
    private Integer gender;

    /**
     * 生日
     */
    @LegalNormalDate(message = "生日日期不合法[pattern:yyyy-MM-dd]")
    private String birthday;

    /**
     * 当前在住房间
     */
    private String roomNo;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 民族
     */
    private String nation;

    /**
     * 车牌号
     */
    private String numberPlate;

    /**
     * 协议单位编号
     */
    private String agreementNo;

    /**
     * 备注
     */
    private String remark;

    /**
     * 语言
     */
    private String language;

    /**
     * 邮箱
     */
    private String email;

    /**
     * QQ号
     */
    private String qq;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 国家id
     */
    private Integer countryId;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 区域id
     */
    private Integer districtId;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

}
