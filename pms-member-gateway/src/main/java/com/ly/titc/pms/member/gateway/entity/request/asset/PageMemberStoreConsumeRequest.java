package com.ly.titc.pms.member.gateway.entity.request.asset;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;

/**
 * @Author：rui
 * @name：PageMemberStoreConsumeRequest
 * @Date：2024-12-9 16:13
 * @Filename：PageMemberStoreConsumeRequest
 */
@Data
public class PageMemberStoreConsumeRequest extends PageBaseRequest {

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 消费类型：pay:消费，freeze冻结, refund 退款
     */
    private String consumeType;

    /**
     * 渠道
     */
    private String platformChannel;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

    /**
     * 订单状态 1 未支付 2 支付成功  3 已退款
     */
    private Integer state;

}
