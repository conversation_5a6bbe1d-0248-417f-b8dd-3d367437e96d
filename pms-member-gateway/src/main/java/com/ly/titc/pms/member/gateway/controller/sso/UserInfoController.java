package com.ly.titc.pms.member.gateway.controller.sso;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.gateway.converter.UserInfoConverter;
import com.ly.titc.pms.member.gateway.entity.response.sso.UserInfoResponse;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 用户信息
 * @Author: <PERSON><PERSON><PERSON>
 * @CreateTime: 2020-09-10 11:05
 * @Description:
 */

@Slf4j
@RestController
@RequestMapping(value = "/userInfo")
public class UserInfoController {

    @Resource
    private UserInfoConverter userInfoConverter;

    /**
     * sso2.0 获取当前登录用户信息
     *
     * @return
     */
    @PostMapping("/newLogin/user")
    public Response<UserInfoResponse> getNewLoginUserInfo() {
        UserInfoDto user = UserInfoThreadHolder.getUser();
        if (null == user) {
            throw new ServiceException(RespCodeEnum.MEMBER_10031);
        }
        return Response.success(userInfoConverter.convertUserInfoDto2UserInfoResponse(user));
    }
}