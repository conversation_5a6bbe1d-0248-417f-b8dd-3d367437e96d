package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-18 11:31
 */
@Data
@Accessors(chain = true)
public class PageActivityRequest extends PageBaseRequest {

    /**
     * 活动名称
     */
    private String name;

    /**
     * 活动状态  un_start  未开始  ongoing 进行中  end 结束 closed 关闭
     */
    private String state;

    /**
     * 活动类型 member_card_sale  售卡活动  member_paid_upgrade  升级活动
     */
    private String displayType;
}
