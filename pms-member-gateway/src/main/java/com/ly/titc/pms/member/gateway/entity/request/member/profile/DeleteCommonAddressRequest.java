package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：DeleteAddressRequest
 * @Date：2024-11-18 14:55
 * @Filename：DeleteAddressRequest
 */
@Data
public class DeleteCommonAddressRequest extends BaseRequest {

    /**
     * 地址
     */
    @NotBlank(message = "地址不能为空")
    private String address;

    /**
     * 地址编号
     */
    @NotBlank(message = "地址编号不能为空")
    private String addressNo;

    /**
     * 会员编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String memberNo;
}
