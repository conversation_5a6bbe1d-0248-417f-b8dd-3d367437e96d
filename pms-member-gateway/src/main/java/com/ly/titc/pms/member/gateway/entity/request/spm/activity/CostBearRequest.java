package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.spm.dubbo.enums.CostBearTypeEnum;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-13 14:20
 */
@Data
public class CostBearRequest {

    /**
     * 成本承担类型
     *  0,"集团"  1,"门店"  2,"固定比例多方承担"  3,"收款方承担"
     */

    @LegalEnum(target = CostBearTypeEnum.class, message = "成本承担类型不合法",methodName = "getCode")
    private Integer costBearType;

    /**
     * 承担比例
     */
    private BearRatioRequest bearRatioDto;
}
