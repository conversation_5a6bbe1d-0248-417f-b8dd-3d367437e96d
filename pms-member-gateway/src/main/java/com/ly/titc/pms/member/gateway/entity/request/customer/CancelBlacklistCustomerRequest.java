package com.ly.titc.pms.member.gateway.entity.request.customer;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 取消黑名单
 *
 * <AUTHOR>
 * @date 2024/12/13 15:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class CancelBlacklistCustomerRequest extends BaseRequest {

    /**
     * 酒店编码
     */
    @NotBlank(message = "酒店编码不能为空")
    private String hotelCode;

    /**
     * 客户编号
     */
    @NotBlank(message = "客户编号不能为空")
    private String customerNo;

    /**
     * 黑名单编号
     */
    @NotBlank(message = "黑名单编号不能为空")
    private String blacklistNo;

}
