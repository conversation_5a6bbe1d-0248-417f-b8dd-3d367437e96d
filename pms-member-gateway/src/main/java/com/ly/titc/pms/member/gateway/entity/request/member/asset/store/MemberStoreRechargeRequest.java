package com.ly.titc.pms.member.gateway.entity.request.member.asset.store;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-20 13:47
 */
@Data
@Accessors(chain = true)
public class MemberStoreRechargeRequest {


    /**
     * 本金金额
     */
    @NotNull(message = "本金金额不能为空")
    private BigDecimal capitalAmount;

    /**
     * 礼金金额
     */
    @NotNull(message = "礼金金额不能为空")
    private BigDecimal giftAmount;

    /**
     * 礼金过期时间 yyyy-MM-dd
     * 礼金有值时，该值不能为空
     */

    private String giftExpireDate;


}
