package com.ly.titc.pms.member.gateway.entity.response.spm.activity;

import com.ly.titc.pms.spm.dubbo.entity.dto.activity.common.MemberActivityCommonDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 会员积分活动
 *
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-17 20:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class MemberPointsActivityDetailResponse extends MemberActivityCommonResponse {

    /**
     * 消费金额
     */
    private Integer exchangeAmount;
    /**
     * 兑换积分
     */
    private Integer exchangePoint;

    /**
     * 发放节点
     */
    private List<String> issueNodes;

}
