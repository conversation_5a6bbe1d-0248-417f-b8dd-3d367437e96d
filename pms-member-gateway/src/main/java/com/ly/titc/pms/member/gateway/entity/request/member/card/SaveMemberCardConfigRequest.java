package com.ly.titc.pms.member.gateway.entity.request.member.card;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.ArrayList;
import java.util.List;

/**
 * @Author：rui
 * @name：SaveMemberCardConfigRequest
 * @Date：2024-11-12 17:46
 * @Filename：SaveMemberCardConfigRequest
 */
@Data
public class SaveMemberCardConfigRequest extends BaseRequest {


    /**
     * id 新增时不用传值，修改时必传
     */
    private String id;

    /**
     * 卡类型 1: 基础卡 2 企业卡
     */
    @NotNull(message = "会员卡类型必填")
    private Integer cardType;

    /**
     * 卡名称
     */
    @NotBlank(message = "会员卡名称必填")
    private String cardName;


    /**
     * 0 否 1是
     */
    @NotNull(message = "默认卡必填")
    private Integer isDefault;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    @NotNull(message = "排序值不可为空")
    @Max(value = 9999, message = "排序值最大为9999")
    @Min(value = 0, message = "排序值最小为0")
    private Integer sort;

    /**
     * 适用门店 0 部分 1 全部
     */
    private Integer applicationScope;
    /**
     * 适用范围值列表，也就是酒店列表
     */
    private List<String> scopeValues = new ArrayList<>();

    /**
     * 卡号前缀
     */
    private String cardPrefix;

    /**
     * 卡号长度
     */
    @NotNull(message = "卡号长度不可为空")
    @Min(value = 5, message = "卡号长度最小为5")
    @Max(value = 12, message = "卡号长度最大为12")
    private Integer cardLength;

    /**
     * 排除的数字（逗号,分隔）
     */
    private String excludeNumber;

    /**
     * 默认卡ID,二次校验需要修改默认卡时传值
     */
    private String defaultCardId;


}
