package com.ly.titc.pms.member.gateway.controller.spm.activity;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.LocalDateTimeUtil;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.gateway.converter.MemberActivityConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.*;
import com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity.QueryMemberActiveActivityDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.EventActivityDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.MemberActivityDecorator;
import com.ly.titc.pms.member.mediator.service.ActivityMedService;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.ModifyEventActivityReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.PageEventActivitiesReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.QueryEventActivitiesReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.QueryEventActivityReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.member.*;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityListResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.member.*;
import com.ly.titc.pms.spm.dubbo.enums.ActivityDisplayTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 营销活动相关 售卡活动，升级活动 储值活动，积分活动
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-17 19:45
 */
@Slf4j
@RestController
@RequestMapping("/activity")
public class ActivityController {

    @Resource
    private EventActivityDecorator activityDecorator;

    @Resource
    private MemberActivityDecorator memberActivityDecorator;

    @Resource
    private MemberActivityConverter memberActivityConverter;
    @Resource
    private ActivityMedService activityMedService;


    /**
     * 分页查询
     */
    @PostMapping("/page")
    public Response<Pageable<EventActivityListResponse>> page(@RequestBody @Valid PageActivityRequest request) {
        PageEventActivitiesReq req =memberActivityConverter.convertRequestToReq(request);
        if (StringUtils.isNotBlank(request.getDisplayType())) {
            req.setDisplayTypes(Collections.singletonList(request.getDisplayType()));
        } else {
            req.setDisplayTypes(Arrays.asList(ActivityDisplayTypeEnum.MEMBER_CARD_SALE.getCode(), ActivityDisplayTypeEnum.MEMBER_PAID_UPGRADE.getCode()));
        }
        req.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        Pageable<EventActivityListResp> pageable = activityDecorator.pageEventActivities(req);
        return Response.success(PageableUtil.convert(pageable, record -> {
            EventActivityListResponse response = memberActivityConverter.convertRespToResponse(record);
            response.setIsLongTerm(LocalDateTimeUtil.formatByNormalDateTime(record.getEffectiveEndTime()).equals(SystemConstant.PERPETUAL_EFFECT_TIME));
            return response;
        }));
    }

    /**
     * 新增售卡活动
     */
    @PostMapping("/addSaleCardActivity")
    public Response<String> addSaleCardActivity(@RequestBody @Valid SaveSaleCardActivityRequest request) {
        SaveSaleCardActivityReq req = memberActivityConverter.convertSaleCardActivityReq(request);
        String activityCode = memberActivityDecorator.saveSaleCardActivity(req);
        return Response.success(activityCode);
    }

    /**
     * 新增升级活动
     */
    @PostMapping("/addUpgradeActivity")
    public Response<String> addUpgradeActivity(@RequestBody @Valid SaveUpgradeActivityRequest request) {
        SaveUpgradeActivityReq req = memberActivityConverter.convertUpgradeActivityReq(request);
        String activityCode = memberActivityDecorator.saveUpgradeActivity(req);
        return Response.success(activityCode);
    }

    /**
     * 新增储值活动
     *
     * @param request
     * @return
     */
    @PostMapping("/addRechargeActivity")
    public Response<String> addRechargeActivity(@RequestBody @Valid SaveRechargeActivityRequest request) {
        SaveRechargeActivityReq req = memberActivityConverter.convertRechargeActivityReq(request);
        if (request.isPerpetualEffect()) {
            req.setStoreLimit(SystemConstant.GIFT_MONEY_LONG_PERIOD);
        }
        String activityCode = memberActivityDecorator.saveRechargeActivity(req);
        return Response.success(activityCode);
    }

    /**
     * 新增积分活动
     *
     * @param request
     * @return
     */
    @PostMapping("/addPointsActivity")
    public Response<Boolean> addPointsActivity(@RequestBody @Valid SavePointsActivityRequest request) {
        SavePointsActivityReq req = memberActivityConverter.convertRechargeActivityReq(request);
        String activityCode = memberActivityDecorator.savePointsActivity(req);
        return Response.success(activityCode);
    }

    /**
     * 编辑售卡活动
     *
     * @param request
     * @return
     */
    @PostMapping("/saveSaleCardActivity")
    public Response<String> saveSaleCardActivity(@RequestBody @Valid SaveSaleCardActivityRequest request) {
        if (StringUtils.isEmpty(request.getActivityCode())) {
            throw new ServiceException("活动编码不能为空", RespCodeEnum.CODE_400.getCode());
        }
        SaveSaleCardActivityReq req = memberActivityConverter.convertSaleCardActivityReq(request);
        String activityCode = memberActivityDecorator.saveSaleCardActivity(req);
        return Response.success(activityCode);
    }

    /**
     * 编辑升级活动
     *
     * @param request
     * @return
     */
    @PostMapping("/saveUpgradeActivity")
    public Response<String> saveUpgradeActivity(@RequestBody @Valid SaveUpgradeActivityRequest request) {
        if (StringUtils.isEmpty(request.getActivityCode())) {
            throw new ServiceException("活动编码不能为空", RespCodeEnum.CODE_400.getCode());
        }
        SaveUpgradeActivityReq req = memberActivityConverter.convertUpgradeActivityReq(request);
        String activityCode = memberActivityDecorator.saveUpgradeActivity(req);
        return Response.success(activityCode);
    }

    /**
     * 编辑储值活动
     *
     * @param request
     * @return
     */
    @PostMapping("/saveRechargeActivity")
    public Response<String> saveRechargeActivity(@RequestBody @Valid SaveRechargeActivityRequest request) {
        if (StringUtils.isEmpty(request.getActivityCode())) {
            throw new ServiceException("活动编码不能为空", RespCodeEnum.CODE_400.getCode());
        }
        SaveRechargeActivityReq req = memberActivityConverter.convertRechargeActivityReq(request);
        if (request.isPerpetualEffect()) {
            req.setStoreLimit(SystemConstant.GIFT_MONEY_LONG_PERIOD);
        }
        String activityCode = memberActivityDecorator.saveRechargeActivity(req);
        return Response.success(activityCode);
    }

    /**
     * 编辑积分活动
     *
     * @param request
     * @return
     */
    @PostMapping("/savePointsActivity")
    public Response<Boolean> savePointsActivity(@RequestBody @Valid SavePointsActivityRequest request) {
        if (StringUtils.isEmpty(request.getActivityCode())) {
            throw new ServiceException("活动编码不能为空", RespCodeEnum.CODE_400.getCode());
        }
        SavePointsActivityReq req = memberActivityConverter.convertRechargeActivityReq(request);
        String activityCode = memberActivityDecorator.savePointsActivity(req);
        return Response.success(activityCode);
    }

    /**
     * 查看售卡活动详情
     *
     * @param request
     * @return
     */
    @PostMapping("/getSaleCardActivityDetail")
    public Response<MemberSaleCardActivityDetailResponse> getSaleCardActivityDetail(@RequestBody @Valid QueryEventActivityRequest request) {
        GetMemberActivityReq req = memberActivityConverter.convertGetMemberActivityReq(request);
        MemberCardSaleActivityDetailResp activity = memberActivityDecorator.getSaleCardActivity(req);
        if (activity != null) {
            MemberSaleCardActivityDetailResponse activityDetail = memberActivityConverter.convertSaleCardActivityResp(activity);
            return Response.success(activityDetail);
        }
        return Response.success(null);
    }

    /**
     * 查询集团下可售卡
     *
     * @param request
     * @return
     */
    @PostMapping("/listSellableCard")
    public Response<List<MemberSellableCardResponse>> listSellableCard(@RequestBody @Valid QuerySellableCardRequest request){

        QuerySellableCardReq querySellableCardReq = new QuerySellableCardReq();
        querySellableCardReq.setBlocCode(request.getBlocCode());
        querySellableCardReq.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        querySellableCardReq.setScopeSource(ScopeSourceEnum.BLOC.getCode());
        querySellableCardReq.setMemberNo(request.getMemberNo());
        List<MemberSellableCardResp> cardList = memberActivityDecorator.listSellableCard(querySellableCardReq);
        return Response.success(cardList);
    }

    /**
     * 查询命中的售卡活动
     *
     * @param request
     * @return
     */
    @PostMapping("/getSaleCardActiveActivityDetail")
    public Response<MemberSaleCardActivityDetailResponse> getSaleCardActiveActivityDetail(@RequestBody @Valid QueryMemberActiveActivityRequest request) {
        QueryMemberActiveActivityDto dto = memberActivityConverter.convertQueryDto(request);
        dto.setEventType(ActivityDisplayTypeEnum.MEMBER_CARD_SALE.getCode());
        EventActivityResp activeActivity= activityMedService.getHighestPriorityEventActivity(dto);
        if (activeActivity != null) {
            GetMemberActivityReq req = buildGetMemberActivityReq(request, activeActivity);
            MemberCardSaleActivityDetailResp activity = memberActivityDecorator.getSaleCardActivity(req);
            MemberSaleCardActivityDetailResponse activityDetail = memberActivityConverter.convertSaleCardActivityResp(activity);
            return Response.success(activityDetail);
        }
        throw new ServiceException(RespCodeEnum.MEMBER_10049);
    }

    /**
     * 查看升级活动详情
     *
     * @param request
     * @return
     */
    @PostMapping("/getUpgradeActivityDetail")
    public Response<MemberUpgradeActivityDetailResponse> getUpgradeActivityDetail(@RequestBody @Valid QueryEventActivityRequest request) {
        GetMemberActivityReq req = memberActivityConverter.convertGetMemberActivityReq(request);
        MemberUpgradeActivityDetailResp activity = memberActivityDecorator.getUpgradeActivity(req);
        if (activity != null) {
            MemberUpgradeActivityDetailResponse activityDetail = memberActivityConverter.convertUpgradeActivityResp(activity);
            return Response.success(activityDetail);
        }
        throw new ServiceException(RespCodeEnum.MEMBER_10049);
    }

    /**
     * 查询命中的升级活动详情
     *
     * @param request
     * @return
     */
    @PostMapping("/getUpgradeActiveActivityDetail")
    public Response<MemberUpgradeActivityDetailResponse> getUpgradeActiveActivityDetail(@RequestBody @Valid QueryMemberActiveActivityRequest request) {
        QueryMemberActiveActivityDto dto = memberActivityConverter.convertQueryDto(request);
        dto.setEventType(ActivityDisplayTypeEnum.MEMBER_PAID_UPGRADE.getCode());
        EventActivityResp activeActivity= activityMedService.getHighestPriorityEventActivity(dto);
        if (activeActivity != null) {
            GetMemberActivityReq req = buildGetMemberActivityReq(request, activeActivity);
            MemberUpgradeActivityDetailResp activity = memberActivityDecorator.getUpgradeActivity(req);
            MemberUpgradeActivityDetailResponse activityDetail = memberActivityConverter.convertUpgradeActivityResp(activity);
            return Response.success(activityDetail);
        }
        return Response.success(null);
    }

    /**
     * 查询储值充值活动详情
     *
     * @param request
     * @return
     */
    @PostMapping("/getRechargeActivityDetail")
    public Response<MemberRechargeActivityDetailResponse> getRechargeActivityDetail(@RequestBody @Valid QueryEventActivityRequest request){
        GetMemberActivityReq req = memberActivityConverter.convertGetMemberActivityReq(request);
        MemberRechargeActivityDetailResp activity = memberActivityDecorator.getRechargeActivity(req);
        if (activity != null) {
            MemberRechargeActivityDetailResponse activityDetail = memberActivityConverter.convertRechargeActivityResp(activity);
            return Response.success(activityDetail);
        }
        return Response.success(null);
    }

    /**
     * 查询命中的储值充值活动详情
     *
     * @param request
     * @return
     */
    @PostMapping("/getRechargeActiveActivityDetail")
    public Response<MemberRechargeActivityDetailResponse> getRechargeActiveActivityDetail(@RequestBody @Valid QueryMemberActiveActivityRequest request) {
        QueryMemberActiveActivityDto dto = memberActivityConverter.convertQueryDto(request);
        if(StringUtils.isEmpty(request.getMemberNo())){
            throw new ServiceException("会员号不能为空", RespCodeEnum.CODE_400.getCode());
        }
        dto.setEventType(ActivityDisplayTypeEnum.STORE_GIFT.getCode());
        //1.查询会员的卡和等级信息
        //2.查询会员的标签信息
        EventActivityResp activeActivity= activityMedService.getHighestPriorityEventActivity(dto);
        if (activeActivity != null) {
            GetMemberActivityReq req = buildGetMemberActivityReq(request, activeActivity);
            MemberRechargeActivityDetailResp activity = memberActivityDecorator.getRechargeActivity(req);
            MemberRechargeActivityDetailResponse activityDetail = memberActivityConverter.convertRechargeActivityResp(activity);
            return Response.success(activityDetail);
        }
        return Response.success(null);
    }

    /**
     * 查询积分活动详情
     *
     * @param request
     * @return
     */
    @PostMapping("/getPointsActivityDetail")
    public Response<MemberPointsActivityDetailResponse> getPointsActivityDetail(@RequestBody @Valid QueryEventActivityRequest request){
        GetMemberActivityReq req = memberActivityConverter.convertGetMemberActivityReq(request);
        MemberPointsActivityDetailResp activity = memberActivityDecorator.getPointsActivity(req);
        if (activity != null) {
            MemberPointsActivityDetailResponse activityDetail = memberActivityConverter.convertPointsActivityResp(activity);
            return Response.success(activityDetail);
        }
        return Response.success(null);
    }

    /**
     * 查询命中的积分活动详情
     *
     * @param request
     * @return
     */
    @PostMapping("/getPointsActiveActivity")
    public Response<MemberUpgradeActivityDetailResponse> getPointsActiveActivity(@RequestBody @Valid QueryMemberActiveActivityRequest request) {
        QueryMemberActiveActivityDto dto = memberActivityConverter.convertQueryDto(request);
        if(StringUtils.isEmpty(request.getMemberNo())){
            throw new ServiceException("会员号不能为空", RespCodeEnum.CODE_400.getCode());
        }
        dto.setEventType(ActivityDisplayTypeEnum.STORE_GIFT.getCode());
        //1.查询会员的卡和等级信息
        //2.查询会员的标签信息
        EventActivityResp activeActivity= activityMedService.getHighestPriorityEventActivity(dto);
        if (activeActivity != null) {
            GetMemberActivityReq req = buildGetMemberActivityReq(request, activeActivity);
            MemberPointsActivityDetailResp activity = memberActivityDecorator.getPointsActivity(req);
            MemberPointsActivityDetailResponse activityDetail = memberActivityConverter.convertPointsActivityResp(activity);
            return Response.success(activityDetail);
        }
        return Response.success(null);
    }


    /**
     * 停用
     */
    @PostMapping("/disable")
    public Response<Boolean> disableEventActivity(@RequestBody @Valid QueryEventActivityRequest request){
        ModifyEventActivityReq req = memberActivityConverter.convertRequestToReq(request);
        req.setUniqueCheck(false);
        activityDecorator.disableEventActivity(req);
        return Response.success(Boolean.TRUE);
    }


    /**
     * 启用
     */
    @PostMapping("/enable")
    public Response<Boolean> enableEventActivity(@RequestBody @Valid QueryEventActivityRequest request){
        ModifyEventActivityReq req = memberActivityConverter.convertRequestToReq(request);
        req.setUniqueCheck(false);
        activityDecorator.enableEventActivity(req);
        return Response.success(Boolean.TRUE);
    }

    /**
     * 构建查询对象
     *
     * @param request
     * @param activeActivity
     * @return
     */
    private static GetMemberActivityReq buildGetMemberActivityReq(QueryMemberActiveActivityRequest request, EventActivityResp activeActivity) {
        GetMemberActivityReq req = new GetMemberActivityReq();
        req.setBlocCode(request.getBlocCode());
        req.setActivityCode(activeActivity.getActivityCode());
        req.setTrackingId(request.getTrackingId());
        return req;
    }

}
