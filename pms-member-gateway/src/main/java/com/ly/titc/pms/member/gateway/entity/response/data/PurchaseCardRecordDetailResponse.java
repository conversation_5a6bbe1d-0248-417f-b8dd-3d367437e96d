package com.ly.titc.pms.member.gateway.entity.response.data;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

import java.sql.Timestamp;

/**
 * @Author：rui
 * @name：PurchaseCardRecordDetailDto
 * @Date：2024-12-12 16:17
 * @Filename：PurchaseCardRecordDetailDto
 */
@Data
public class PurchaseCardRecordDetailResponse {

    /**
     * 会员订单号
     */
    @PrimaryKey(column = "member_order_no", value = 1)
    private String memberOrderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 集团组code
     */
    private String clubCode;

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 会员业务场景 register 注册 purchaseCard 购卡,recharge 充值
     */
    private String memberScene;

    /**
     * 业务请求体
     */
    private String memberSceneNote;

    /**
     * 活动code
     */
    private String activityCode;

    /**
     * 活动名称
     */
    private String activityName;

    /**
     * 活动成本
     */
    private String activityCost;

    /**
     * 礼包信息JSON
     */
    private String giftPack;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private Timestamp gmtCreate;

    /**
     * 修改时间
     */
    private Timestamp gmtModified;
}
