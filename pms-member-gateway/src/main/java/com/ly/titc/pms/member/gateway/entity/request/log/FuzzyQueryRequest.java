package com.ly.titc.pms.member.gateway.entity.request.log;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：FuzzyQueryRequest
 * @Date：2023-11-21 17:41
 * @Filename：FuzzyQueryRequest
 */
@Data
public class FuzzyQueryRequest extends BaseRequest {
    /**
     * 模块id
     */
    @NotNull(message = "模块id不能为空")
    private Integer moduleId;

    /**
     * 操作对象名称
     */
    private String name;
}
