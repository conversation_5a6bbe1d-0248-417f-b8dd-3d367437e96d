package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

/**
 * @Author：rui
 * @name：SaveAddressRequest
 * @Date：2024-11-18 14:54
 * @Filename：SaveAddressRequest
 */
@Data
public class SaveCommonAddressRequest extends BaseRequest {

    /**
     * 常用地址编号
     */
    private String addressNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 国家id
     */
    private Long countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 省id
     */
    private Long provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市id
     */
    private Long cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域id
     */
    private Long districtId;

    /**
     * 区域
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

    /**
     * 标签
     */
    private String tag;

    /**
     * 排序
     */
    private Integer sort;
}
