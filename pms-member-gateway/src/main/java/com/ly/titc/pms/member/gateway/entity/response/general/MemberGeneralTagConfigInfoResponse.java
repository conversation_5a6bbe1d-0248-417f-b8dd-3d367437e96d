package com.ly.titc.pms.member.gateway.entity.response.general;

import com.ly.titc.pms.member.gateway.entity.response.member.MemberTagConfigInfoResponse;
import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberGeneralTagConfigInfoResponse
 * @Date：2024-12-17 15:02
 * @Filename：MemberGeneralTagConfigInfoResponse
 */
@Data
public class MemberGeneralTagConfigInfoResponse {

    private Integer type;

    private String name;

    private List<MemberTagConfigInfoResponse> memberTags;
}
