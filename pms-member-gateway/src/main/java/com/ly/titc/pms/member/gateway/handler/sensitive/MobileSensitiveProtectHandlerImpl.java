package com.ly.titc.pms.member.gateway.handler.sensitive;

import com.ly.titc.pms.member.gateway.entity.enums.SensitiveFieldTypeEnum;
import org.springframework.stereotype.Component;

/**
 * 手机号保护
 *
 * <AUTHOR>
 * @date 2024/12/4 14:50
 */
@Component
public class MobileSensitiveProtectHandlerImpl extends SensitiveProtectHandler{

    @Override
    public String fieldType() {
        return SensitiveFieldTypeEnum.MOBILE.name();
    }

    @Override
    public String handle(String phoneNumber) {
        if (phoneNumber == null || !phoneNumber.matches("\\d{11}")) {
            // 如果手机号不符合11位数字的格式，则直接返回原始输入
            return phoneNumber;
        }
        return phoneNumber.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
    }
}
