package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

/**
 * @Author：rui
 * @name：SaveCommonOccupantRequest
 * @Date：2024-11-18 15:15
 * @Filename：SaveCommonOccupantRequest
 */
@Data
public class SaveCommonOccupantRequest extends BaseRequest {

    /**
     * 入住人编号,编辑时必传
     */
    private String occupantsNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 姓名
     */
    private String name;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 0:身份证;1:护照;2:学生证;3:军官证;4:回乡证;5:台胞证;6:港澳通行证;7:国际海员;8:外国人永久居留（身份）证;9:其他证件;10:警官证;11:士兵证;12:台湾通行证;13:入台证;14:户口薄;15:出生证明;16:中国驾照;17:港澳居民居住证;18:台湾居民居住证
     */
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 标签
     */
    private String tag;

    /**
     * 排序
     */
    private Integer sort;
}
