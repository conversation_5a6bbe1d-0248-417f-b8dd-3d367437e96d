package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetGiftPackListRequest extends PageBaseRequest {
    /**
     * 礼包名称
     */
    private String giftPackName;

    /**
     * 礼包状态 1-已生效 2-已停用 3-已过期
     */
    private Integer state;
}
