package com.ly.titc.pms.member.gateway.helper;

import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @author: shawn
 * @email: <EMAIL>
 * @date: 2024/5/23 14:02
 * @description:
 */
@Slf4j
public class UserPermissionHelper {


    /**
     * 只判断用户酒店权限在不在范围内，不在则报错
     * @param user
     * @param targetHotelCodes
     * @return
     */
    public static List<String> accessHotels(UserInfoDto user, List<String> targetHotelCodes) {
        String blocCode = user.getBlocCode();
        List<String> hotelCodes = user.getHotelCodes();
        if (!user.getIsAdmin() && (CollectionUtils.isEmpty(hotelCodes))) {
            log.warn("门店下拉列表，用户没有门店的访问权限， blocCode:{}, hotelCodes:{}, userId:{}", blocCode, hotelCodes, user.getUserId());
            throw new ServiceException(RespCodeEnum.NO_HOTEL_PERMISSION);
        }
        if (user.getIsAdmin()) {
            return targetHotelCodes;
        }
        if (!CollectionUtils.isEmpty(targetHotelCodes)) {
            if (hotelCodes.containsAll(targetHotelCodes)) {
                return targetHotelCodes;
            } else {
                log.warn("门店下拉列表，用户没有门店的访问权限， blocCode:{},targetHotelCode:{}, userId:{}", blocCode, targetHotelCodes, user.getUserId());
                throw new ServiceException(RespCodeEnum.NO_HOTEL_PERMISSION);
            }
        }
        return new ArrayList<>(hotelCodes);
    }

    /**
     * 只判断用户酒店权限在不在范围内，不在则报错
     * @param user
     * @param targetHotelCode
     * @return
     */
    public static List<String> accessHotels(UserInfoDto user, String targetHotelCode) {
        if (StringUtils.isBlank(targetHotelCode)) {
            return new ArrayList<>(allAccessHotelCodes(user));
        }
        List<String> list = new ArrayList<>();
        list.add(targetHotelCode);
        return accessHotels(user, list);
    }


    /**
     * 只判断用户酒店权限在不在范围内，不在则报错
     * @param user
     * @param targetHotelCode
     * @return
     */
    public static List<Long> accessHotelVids(UserInfoDto user, String targetHotelCode) {
        List<String> hotelCodes = accessHotels(user, targetHotelCode);
        if (CollectionUtils.isEmpty(hotelCodes)) {
            return Collections.emptyList();
        }
        return hotelCodes.stream().map(Long::valueOf).collect(Collectors.toList());
    }


    /**
     * 获取所有有权限的酒店，如果非超管且没有任何酒店权限，直接报错
     * @param user
     * @return
     */
    public static Set<String> allAccessHotelCodes(UserInfoDto user) {
        List<String> hotelVIds = user.getHotelCodes();
        if (!user.getIsAdmin() && CollectionUtils.isEmpty(hotelVIds)) {
            throw new ServiceException(RespCodeEnum.NO_HOTEL_PERMISSION);
        }
        if (user.getIsAdmin()) {
            return new HashSet<>();
        }
        return hotelVIds.stream().map(Object::toString).collect(Collectors.toSet());
    }
}
