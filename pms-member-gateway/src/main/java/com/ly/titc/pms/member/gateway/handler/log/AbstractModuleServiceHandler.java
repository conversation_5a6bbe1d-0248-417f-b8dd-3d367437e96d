package com.ly.titc.pms.member.gateway.handler.log;

import com.alibaba.fastjson.JSONObject;

import java.util.List;

/**
 * @Author：rui
 * @name：AbstractModuleServiceHandler
 * @Date：2023-11-21 17:39
 * @Filename：AbstractModuleServiceHandler
 */
public abstract class AbstractModuleServiceHandler {


    public AbstractModuleServiceHandler() {
        ModuleServiceHandlerManager.putInstance(getModuleId(), this);
    }

    /**
     * 名称处理
     *
     * @param name 名称
     * @return
     */
    public abstract List<String> execute(String blocCode, String hotelCode, String name, String trackingId);

    /**
     * 获取模块id
     *
     * @return
     */
    public abstract Integer getModuleId();



    /**
     * 构建日志字符串
     *
     * @param blocCode
     * @param name
     * @param records
     * @return
     */
    protected static String buildLogStr(String blocCode, String hotelCode, String name, List<String> records){
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", blocCode);
        json.put("hotelCode", hotelCode);
        json.put("records", records);
        return json.toJSONString();
    }

}
