package com.ly.titc.pms.member.gateway.entity.response.spm.giftpack;

import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-22
 */
@Data
public class GiftPackInfoResponse{
    /**
     * 礼包编码
     */
    private String giftPackNo;

    /**
     * 礼包名称
     */
    private String giftPackName;

    /**
     * 礼包金额
     */
    private BigDecimal amount;

    /**
     * 是否允许售卖
     */
    private Integer isAllowSale;

    /**
     * 礼包开始时间
     */
    private String effectStartDate;

    /**
     * 礼包结束时间
     */
    private String effectEndDate;

    /**
     * 礼包描述
     */
    private String content;

    /**
     * 促销信息
     */
    private String promotionContent;

    /**
     * 备注
     */
    private String remark;

    /**
     * 礼包剩余库存 -1-不限
     */
    private Integer remainingQuantity;

    /**
     * 修改时间
     */
    private String gmtModified;

    /**
     * 版本
     */
    private Integer version;

    /**
     * 礼包售卖信息
     */
    private GiftPackSaleInfoResponse saleInfo;

    /**
     * 礼包明细集合
     */
    private List<GiftPackItemInfoResponse> itemInfos;

}
