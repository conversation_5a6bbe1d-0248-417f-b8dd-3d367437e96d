package com.ly.titc.pms.member.gateway.entity.request.member.card;

import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：ActionRuleBaseRequest
 * @Date：2024-11-15 14:21
 * @Filename：ActionRuleBaseRequest
 */
@Data
public class ActionRuleBaseRequest extends ActionBaseRequest {

    /**
     * 规则名称
     */
    @NotBlank(message = "规则名称不能为空")
    private String name;
}
