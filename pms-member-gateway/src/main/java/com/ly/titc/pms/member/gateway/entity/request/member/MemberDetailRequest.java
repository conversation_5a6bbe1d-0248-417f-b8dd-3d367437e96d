package com.ly.titc.pms.member.gateway.entity.request.member;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：MemberDetailRequest
 * @Date：2024-11-18 17:46
 * @Filename：MemberDetailRequest
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberDetailRequest extends BaseRequest {

    @NotBlank(message = "会员号不能为空")
    private String memberNo;
}
