package com.ly.titc.pms.member.gateway.controller.spm.coupon;

import com.ly.titc.cdm.dubbo.entity.response.plan.HotelRatePlanListResp;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.entity.response.area.AreaResp;
import com.ly.titc.mdm.entity.response.brand.SelectBrandResp;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import com.ly.titc.mdm.entity.response.hotel.room.type.RoomTypeBaseInfoResp;
import com.ly.titc.mdm.entity.response.region.RegionResp;
import com.ly.titc.mdm.enums.StateEnum;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.account.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.converter.CouponTemplateConverter;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.ApplicableHotelInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.ApplicableHotelRatePlanInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.ApplicableHotelRoomTypeInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.CommonNameInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.*;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.*;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cdm.RatePlanDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.coupon.CouponTemplateDecorator;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.grant.CouponGrantRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.CouponUseRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableRuleDataInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableRuleInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.template.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateListResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.SaveCouponTemplateInfoResp;
import com.ly.titc.pms.spm.dubbo.enums.ApplicableScopeTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.logging.log4j.util.Strings;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 优惠券模版接口
 *
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Slf4j
@RestController
@RequestMapping("/coupon/template")
public class CouponTemplateController {
    @Resource(type = HotelDecorator.class)
    private HotelDecorator hotelDecorator;
    @Resource(type = BrandDecorator.class)
    private BrandDecorator brandDecorator;
    @Resource(type = AreaDecorator.class)
    private AreaDecorator areaDecorator;
    @Resource(type = RegionDecorator.class)
    private RegionDecorator regionDecorator;
    @Resource(type = RoomTypeDecorator.class)
    private RoomTypeDecorator roomTypeDecorator;
    @Resource(type = RatePlanDecorator.class)
    private RatePlanDecorator ratePlanDecorator;
    @Resource(type = CouponTemplateDecorator.class)
    private CouponTemplateDecorator couponTemplateDecorator;
    @Resource(type = CouponTemplateConverter.class)
    private CouponTemplateConverter couponTemplateConverter;

    /**
     * 优惠券模版列表(分页)
     */
    @PostMapping("/page/list")
    public Response<Pageable<CouponTemplateListResponse>> pageCouponTemplateList(@Valid @RequestBody PageCouponTemplateListRequest request) {
        GetCouponTemplateListReq req = couponTemplateConverter.convertGetCouponTemplateListReq(request);
        Pageable<CouponTemplateListResp> pageable = couponTemplateDecorator.pageCouponTemplateList(req);
        return Response.success(
                Pageable.convert(pageable, couponTemplateConverter.convertCouponTemplateListResponses(pageable.getDatas()))
        );
    }

    /**
     * 优惠券模版列表
     */
    @PostMapping("/list")
    public Response<List<CouponTemplateListResponse>> listCouponTemplate(@Valid @RequestBody ListCouponTemplateRequest request) {
        GetCouponTemplateListReq req = couponTemplateConverter.convertGetCouponTemplateListReqByListRequest(request);
        req.setCurrPage(Constant.ONE);
        req.setPageSize(Integer.MAX_VALUE);
        List<CouponTemplateListResp> responseList = couponTemplateDecorator.listCouponTemplate(req);
        return Response.success(couponTemplateConverter.convertCouponTemplateListResponses(responseList));
    }

    /**
     * 优惠券模版列表数
     */
    @PostMapping("/list/tree")
    public Response<List<CouponTemplateTreeResponse>> listCouponTemplateTree() {
        UserInfoDto user = UserInfoThreadHolder.getUser();
        List<CouponTemplateListResp> responseList = couponTemplateDecorator.defaultListCouponTemplate(user);
        return Response.success(couponTemplateConverter.convertCouponTemplateTreeResponses(responseList));
    }

    /**
     * 查询优惠券模版明细
     */
    @PostMapping("/detail")
    public Response<CouponTemplateInfoResponse> getCouponTemplateDetail(@Valid @RequestBody GetCouponTemplateDetailRequest request) {
        GetCouponTemplateDetailReq req = couponTemplateConverter.convertGetCouponTemplateDetailReq(request);
        CouponTemplateDetailResp resp = couponTemplateDecorator.getCouponTemplateDetail(req);
        return Response.success(this.buildCouponTemplateInfoResponse(resp));
    }

    /**
     * 保存优惠券模版(创建(复制)/更新)
     */
    @PostMapping("/save")
    public Response<SaveCouponTemplateInfoResponse> saveCouponTemplate(@Valid @RequestBody SaveCouponTemplateRequest request) {
        SaveCouponTemplateReq req = couponTemplateConverter.convertSaveCouponTemplateReq(request, UserInfoThreadHolder.getUser());
        SaveCouponTemplateInfoResp resp = couponTemplateDecorator.saveCouponTemplate(req);
        SaveCouponTemplateInfoResponse response = couponTemplateConverter.convertSaveCouponTemplateInfoResponse(resp);
        return Response.success(response);
    }

    /**
     * 模版删除
     */
    @PostMapping("/delete")
    public Response<String> deleteCouponTemplate(@Valid @RequestBody DeleteCouponTemplateRequest request) {
        DeleteCouponTemplateReq req = couponTemplateConverter.convertDeleteCouponTemplateReq(request);
        UserInfoDto user = UserInfoThreadHolder.getUser();
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(user.getBlocCode());
        req.setOperator(user.getUserName());
        couponTemplateDecorator.deleteCouponTemplate(req);
        return Response.success();
    }

    /**
     * 模版作废
     */
    @PostMapping("/repeal")
    public Response<String> repealCouponTemplate(@Valid @RequestBody RepealCouponTemplateRequest request) {
        ChangeCouponTemplateStateReq req = couponTemplateConverter.convertRepealCouponTemplateReq(request);
        UserInfoDto user = UserInfoThreadHolder.getUser();
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(user.getBlocCode());
        req.setOperator(user.getUserName());
        couponTemplateDecorator.repealCouponTemplate(req);
        return Response.success();
    }

    /**
     * 查询适用酒店信息
     */
    @PostMapping("/applicable/hotel/info")
    public Response<ApplicableHotelInfoResponse> getApplicableHotelInfo(@Valid @RequestBody CouponApplicableInfoRequest request) {
        CouponTemplateInfoResponse couponTemplateInfoResponse = this.getCouponTemplateInfoResponse(request);
        CouponUseRuleResponse useRuleInfo = couponTemplateInfoResponse.getUseRuleInfo();
        ApplicableHotelInfoResponse applicableHotelInfoResponse = useRuleInfo.getHotelInfos();
        this.buildApplicableHotelInfoResponse(request.getBlocCode(), applicableHotelInfoResponse);
        return Response.success(applicableHotelInfoResponse);
    }

    /**
     * 查询适用酒店列表
     */
    @PostMapping("/page/applicable/hotel/list")
    public Response<Pageable<CouponApplicableHotelResponse>> pageApplicableHotelList(@Valid @RequestBody PageCouponApplicableHotelRequest request) {
        Set<String> applicableHotelCodes = couponTemplateDecorator.getCouponTemplateApplicableHotelList(
                couponTemplateConverter.convertGetCouponTemplateApplicableHotelListReq(request));
        Set<String> havingPermissionHotelCodes = this.getHavingPermissionHotelCodes(applicableHotelCodes);
        if (CollectionUtils.isEmpty(havingPermissionHotelCodes)) {
            return Response.success(Pageable.empty());
        }
        List<PageHotelsResp> pageHotels = hotelDecorator.selectHotelsByFuzzy(request.getBlocCode(), request.getLkNameOrCode(),
                StateEnum.VALID.getValue(), request.getTrackingId());
        if (!havingPermissionHotelCodes.contains(Strings.EMPTY)) {
            pageHotels = pageHotels.stream().filter(pageHotelsResp ->
                    havingPermissionHotelCodes.contains(pageHotelsResp.getHotelCode())).collect(Collectors.toList());
        }
        List<CouponApplicableHotelResponse> responses = couponTemplateConverter.convertCouponApplicableHotelResponses(pageHotels);
        return Response.success(
                this.buildPageableCouponApplicableList(request.getPageIndex(), request.getPageSize(),responses.size(),responses)
        );
    }


    private Set<String> getHavingPermissionHotelCodes(Set<String> applicableHotelList) {
        if (CollectionUtils.isEmpty(applicableHotelList)) {
            return new HashSet<>();
        }
        UserInfoDto user = UserInfoThreadHolder.getUser();
        if (!user.getIsAdmin()) {
            if (applicableHotelList.contains(Strings.EMPTY)) {
                return new HashSet<>(user.getHotelCodes());
            }
            applicableHotelList.retainAll(user.getHotelCodes());//当前用户权限下适用的酒店code
            return applicableHotelList;
        }
        return applicableHotelList;
    }

    private <T> Pageable<T> buildPageableCouponApplicableList(Integer pageIndex,Integer pageSize,Integer total,
                                                              List<T> applicableDataList) {
        return new Pageable<>(pageIndex, Pageable.getTotalPage(total, pageSize),total,
                applicableDataList.subList(pageIndex - 1, pageSize));
    }

    /**
     * 查询发放酒店信息
     */
    @PostMapping("/grant/hotel/info")
    public Response<ApplicableHotelInfoResponse> getGrantHotelInfo(@Valid @RequestBody CouponApplicableInfoRequest request) {
        CouponTemplateInfoResponse couponTemplateInfoResponse = this.getCouponTemplateInfoResponse(request);
        CouponGrantRuleResponse couponGrantRuleResponse = couponTemplateInfoResponse.getGrantRuleInfo();
        CouponUseRuleResponse couponUseRuleResponse = couponTemplateInfoResponse.getUseRuleInfo();
        if (couponGrantRuleResponse.getIsOnlyBloc()) {  //仅集团发放
            return Response.success();
        }
        ApplicableHotelInfoResponse applicableHotelInfoResponse = couponGrantRuleResponse.getIsApplicableHotel()
                ? couponUseRuleResponse.getHotelInfos() : couponGrantRuleResponse.getCustomizedHotelInfos();
        this.buildApplicableHotelInfoResponse(request.getBlocCode(), applicableHotelInfoResponse);
        return Response.success(applicableHotelInfoResponse);
    }


    /**
     * 查询适用集团房型
     */
    @PostMapping("/applicable/bloc/roomType/info")
    public Response<List<CommonNameInfoResponse>> getApplicableBlocRoomType(@Valid @RequestBody CouponApplicableInfoRequest request) {
        CouponTemplateInfoResponse couponTemplateInfoResponse = this.getCouponTemplateInfoResponse(request);
        CouponUseRuleResponse useRuleInfo = couponTemplateInfoResponse.getUseRuleInfo();
        List<String> blocRoomTypeInfos = useRuleInfo.getBlocRoomTypeInfos();
        if (CollectionUtils.isEmpty(blocRoomTypeInfos)) {
            return Response.success(Collections.emptyList());
        }
        Map<String, String> roomTypeInfoMap = this.getRoomTypeInfoMap(request.getBlocCode());
        List<CommonNameInfoResponse> responses = blocRoomTypeInfos.stream().map(
                roomTypeCode -> this.buildRoomTypeInfo(roomTypeCode, roomTypeInfoMap)).collect(Collectors.toList());
        return Response.success(responses);
    }

    /**
     * 查询适用酒店房型
     */
    @PostMapping("/applicable/hotel/roomType/info")
    public Response<ApplicableHotelRoomTypeInfoResponse> getApplicableHotelRoomType(@Valid @RequestBody CouponApplicableInfoRequest request) {
        CouponTemplateInfoResponse couponTemplateInfoResponse = this.getCouponTemplateInfoResponse(request);
        CouponUseRuleResponse useRuleInfo = couponTemplateInfoResponse.getUseRuleInfo();
        ApplicableHotelRoomTypeInfoResponse hotelRoomTypeInfos = useRuleInfo.getHotelRoomTypeInfos();
        rebuildApplicableHotelRoomTypeInfoResponse(request.getBlocCode(), hotelRoomTypeInfos);
        return Response.success(hotelRoomTypeInfos);
    }

    /**
     * 查询适用房型列表
     */
    @PostMapping("/page/applicable/roomType/list")
    public Response<Pageable<CouponApplicableRoomTypeResponse>> pageApplicableHotelRoomTypeList(
            @Valid @RequestBody PageCouponApplicableRoomTypeRequest request) {
        List<PageHotelsResp> pageHotels = hotelDecorator.selectHotelsByFuzzy(request.getBlocCode(), request.getHotelNameOrCode(),
                StateEnum.VALID.getValue(), request.getTrackingId());
        Set<String> hotelCodes = pageHotels.stream().map(PageHotelsResp::getHotelCode).collect(Collectors.toSet());
        Set<String> havingPermissionHotelCodes = this.getHavingPermissionHotelCodes(hotelCodes);
        if (CollectionUtils.isEmpty(havingPermissionHotelCodes)) {
            return Response.success(Pageable.empty());
        }
        List<Long> hotelVids = havingPermissionHotelCodes.stream().map(Long::valueOf).collect(Collectors.toList());
        List<RoomTypeBaseInfoResp> roomTypeInfos = roomTypeDecorator.selectRoomTypesByFuzzy(request.getBlocCode(),
                hotelVids, request.getRoomTypeNameOrCode(), StateEnum.VALID.getValue());
        if (CollectionUtils.isEmpty(roomTypeInfos)) {
            return Response.success(Pageable.empty());
        }
        CouponTemplateInfoResponse couponTemplateInfoResponse = this.getCouponTemplateInfoResponse(
                couponTemplateConverter.convertCouponApplicableInfoRequestByRoomType(request));
        ApplicableHotelRoomTypeInfoResponse hotelRoomTypeInfos = couponTemplateInfoResponse.getUseRuleInfo().getHotelRoomTypeInfos();
        if(Objects.isNull(hotelRoomTypeInfos)){
            return Response.success(Pageable.empty());
        }
        if (Objects.equals(hotelRoomTypeInfos.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL_UNAVAILABLE.getScopeType())) {
            return Response.success(Pageable.empty());
        }
        List<CouponApplicableRoomTypeResponse> responses;
        if (Objects.equals(hotelRoomTypeInfos.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())) {
            responses = couponTemplateConverter.convertCouponApplicableRoomTypeResponses(roomTypeInfos);
        } else {
            responses = couponTemplateConverter.convertCouponApplicableRoomTypeResponses(roomTypeInfos,havingPermissionHotelCodes,hotelRoomTypeInfos);
        }
        return Response.success(
                this.buildPageableCouponApplicableList(request.getPageIndex(), request.getPageSize(),responses.size(), responses)
        );
    }


    /**
     * 查询适用集团价格方案
     */
    @PostMapping("/applicable/bloc/ratePlan/info")
    public Response<List<CommonNameInfoResponse>> getApplicableBlocRatePlan(@Valid @RequestBody CouponApplicableInfoRequest request) {
        CouponTemplateInfoResponse couponTemplateInfoResponse = this.getCouponTemplateInfoResponse(request);
        CouponUseRuleResponse useRuleInfo = couponTemplateInfoResponse.getUseRuleInfo();
        List<String> blocRatePlanInfos = useRuleInfo.getBlocRatePlanInfos();
        if (CollectionUtils.isEmpty(blocRatePlanInfos)) {
            return Response.success(Collections.emptyList());
        }
        Map<String, String> ratePlanInfoMap = getRatePlanInfoMap(request.getBlocCode());
        List<CommonNameInfoResponse> responses = blocRatePlanInfos.stream().map(
                ratePlanCode -> this.buildRatePlanInfo(ratePlanCode, ratePlanInfoMap)).collect(Collectors.toList());
        return Response.success(responses);
    }

    /**
     * 查询适用酒店价格方案
     */
    @PostMapping("/applicable/hotel/ratePlan/info")
    public Response<ApplicableHotelRatePlanInfoResponse> getApplicableHotelRatePlan(@Valid @RequestBody CouponApplicableInfoRequest request) {
        CouponTemplateInfoResponse couponTemplateInfoResponse = this.getCouponTemplateInfoResponse(request);
        CouponUseRuleResponse useRuleInfo = couponTemplateInfoResponse.getUseRuleInfo();
        ApplicableHotelRatePlanInfoResponse hotelRatePlanInfos = useRuleInfo.getHotelRatePlanInfos();
        rebuildApplicableHotelRatePlanInfoResponse(request.getBlocCode(), hotelRatePlanInfos);
        return Response.success(hotelRatePlanInfos);
    }

    /**
     * 查询适用价格方案列表
     */
    @PostMapping("/page/applicable/ratePlan/list")
    public Response<Pageable<CouponApplicableRatePlanResponse>> pageApplicableHotelRatePlanList(
            @Valid @RequestBody PageCouponApplicableRatePlanRequest request) {
        List<PageHotelsResp> pageHotels = hotelDecorator.selectHotelsByFuzzy(request.getBlocCode(), request.getHotelNameOrCode(),
                StateEnum.VALID.getValue(), request.getTrackingId());
        Map<String,String> hotelCodeMap = pageHotels.stream().collect(Collectors.toMap(PageHotelsResp::getHotelCode,PageHotelsResp::getHotelName,(o1,o2)->o2));
        Set<String> havingPermissionHotelCodes = this.getHavingPermissionHotelCodes(hotelCodeMap.keySet());
        if (CollectionUtils.isEmpty(havingPermissionHotelCodes)) {
            return Response.success(Pageable.empty());
        }
        List<HotelRatePlanListResp> hotelRatePlanList = ratePlanDecorator.listRatePlan(request.getBlocCode(),
                request.getRatePlanNameOrCode(),new ArrayList<>(havingPermissionHotelCodes),StateEnum.VALID.getValue());
        if (CollectionUtils.isEmpty(hotelRatePlanList)) {
            return Response.success(Pageable.empty());
        }
        CouponTemplateInfoResponse couponTemplateInfoResponse = this.getCouponTemplateInfoResponse(
                couponTemplateConverter.convertCouponApplicableInfoRequestByRatePlan(request));
        ApplicableHotelRatePlanInfoResponse hotelRatePlanInfos = couponTemplateInfoResponse.getUseRuleInfo().getHotelRatePlanInfos();
        if(Objects.isNull(hotelRatePlanInfos)){
            return Response.success(Pageable.empty());
        }
        if (Objects.equals(hotelRatePlanInfos.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL_UNAVAILABLE.getScopeType())) {
            return Response.success(Pageable.empty());
        }
        List<CouponApplicableRoomTypeResponse> responses;
        if (Objects.equals(hotelRatePlanInfos.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())) {
            responses = couponTemplateConverter.convertCouponApplicableRatePlanResponses(hotelRatePlanList,hotelCodeMap);
        } else {
            responses = couponTemplateConverter.convertCouponApplicableRatePlanResponses(hotelRatePlanList,havingPermissionHotelCodes,
                    hotelRatePlanInfos.getHotelRatePlanInfos(),hotelCodeMap);
        }
        return Response.success(
                this.buildPageableCouponApplicableList(request.getPageIndex(),request.getPageSize(),responses.size(), responses)
        );
    }


    private void buildApplicableHotelInfoResponse(String blocCode, ApplicableHotelInfoResponse applicableHotelInfoResponse) {
        if (Objects.equals(applicableHotelInfoResponse.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())
                || Objects.equals(applicableHotelInfoResponse.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL_UNAVAILABLE.getScopeType())) {
            return;
        }
        if (!CollectionUtils.isEmpty(applicableHotelInfoResponse.getBrandInfos())) {
            List<SelectBrandResp> selectBrands = brandDecorator.listBrand(blocCode, StateEnum.VALID.getValue(), null);
            Map<String, String> brandInfoMap = selectBrands.stream().collect(Collectors.toMap(SelectBrandResp::getBrandCode, SelectBrandResp::getBrandName, (o1, o2) -> o2));
            applicableHotelInfoResponse.getBrandInfos().stream().parallel().forEach(commonNameInfo -> {
                commonNameInfo.setName(brandInfoMap.getOrDefault(commonNameInfo.getCode(), ""));
            });
        }
        if (!CollectionUtils.isEmpty(applicableHotelInfoResponse.getAreaInfos())) {
            List<AreaResp> areaInfos = areaDecorator.listAreas(blocCode, StateEnum.VALID.getValue(), "");
            Map<Long, String> areaInfoMap = areaInfos.stream().collect(Collectors.toMap(AreaResp::getAreaId, AreaResp::getAreaName, (o1, o2) -> o2));
            applicableHotelInfoResponse.getAreaInfos().stream().parallel().forEach(commonNameInfo -> {
                commonNameInfo.setName(areaInfoMap.getOrDefault(Long.valueOf(commonNameInfo.getCode()), ""));
            });
        }
        if (!CollectionUtils.isEmpty(applicableHotelInfoResponse.getRegionInfos())) {
            List<Integer> regionIds = applicableHotelInfoResponse.getRegionInfos().stream().map(commonInfo -> Integer.parseInt(commonInfo.getCode())).distinct().collect(Collectors.toList());
            List<RegionResp> regionInfos = regionDecorator.getByIds(regionIds);
            Map<Integer, String> brandInfoMap = regionInfos.stream().collect(Collectors.toMap(RegionResp::getId, RegionResp::getName, (o1, o2) -> o2));
            applicableHotelInfoResponse.getRegionInfos().stream().parallel().forEach(commonNameInfo -> {
                commonNameInfo.setName(brandInfoMap.getOrDefault(Integer.parseInt(commonNameInfo.getCode()), ""));
            });
        }
        if (!CollectionUtils.isEmpty(applicableHotelInfoResponse.getHotelInfos())) {
            Map<Long, String> hotelInfoMap = this.getHotelInfoMap(blocCode);
            applicableHotelInfoResponse.getHotelInfos().stream().parallel().forEach(commonNameInfo -> {
                commonNameInfo.setName(hotelInfoMap.getOrDefault(Long.valueOf(commonNameInfo.getCode()), ""));
            });
        }

    }

    private Map<Long, String> getHotelInfoMap(String blocCode) {
        List<SelectHotelResp> selectHotels = hotelDecorator.selectHotels(blocCode, null, "", null, UUID.randomUUID().toString());
        return selectHotels.stream().collect(Collectors.toMap(SelectHotelResp::getHotelVid, SelectHotelResp::getHotelName, (o1, o2) -> o2));
    }

    private Map<String, String> getRoomTypeInfoMap(String blocCode) {
        List<RoomTypeBaseInfoResp> roomTypeBaseInfos = roomTypeDecorator.selectRoomTypesByBlocCode(blocCode, StateEnum.VALID.getValue());
        return roomTypeBaseInfos.stream().collect(Collectors.toMap(RoomTypeBaseInfoResp::getRoomTypeCode, RoomTypeBaseInfoResp::getRoomTypeName, (o1, o2) -> o2));
    }

    private CommonNameInfoResponse buildRoomTypeInfo(String roomTypeCode, Map<String, String> roomTypeInfoMap) {
        CommonNameInfoResponse response = new CommonNameInfoResponse();
        response.setCode(roomTypeCode);
        response.setName(roomTypeInfoMap.getOrDefault(roomTypeCode, ""));
        return response;
    }

    private void rebuildApplicableHotelRoomTypeInfoResponse(String blocCode, ApplicableHotelRoomTypeInfoResponse hotelRoomTypeInfos) {
        if (Objects.isNull(hotelRoomTypeInfos)) {
            return;
        }
        if (Objects.equals(hotelRoomTypeInfos.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())
                || Objects.equals(hotelRoomTypeInfos.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL_UNAVAILABLE.getScopeType())) {
            return;
        }
        Map<Long, String> hotelInfoMap = this.getHotelInfoMap(blocCode);
        Map<String, String> roomTypeInfoMap = this.getRoomTypeInfoMap(blocCode);
        hotelRoomTypeInfos.getHotelRoomTypeInfos().stream().parallel().forEach(hotelRoomTypeInfo -> {
            hotelRoomTypeInfo.setHotelCode(hotelRoomTypeInfo.getHotelCode());
            hotelRoomTypeInfo.setHotelName(hotelInfoMap.getOrDefault(Long.valueOf(hotelRoomTypeInfo.getHotelCode()), ""));
            List<CommonNameInfoResponse> roomTypeInfos = hotelRoomTypeInfo.getRoomTypeInfos()
                    .stream().map(roomTypeInfo -> buildRoomTypeInfo(roomTypeInfo.getCode(), roomTypeInfoMap)).collect(Collectors.toList());
            hotelRoomTypeInfo.setRoomTypeInfos(roomTypeInfos);
        });
    }

    private void rebuildApplicableHotelRatePlanInfoResponse(String blocCode, ApplicableHotelRatePlanInfoResponse hotelRatePlanInfos) {
        if (Objects.isNull(hotelRatePlanInfos)) {
            return;
        }
        if (Objects.equals(hotelRatePlanInfos.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())
                || Objects.equals(hotelRatePlanInfos.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL_UNAVAILABLE.getScopeType())) {
            return;
        }
        Map<Long, String> hotelInfoMap = this.getHotelInfoMap(blocCode);
        Map<String, String> roomTypeInfoMap = this.getRoomTypeInfoMap(blocCode);
        hotelRatePlanInfos.getHotelRatePlanInfos().stream().parallel().forEach(hotelRatePlanInfo -> {
            hotelRatePlanInfo.setHotelCode(hotelRatePlanInfo.getHotelCode());
            hotelRatePlanInfo.setHotelName(hotelInfoMap.getOrDefault(Long.valueOf(hotelRatePlanInfo.getHotelCode()), ""));
            List<CommonNameInfoResponse> roomTypeInfos = hotelRatePlanInfo.getRatePlanInfos()
                    .stream().map(roomTypeInfo -> buildRoomTypeInfo(roomTypeInfo.getCode(), roomTypeInfoMap)).collect(Collectors.toList());
            hotelRatePlanInfo.setRatePlanInfos(roomTypeInfos);
        });
    }

    private Map<String, String> getRatePlanInfoMap(String blocCode) {
        List<HotelRatePlanListResp> hotelRatePlanList = ratePlanDecorator.listRatePlanByBlocCode(blocCode, StateEnum.VALID.getValue());
        return hotelRatePlanList.stream().collect(Collectors.toMap(HotelRatePlanListResp::getRatePlanCode, HotelRatePlanListResp::getRatePlanName, (o1, o2) -> o2));
    }

    private CommonNameInfoResponse buildRatePlanInfo(String ratePlanCode, Map<String, String> ratePlanInfoMap) {
        CommonNameInfoResponse response = new CommonNameInfoResponse();
        response.setCode(ratePlanCode);
        response.setName(ratePlanInfoMap.getOrDefault(ratePlanCode, ""));
        return response;
    }


    private CouponTemplateInfoResponse getCouponTemplateInfoResponse(CouponApplicableInfoRequest request) {
        GetCouponTemplateDetailReq req = couponTemplateConverter.convertGetCouponTemplateDetailReqByApplicableInfo(request);
        CouponTemplateDetailResp resp = couponTemplateDecorator.getCouponTemplateDetail(req);
        return this.buildCouponTemplateInfoResponse(resp);
    }


    private CouponTemplateInfoResponse buildCouponTemplateInfoResponse(CouponTemplateDetailResp resp) {
        CouponTemplateInfoResponse couponTemplateInfoResponse = couponTemplateConverter.convertCouponTemplateInfoResponse(resp);
        couponTemplateInfoResponse.setUseRuleInfo(this.buildCouponUseRuleResponse(resp.getUseRuleInfo()));
        couponTemplateInfoResponse.setGrantRuleInfo(this.buildCouponGrantRuleResponse(resp.getGrantRuleInfo()));
        return couponTemplateInfoResponse;
    }

    private CouponUseRuleResponse buildCouponUseRuleResponse(CouponUseRuleDto couponUseRuleDto) {
        CouponUseRuleResponse couponUseRuleResponse = couponTemplateConverter.convertCouponUseRuleResponse(couponUseRuleDto);
        couponTemplateConverter.buildCouponCommonApplicableRuleInfo(couponUseRuleDto.getCommonApplicableRuleInfos(), couponUseRuleResponse);
        List<ApplicableRuleInfoDto<ApplicableRuleDataInfoDto>> applicableRuleInfos = couponUseRuleDto.getApplicableRuleInfos();
        List<String> hotelCodes = couponTemplateConverter.getApplicableHotelCodes(applicableRuleInfos); //获取酒店code
        List<HotelBaseInfoResp> hotelBaseInfoResp = hotelDecorator.listHotelBaseInfos(null, hotelCodes, UserThreadHolder.getTrackingId());
        couponTemplateConverter.buildCouponOtherApplicableRuleInfo(applicableRuleInfos, hotelBaseInfoResp, couponUseRuleResponse);
        return couponUseRuleResponse;
    }


    private CouponGrantRuleResponse buildCouponGrantRuleResponse(CouponGrantRuleDto couponGrantRuleDto) {
        CouponGrantRuleResponse couponGrantRuleResponse = couponTemplateConverter.convertCouponGrantRuleResponse(couponGrantRuleDto);
        ApplicableHotelInfoResponse applicableHotelInfoResponse =
                couponTemplateConverter.buildCouponApplicableHotelInfoResponse(couponGrantRuleDto.getCustomizeHotelInfos());
        couponGrantRuleResponse.setCustomizedHotelInfos(applicableHotelInfoResponse);
        return couponGrantRuleResponse;
    }

}
