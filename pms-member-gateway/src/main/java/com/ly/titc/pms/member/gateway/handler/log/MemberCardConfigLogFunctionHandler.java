package com.ly.titc.pms.member.gateway.handler.log;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardConfigResp;
import com.ly.titc.pms.member.com.annotation.LogRecordFunction;
import com.ly.titc.pms.member.com.enums.ApplicationScopeTypeEnum;
import com.ly.titc.pms.member.com.enums.MemberCardTypeEnum;
import com.ly.titc.pms.member.com.enums.ModuleEnum;
import com.ly.titc.pms.member.gateway.entity.request.member.card.DeleteMemberCardConfigRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.SaveMemberCardConfigRequest;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberCardConfigLogFunctionHandler
 * @Date：2024-11-15 11:47
 * @Filename：MemberCardConfigLogFunctionHandler
 */
@LogRecordFunction("memberCardConfig")
public class MemberCardConfigLogFunctionHandler extends AbstractModuleServiceHandler implements ApplicationContextAware {

    private static MemberCardInfoDecorator decorator;

    private static HotelDecorator hotelDecorator;

    @Override
    public List<String> execute(String blocCode, String hotelCode, String name, String trackingId) {
        return null;
    }

    @Override
    public Integer getModuleId() {
        return ModuleEnum.MEMBER_CARD_MANAGE.getCode();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        decorator = applicationContext.getBean(MemberCardInfoDecorator.class);
        hotelDecorator = applicationContext.getBean(HotelDecorator.class);
    }

    @LogRecordFunction("addMemberCardConfig")
    public static String addMemberCardConfig(SaveMemberCardConfigRequest request) {
        String name = request.getCardName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(String.format("新增【%s】", name));
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("deleteMemberCardConfig")
    public static String deleteMemberCardConfig(DeleteMemberCardConfigRequest request) {
        String name = request.getCardName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(String.format("删除【%s】", name));
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("updateMemberCardConfig")
    public static String updateMemberCardConfig(SaveMemberCardConfigRequest request) {
        String name = request.getCardName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(String.format("修改【%s】", name));
        records.add(stringBuilder.toString());
        // 查询详情
        MemberCardConfigResp memberCardConfigResp = decorator.getById(Long.parseLong(request.getId()), request.getTrackingId());
        if (!memberCardConfigResp.getCardType().equals(request.getCardType())) {
            records.add(String.format("【会员卡类型】%s->%s", MemberCardTypeEnum.getNameByType(memberCardConfigResp.getCardType()), MemberCardTypeEnum.getNameByType(request.getCardType())));
        }
        if (!memberCardConfigResp.getCardName().equals(request.getCardName())) {
            records.add(String.format("【会员卡名称】%s->%s", memberCardConfigResp.getCardName(), request.getCardName()));
        }
        if (!memberCardConfigResp.getDescription().equals(request.getDescription())) {
            records.add(String.format("【备注】%s->%s", memberCardConfigResp.getDescription(), request.getDescription()));
        }
        if (memberCardConfigResp.getState() != request.getState()) {
            records.add(String.format("【状态】%s->%s", memberCardConfigResp.getState() == 0 ? "停用" : "启用", request.getState() == 0 ? "停用" : "启用"));
        }
        if (memberCardConfigResp.getSort() != request.getSort()) {
            records.add(String.format("【排序】%s->%s", memberCardConfigResp.getSort(), request.getSort()));
        }
        if (memberCardConfigResp.getIsDefault() != request.getIsDefault()) {
            records.add(String.format("【默认卡】%s->%s", memberCardConfigResp.getIsDefault() == 0 ? "否" : "是", request.getIsDefault() == 0 ? "否" : "是"));
        }
        if (memberCardConfigResp.getCardNoRule().getCardLength() != request.getCardLength()) {
            records.add(String.format("【卡号长度】%s->%s", memberCardConfigResp.getCardNoRule().getCardLength(), request.getCardLength()));
        }
        if (memberCardConfigResp.getCardNoRule().getExcludeNumber() != null && !memberCardConfigResp.getCardNoRule().getExcludeNumber().equals(request.getExcludeNumber())) {
            records.add(String.format("【不允许包含】%s->%s", memberCardConfigResp.getCardNoRule().getExcludeNumber(), request.getExcludeNumber()));
        }
        if (memberCardConfigResp.getCardNoRule().getCardPrefix() != null && !memberCardConfigResp.getCardNoRule().getCardPrefix().equals(request.getCardPrefix())) {
            records.add(String.format("【卡号前缀】%s->%s", memberCardConfigResp.getCardNoRule().getCardPrefix(), request.getCardPrefix()));
        }
        if (memberCardConfigResp.getApplicationScope() != request.getApplicationScope()) {
            records.add(String.format("【适用门店范围】%s->%s", ApplicationScopeTypeEnum.getNameByType(memberCardConfigResp.getApplicationScope()), ApplicationScopeTypeEnum.getNameByType(request.getApplicationScope())));
        }
        if (!new HashSet<>(request.getScopeValues()).equals(new HashSet<>(memberCardConfigResp.getScopeValues()))) {
            Set<String> hotelCodes = new HashSet<>();
            hotelCodes.addAll(memberCardConfigResp.getScopeValues());
            hotelCodes.addAll(request.getScopeValues());
            List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(memberCardConfigResp.getBlocCode(), new ArrayList<>(hotelCodes), request.getTrackingId());
            Map<String, HotelBaseInfoResp> hotelBaseInfoMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, hotelBaseInfoResp -> hotelBaseInfoResp));
            records.add(String.format("【适用门店】%s->%s",
                    CollectionUtils.isNotEmpty(memberCardConfigResp.getScopeValues()) ? String.join(",", memberCardConfigResp.getScopeValues().stream().map(item -> hotelBaseInfoMap.get(item).getHotelName()).collect(Collectors.toList()))  : "全部" ,
                    CollectionUtils.isNotEmpty(request.getScopeValues()) ? String.join(",", request.getScopeValues().stream().map(item -> hotelBaseInfoMap.get(item).getHotelName()).collect(Collectors.toList()))  : "全部" ));
        }
        json.put("records", records);
        return json.toJSONString();
    }
}
