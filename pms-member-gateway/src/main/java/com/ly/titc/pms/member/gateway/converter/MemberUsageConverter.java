package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.usageRule.QueryMemberScopeUsageForAssetReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleAssetResp;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.usage.*;
import com.ly.titc.pms.member.gateway.entity.response.usage.MemberPointUsageRuleResponse;
import com.ly.titc.pms.member.gateway.entity.response.usage.MemberStoreUsageRuleAssetResponse;
import com.ly.titc.pms.member.gateway.entity.response.usage.MemberStoreUsageRuleResponse;
import com.ly.titc.pms.member.gateway.entity.response.usage.MemberUsageRuleSaveResultResponse;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 14:31
 */
@Mapper(componentModel = "spring")
public interface MemberUsageConverter extends BaseConverter {

    QueryMemberMasterUsageDto convert(QueryMemberMasterUsageRequest request);

    MemberStoreUsageRuleResponse convert(MemberStoreUsageRuleDto dto);


   MemberPointUsageRuleResponse convert(MemberPointUsageRuleDto ruleDtoPageable);

    MemberStoreUsageRuleConfigSaveDto convert(MemberStoreUsageRuleConfigSaveRequest request);

    MemberPointUsageRuleConfigSaveDto convert(MemberPointUsageRuleConfigSaveRequest request);

    List<MemberUsageRuleSaveResultResponse> convert(List<MemberUsageRuleSaveResultDto> resultDtos);

    UpdateMemberUsageStateDto convert(UpdateMemberUsageStateRequest request);

    DeleteMemberUsageDto convert(DeleteMemberUsageRequest request);

    BaseDto convert(BaseRequest request);

    MemberUsageRuleSaveResultResponse convert(MemberUsageRuleSaveResultDto dto);

    default QueryMemberScopeUsageForAssetReq convertAssetReq(BaseRequest request){
        QueryMemberScopeUsageForAssetReq req = new QueryMemberScopeUsageForAssetReq();
        req.setScopeSource(ScopeSourceEnum.BLOC.getCode());
        req.setScopeSourceCode(request.getBlocCode());
        req.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        req.setTrackingId(request.getTrackingId());
        return req;
    }

    List<MemberStoreUsageRuleAssetResponse> convertAsset(List<MemberStoreUsageRuleAssetResp> resps);
}
