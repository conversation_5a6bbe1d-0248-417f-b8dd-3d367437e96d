package com.ly.titc.pms.member.gateway.entity.response.menu;


import lombok.Data;

import java.util.List;

/**
 * @Description: 菜单信息
 * @Author: lixu
 * @Date: 2022/7/12
 */
@Data
public class MenuInfoResponse {

    /**
     * 资源id
     */
    private Long resourceId;

    /**
     * 父资源id
     */
    private Long resourcePid;

    /**
     * 资源名称
     */
    private String resourceName;

    /**
     * 资源自定义名称
     */
    private String resourceCustomName;

    /**
     * 资源英文名称
     */
    private String resourceEnName;

    /**
     * 资源类型 BTN按钮 PAGE 页面 MENU 菜单
     */
    private String resourceType;

    /**
     * 资源url
     */
    private String resourceUrl;

    /**
     * 资源别名
     */
    private String resourceAlias;

    /**
     * 资源图标
     */
    private String resourceIcon;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 是否是自定义资源
     */
    private Integer isCustomize;

    /**
     * 子菜单
     */
    private List<MenuInfoResponse> children;
}
