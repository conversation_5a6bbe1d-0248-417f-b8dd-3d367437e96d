package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import lombok.Data;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-5
 */
@Data
public class CouponTemplateListResponse{
    /**
     * 券类型 1.折扣券,2.代金券,3.免房券,4.餐饮券,5.抵扣券,6.小时券,7.延时券,8.升房券,9.体验券,10.通用券
     */
    private Integer couponType;

    /**
     * 券类型名称
     */
    private String couponTypeName;

    /**
     * 券名称
     */
    private String couponName;

    /**
     * 券值
     */
    private String couponValue;

    /**
     * 状态 1-已生效 2-已作废 3-已过期
     */
    private Integer state;

    /**
     * 模版code
     */
    private String templateCode;

    /**
     * 模板版本
     */
    private Integer version;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;

    /**
     * 券有效期
     */
    private CouponEffectDateConfigResponse effectDateInfo;
}
