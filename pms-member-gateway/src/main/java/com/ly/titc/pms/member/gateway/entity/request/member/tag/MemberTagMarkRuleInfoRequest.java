package com.ly.titc.pms.member.gateway.entity.request.member.tag;

import lombok.Data;

/**
 * @Author：rui
 * @name：MemberTagMarkRuleInfoRequest
 * @Date：2024-11-15 10:57
 * @Filename：MemberTagMarkRuleInfoRequest
 */
@Data
public class MemberTagMarkRuleInfoRequest {

    private String id;

    /**
     * 打标条件类型 0-入住次数 1-入住房晚 2-充值金额 3-消费金额（不含赠送） 4-积分 5-平均房费 6-未入住天数 7-注册天数
     */
    private Integer conditionType;

    /**
     * 计算方式  1 大于等于 2 大于 3 小于等于 4 小于
     */
    private Integer calculateType;

    /**
     * 条件值
     */
    private String conditionValue;
}
