package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.cashier.dubbo.entity.response.SelectResp;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.mdm.entity.request.SelectReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.ReceiveMemberPointReq;
import com.ly.titc.pms.member.asset.dubbo.enums.PointActionItemEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.gateway.converter.AssetConverter;
import com.ly.titc.pms.member.gateway.entity.request.SelectRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.point.MemberPointAdjustRequest;
import com.ly.titc.pms.member.mediator.entity.dto.asset.MemberRecordOPResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.asset.ReceiveMemberPointDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.MemberPointOpDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberSysConfigDecorator;
import com.ly.titc.pms.member.mediator.service.MemberPointAssetOpMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import java.util.ArrayList;
import java.util.List;

/**
 * 积分操作
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 15:32
 */
@Slf4j
@RestController
@RequestMapping("/member/point/op")
public class MemberPointAssetOpController {

    @Resource
    private MemberPointOpDecorator pointOpDecorator;
    @Resource
    private AssetConverter assetConverter;
    @Resource
    private MemberPointAssetOpMedService opMedService;


    /**
     * 积分调整项目下拉框
     */
    @RequestMapping("/selectActionItem")
    public Response<List<SelectResp>> selectCashierProduct(@RequestBody SelectReq request) {
        String text = request.getText();
        List<SelectResp> list = new ArrayList<>();
        for(PointActionItemEnum value: PointActionItemEnum.values()){
            buildSelectResp(text, list, value.getActionItemDesc(), value.getActionItem());

        }
        return Response.success(list);
    }

    /**
     * 积分调整操作
     */
    @PostMapping("/adjust")
    public Response<String> adjust(@Valid @RequestBody MemberPointAdjustRequest request){
        if(request.getScore() == 0){
            throw new ServiceException("积分数不能为0", RespCodeEnum.CODE_400.getCode());
        }
        String recordeNo ="";
        if(request.getScore()>0){
            ReceiveMemberPointDto receiveDto =  assetConverter.convert(request);
            MemberRecordOPResultDto resultDto =  opMedService.receive(receiveDto);
            recordeNo = resultDto.getRecordNo();
        }else{
            MemberRecordOPResultDto  resultDto= opMedService.consume(assetConverter.convertConsumeReq(request));
            recordeNo = resultDto.getRecordNo();
        }

        return Response.success(recordeNo);

    }



    private void buildSelectResp(String text, List<SelectResp> list, String desc, String code) {
        SelectResp selectResp = new SelectResp();
        selectResp.setText(desc)
                .setValue(code);
        if (StringUtils.isEmpty(text)) {
            list.add(selectResp);
        } else {
            if (desc.contains(text)) {
                list.add(selectResp);
            }
        }
    }


}
