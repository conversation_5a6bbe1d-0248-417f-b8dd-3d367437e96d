package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-5 11:08
 */
@Data
@Accessors(chain = true)
public class CouponDetailResponse {

    /**
     * 发放时间
     * yyyy-MM-dd HH:mm:ss
     */
    private String grantTime;

    /**
     * 所属人
     */
    private String owner;
    /**
     * 客户编号
     */
    private String ownerCustomerNo;
    /**
     * 会员卡号
     */
    private String ownerMemberCardNo;
    /**
     * 手机号
     */
    private String mobile;
}
