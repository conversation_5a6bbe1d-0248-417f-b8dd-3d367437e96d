package com.ly.titc.pms.member.gateway.entity.response.member;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberCardLevelUpgradeRuleResp
 * @Date：2024-11-13 11:03
 * @Filename：MemberCardLevelUpgradeRuleResp
 */
@Data
@Accessors(chain = true)
public class MemberCardLevelUpgradeRuleResponse {

    /**
     * id
     */
    private Long id;

    /**
     * 会员卡模版ID
     */
    private Long cardId;

    /**
     * 会员卡模版名称
     */
    private String cardName;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 原等级
     */
    private Integer sourceLevel;

    /**
     * 原等级名称
     */
    private String sourceLevelName;

    /**
     * 目标等级
     */
    private Integer targetLevel;

    /**
     * 目标等级名称
     */
    private String targetLevelName;

    /**
     * 升级成功执行类型 ALL - 全部  ANY-满足任何一个条件
     */
    private String successfulPerformType;

    /**
     * 统计周期
     */
    private Integer cycleType;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 无效 1 正常
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 指标列表
     */
    private List<MemberCardLevelUpgradeRuleDetailResponse> details;

}
