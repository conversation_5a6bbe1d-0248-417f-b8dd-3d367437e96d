package com.ly.titc.pms.member.gateway.entity.response.spm.activity;

import com.ly.titc.pms.spm.dubbo.entity.dto.activity.common.MemberActivityCommonDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.common.MemberCardSaleActivityGearsDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 售卡活动返回
 *
 * <AUTHOR>
 * @date 2024/12/31 14:54
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class MemberSaleCardActivityDetailResponse extends MemberActivityCommonResponse {

    /**
     * 档位
     */
    private List<MemberCardSaleActivityGearsDto> carriers;

    /**
     * 会员卡code
     */
    private String cardId;

    /**
     * 会员卡名称
     */
    private String cardName;

}
