package com.ly.titc.pms.member.gateway.entity.request.asset;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：PageMemberPointConsumeRecordRequest
 * @Date：2024-12-9 17:20
 * @Filename：PageMemberPointConsumeRecordRequest
 */
@Data
public class PageMemberPointConsumeRecordRequest extends PageBaseRequest {

    /**
     * 会员编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 开始时间
     */
    private String beginTime;

    /**
     * 结束时间
     */
    private String endTime;

}
