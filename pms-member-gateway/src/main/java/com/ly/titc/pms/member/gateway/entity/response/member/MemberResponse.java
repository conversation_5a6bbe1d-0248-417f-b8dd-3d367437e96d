package com.ly.titc.pms.member.gateway.entity.response.member;

import com.ly.titc.pms.member.gateway.annoation.Sensitive;
import com.ly.titc.pms.member.gateway.entity.enums.SensitiveFieldTypeEnum;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberResponse
 * @Date：2024-11-18 17:29
 * @Filename：MemberResponse
 */
@Data
public class MemberResponse {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;

    /**
     * 自定义会员号
     */
    private String customizeMemberNo;

    /**
     * 会员手机号
     */
//    @Sensitive(fieldType = SensitiveFieldTypeEnum.MOBILE)
    private String mobile;

    /**
     * 证件类型
     */
    private String idTypeStr;

    /**
     * 证件类型
     */
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 会员昵称
     */
    private String nickName;

    /**
     * 会员真实姓名
     */
    private String realName;

    /**
     * 会员英文名
     */
    private String enName;

    /**
     * 性别1：男；2：女
     */
    private Integer gender;

    /**
     * 性别1：男；2：女
     */
    private String genderStr;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 民族
     */
    private String nation;

    /**
     * 语言
     */
    private String language;

    /**
     * 车牌号
     */
    private String numberPlate;

    /**
     * 会员来源
     */
    private String source;

    /**
     * 注册门店类型 集团:BLOC;门店:HOTEL
     */
    private String registerHotelType;

    /**
     * 注册门店(集团编号; 酒店编号)
     */
    private String registerHotel;

    /**
     * 注册门店名称
     */
    private String registerHotelName;

    /**
     * 销售员
     */
    private String salesman;

    /**
     * 会员状态 1 在用 0 注销
     */
    private Integer state;

    /**
     * 是否是黑名单用户 0 未拉黑 1 已拉黑
     */
    private Integer blackFlag;

    /**
     * 备注
     */
    private String remark;

    /**
     * 会员联系方式
     */
    private MemberContactResponse memberContactInfo;

    /**
     * 会员卡信息
     */
    private List<MemberCardInfoResponse> memberCardInfos;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;

    /**
     * 入住次数
     */
    private Integer checkInCount;
}
