package com.ly.titc.pms.member.gateway.entity.request.member.card;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

/**
 * @Author：rui
 * @name：ListCardRelatedPrivilegeRequest
 * @Date：2024-11-13 20:52
 * @Filename：ListCardRelatedPrivilegeRequest
 */
@Data
public class ListCardRelatedPrivilegeRequest extends BaseRequest {

    /**
     * 卡id
     */
    private String cardId;

    /**
     * 等级
     */
    private Integer level;
}
