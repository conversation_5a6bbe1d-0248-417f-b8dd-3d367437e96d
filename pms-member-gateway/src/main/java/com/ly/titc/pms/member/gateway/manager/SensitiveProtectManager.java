package com.ly.titc.pms.member.gateway.manager;

import com.ly.titc.pms.member.gateway.handler.sensitive.SensitiveProtectHandler;
import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/4 14:54
 */
public class SensitiveProtectManager {

    private static Map<String, SensitiveProtectHandler> instance = new HashMap<>();

    /**
     * put
     *
     * @param mode   模式
     * @param handler 处理类
     */
    public static void putInstance(String mode, SensitiveProtectHandler handler) {

        instance.put(mode, handler);
    }

    /**
     * get
     *
     * @param mode
     * @return
     */
    public static SensitiveProtectHandler getInstance(String mode) {

        if (ObjectUtils.isEmpty(mode)) {
            return null;
        }
        return instance.get(mode);
    }

}
