package com.ly.titc.pms.member.gateway.entity.request.member.config;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

/**
 * @Author：rui
 * @name：GetMemberRelatedConfigRequest
 * @Date：2024-12-11 14:23
 * @Filename：GetMemberRelatedConfigRequest
 */
@Data
public class GetMemberRelatedConfigRequest extends BaseRequest {

    /**
     * 0 注册 1 手机验证设置 2 列表显示 3 列表快捷操作
     */
    private Integer type;

}
