package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberTagConfigInfoResp;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.com.enums.MemberTagEnum;
import com.ly.titc.pms.member.gateway.entity.request.member.profile.*;
import com.ly.titc.pms.member.gateway.entity.response.member.profile.*;
import com.ly.titc.pms.member.mediator.entity.dto.profile.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberProfileConverter
 * @Date：2024-11-18 14:14
 * @Filename：MemberProfileConverter
 */
@Mapper(componentModel = "spring")
public interface MemberProfileConverter extends BaseConverter {

    List<MemberCommonInvoiceHeaderResponse> convertMemberCommonInvoiceHeaderResponse(List<MemberProfileInvoiceHeaderInfoDto> infoList);

    @Mapping(target = "createUser", source = "request.operator")
    SaveMemberProfileInvoiceHeaderDto convertSaveMemberProfileInvoiceHeaderInfo(SaveCommonInvoiceHeaderRequest request, Integer masterType, String masterCode);
    @Mapping(target = "modifyUser", source = "request.operator")
    SaveMemberProfileInvoiceHeaderDto convertUpdateMemberProfileInvoiceHeaderInfo(SaveCommonInvoiceHeaderRequest request, Integer masterType, String masterCode);

    List<MemberCommonAddressResponse> convertMemberCommonAddressResponse(List<MemberProfileAddressInfoDto> infoList);
    @Mapping(target = "createUser", source = "request.operator")
    SaveMemberProfileAddressDto convertSaveMemberProfileAddressInfo(SaveCommonAddressRequest request, Integer masterType, String masterCode);

    @Mapping(target = "modifyUser", source = "request.operator")
    SaveMemberProfileAddressDto convertUpdateMemberProfileAddressInfo(SaveCommonAddressRequest request, Integer masterType, String masterCode);

    @Mapping(target = "createUser", source = "request.operator")
    SaveMemberProfileOccupantsDto convertSaveMemberProfileOccupantsInfo(SaveCommonOccupantRequest request, Integer masterType, String masterCode);

    @Mapping(target = "modifyUser", source = "request.operator")
    SaveMemberProfileOccupantsDto convertUpdateMemberProfileOccupantsInfo(SaveCommonOccupantRequest request, Integer masterType, String masterCode);

    default List<ListMemberProfileTagResponse> convertMemberProfileTagResponse(List<MemberProfileTagInfoDto> infoList, List<MemberTagConfigInfoResp> memberTagConfigInfoRespList) {
        Map<Integer, List<MemberProfileTagInfoDto>> map = infoList.stream().collect(Collectors.groupingBy(MemberProfileTagInfoDto::getTagType));
        Map<Long, MemberTagConfigInfoResp> memberTagConfigInfoRespMap = memberTagConfigInfoRespList.stream().collect(Collectors.toMap(MemberTagConfigInfoResp::getId,
                memberTagConfigInfoResp -> memberTagConfigInfoResp));
        return map.entrySet().stream().map(entry -> {
            ListMemberProfileTagResponse response = new ListMemberProfileTagResponse();
            response.setType(entry.getKey());
            response.setName(MemberTagEnum.getDescByType(entry.getKey()));
            response.setList(entry.getValue().stream().map(item -> convertMemberProfileTagResponseItem(item, memberTagConfigInfoRespMap.getOrDefault(item.getTagId(), new MemberTagConfigInfoResp())))
                    .sorted(Comparator.comparing(MemberProfileTagResponse::getSort).reversed()).collect(Collectors.toList()));
            return response;
        }).collect(Collectors.toList());
    }

    default MemberProfileTagResponse convertMemberProfileTagResponseItem(MemberProfileTagInfoDto info, MemberTagConfigInfoResp resp) {
        MemberProfileTagResponse response = convertMemberProfileTagResponseItem(info);
        response.setTagName(resp.getName());
        response.setSort(resp.getSort());
        return response;
    }
    MemberProfileTagResponse convertMemberProfileTagResponseItem(MemberProfileTagInfoDto info);

    SaveMemberProfileTagDto convertMemberProfileTagInfo(MemberProfileTagRequest request, Integer masterType, String blocCode, String creator);

    List<MemberCommonOccupantResponse> convertMemberCommonOccupantResponse(List<MemberProfileOccupantsInfoDto> infoList);
}
