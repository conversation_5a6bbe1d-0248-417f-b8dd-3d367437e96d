package com.ly.titc.pms.member.gateway.controller.spm.coupon;


import com.ly.titc.common.entity.Response;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.member.gateway.converter.CouponConfigConverter;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.SaveCouponConfigRequest;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.CouponConfigResponse;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.coupon.CouponConfigDecorator;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.config.GetBlocCouponConfigReq;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.config.SaveBlocCouponConfigReq;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.config.BlocCouponConfigResp;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 优惠券配置
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-14
 */
@Slf4j
@RestController
@RequestMapping("/coupon/config")
public class CouponConfigController {

    @Resource(type = CouponConfigDecorator.class)
    private CouponConfigDecorator couponConfigDecorator;
    @Resource(type = CouponConfigConverter.class)
    private CouponConfigConverter couponConfigConverter;


    /**
     * 保存集团优惠券相关配置
     */
    @PostMapping("/save")
    public Response<String> saveBlocConfig(@RequestBody @Valid SaveCouponConfigRequest request){
        SaveBlocCouponConfigReq req = couponConfigConverter.convertSaveBlocCouponConfigReq(request);
        UserInfoDto user = UserInfoThreadHolder.getUser();
        req.setBlocCode(user.getBlocCode());
        req.setOperator(user.getUserName());
        couponConfigDecorator.saveBlocConfig(req);
        return Response.success();
    }

    /**
     * 查询集团优惠券相关配置
     */
    @PostMapping("/detail")
    public Response<CouponConfigResponse> getBlocConfig(){
        GetBlocCouponConfigReq req = new GetBlocCouponConfigReq();
        req.setBlocCode(UserInfoThreadHolder.getUser().getBlocCode());
        req.setTrackingId(UserThreadHolder.getTrackingId());
        BlocCouponConfigResp dto = couponConfigDecorator.getBlocConfig(req);
        return Response.success(couponConfigConverter.convertCouponConfigResponse(dto));
    }
}
