package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-12 14:27
 */
@Data
@Accessors(chain = true)
public class CouponApplicableRatePlanResponse {

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 价格code
     */
    private String ratePlanCode;

    /**
     * 价格方案名称
     */
    private String ratePlanName;
}
