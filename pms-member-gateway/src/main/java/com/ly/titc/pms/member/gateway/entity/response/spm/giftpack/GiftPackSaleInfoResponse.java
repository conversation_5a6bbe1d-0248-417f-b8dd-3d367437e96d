package com.ly.titc.pms.member.gateway.entity.response.spm.giftpack;

import com.ly.titc.pms.member.gateway.entity.response.spm.ApplicableHotelInfoResponse;
import lombok.Data;

import java.math.BigDecimal;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-22
 */
@Data
public class GiftPackSaleInfoResponse{

    /**
     * 售卖金额
     */
    private BigDecimal saleAmount;

    /**
     * 礼包售卖是否允许积分支付 0-不允许 1-允许
     */
    private Integer isAllowPointsPay;

    /**
     * 是否选择线上平台 true-选择 false-未选择
     */
    private Boolean isSelectedOnlineSalePlatform;

    /**
     * 线上平台 2-微信订房 4-艺龙会
     */
    private Integer onlineSalePlatform;

    /**
     * 线上平台待支付金额
     */
    private BigDecimal onlineSaleAmount;

    /**
     * 线上平台待支付积分
     */
    private Integer onlineSalePoints;

    /**
     * 是否选择售卖线下门店 true-选择 false-未选择
     */
    private Boolean isSelectedOfflineSaleHotel;

    /**
     * 线下售卖门店
     */
    private ApplicableHotelInfoResponse saleHotelInfos;


}
