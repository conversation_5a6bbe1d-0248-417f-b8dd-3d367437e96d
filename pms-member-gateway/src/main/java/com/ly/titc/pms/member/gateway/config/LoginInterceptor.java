package com.ly.titc.pms.member.gateway.config;


import com.ly.titc.oauth.client.utils.TokenUtil;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.servlet.HandlerInterceptor;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.stream.Collectors;



/**
 * @program: cms-parent
 * @description:
 * @author: shawn
 * @create: 2021-09-07 14:40
 */
@Slf4j
@Component
public class LoginInterceptor implements HandlerInterceptor {
    /**
     * 不需要登录的资源相对地址
     * eg： /common/*
     */
    private List<String> noNeedLoginRelationPath = new ArrayList<>();


    /**
     * 需要登陆,但不需要校验token有效性的资源相对地址
     * eg: /logout
     */
    private List<String> noNeedValidTokenRelationPath = new ArrayList<>();

    /**
     * 模糊匹配后缀
     */
    private static final String URL_FUZZY_MATCH = "/*";

    /**
     * 登录拦截
     *
     * @param request  request
     * @param response response
     * @param handler  handler
     * @return boolean
     * @throws Exception e
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        String trackingId = String.format(CommonConstant.TRACKING_PREFIX, UUID.randomUUID().toString());
        //0.判断是否是不需要登录的接口,如果是直接返回true
        String uri = request.getRequestURI();
        String contentPath = request.getContextPath();
        if (uri.startsWith(contentPath + "/")) {
            // 去掉contextPath
            uri = uri.replace(contentPath + "/", "/");
        }
        if (isExcludeLoginUrl(uri)) {
            //不需要登录,直接返回,可以访问资源
            log.info("资源{}不需要登录,直接访问,trackingId:{}", uri, trackingId);
            return true;
        }
        String newAccessToken = TokenUtil.getNewAccessToken(request);
        // 新服务不处理
        if (StringUtils.isEmpty(newAccessToken) ) {
            log.info("sso1.0拦截器处理,存在2.0标识,走新逻辑处理,trackingId:{}", "");
            response.setStatus(401);
            return false;

        }
        return true;

    }

    @Override
    public void postHandle(HttpServletRequest request, HttpServletResponse response, Object handler, ModelAndView modelAndView) {

    }

    /**
     * 拦截
     *
     * @param request  request
     * @param response response
     * @param handler  handler
     * @param ex       ex
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {

    }

    /**
     * 检查接口资源是否需要登录
     *
     * @param uri uri
     * @return true 需要登录的uri   false 不需要登录的uri
     */
    private boolean isExcludeLoginUrl(String uri) {

        if (CollectionUtils.isEmpty(noNeedLoginRelationPath)) {
            return false;
        }
        Map<Boolean, List<String>> map = noNeedLoginRelationPath.stream()
                .collect(Collectors.partitioningBy(u -> u.endsWith(URL_FUZZY_MATCH)));
        return checkUri(uri, map);
    }

    /**
     * 新增不需要登陆校验的资源相对地址（批量）
     *
     * @param pathList pathList
     */
    public void addAllNoNeedLoginRelationPath(List<String> pathList) {
        noNeedLoginRelationPath.addAll(pathList);
    }
    /**
     * 新增需要登陆但不需要校验token的资源相对地址（批量）
     *
     * @param pathList pathList
     */
    public void addAllNoNeedValidTokenRelationPath(List<String> pathList) {
        noNeedValidTokenRelationPath.addAll(pathList);
    }

    /**
     * check uri
     *
     * @param uri        资源uri
     * @param excludeMap 校验map
     * @return boolean
     */
    private Boolean checkUri(String uri, Map<Boolean, List<String>> excludeMap) {

        List<String> urls = excludeMap.get(false);
        // 优先精确匹配
        if (urls.contains(uri)) {
            return true;
        }
        urls = excludeMap.get(true);
        // 再进行模糊匹配
        for (String matchUrl : urls) {
            if (uri.startsWith(matchUrl.replace(URL_FUZZY_MATCH, ""))) {
                return true;
            }
        }
        return false;
    }


}
