package com.ly.titc.pms.member.gateway.entity.request.order;

import com.ly.titc.cashier.dubbo.enums.PayProductEnum;
import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.com.enums.AmountTypeEnum;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.order.PayAccountItemRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Digits;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 16:31
 */
@Data
@Accessors(chain = true)
public class PayOrderRequest  extends BaseRequest {
    /**
     * 会员订单号
     */
    @NotEmpty(message = "会员订单号不能为空")
    private String memberOrderNo;

    /**
     * 支付渠道
     * 扫码付 积分 和储值卡支付非必填
     * WECHAT 微信 ALIPAY 支付宝 PosCARD POS银行卡 MemberCard 会员储值卡
     * @see com.ly.titc.cashier.dubbo.enums.PayChannelEnum
     */
    private String payChannel;

    /**
     *支付产品
     * microPay 条码支付 slotCardPay 刷卡支付 point 积分
     */
    @NotEmpty(message = "支付产品不能为空")
    @LegalEnum(target = PayProductEnum.class, methodName = "getCode", message = "支付产品不合法")
    private String payProduct;

    /**
     * 售价类型
     */
    @NotEmpty(message = "售价类型不能为空")
    @LegalEnum(target = AmountTypeEnum.class,methodName = "getType",message = "售价类型不合法")
    private String amountType;

    /**
     * 金额 （保留到分）
     */
    @NotNull(message = "支付金额不能为空")
    private BigDecimal amount;

    /**
     *卡号（银行卡号/会员卡号）
     * 信用卡，银行卡，会员卡支付时传入
     */
    private String cardNo;


    /**
     *扫码支付授权码，设备读取用户的条码或者二维码信息（非pos机场景下必传）
     */
    private String authCode;

    /**
     * 终端ID
     * POS支付场景下必传
     */
    private String termId;

    /**
     * 是否是POS机支付，默认0
     * 1：POS机支付 0：非POS机支付
     */
    @NotNull(message = "是否是POS机支付不能为空")
    private Integer isUsePos =0;

    /**
     * 银行卡类型 POS机支付银卡卡方式支付必传
     * 银行卡类型 unioncard(国内卡)；wildcard(外卡)，预授权交易只支持国内卡刷卡预授权
     */
    private String cardType;

    /**
     * 账务信息 （CRM暂时没有账相关）
     */
    private PayAccountItemRequest accountItem;

}
