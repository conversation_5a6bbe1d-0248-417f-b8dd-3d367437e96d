package com.ly.titc.pms.member.gateway.entity.response.member.profile;

import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：MemberProfileTagResponse
 * @Date：2024-11-18 16:16
 * @Filename：MemberProfileTagResponse
 */
@Data
public class MemberProfileTagResponse {

    /**
     * 标签编码
     */
    private String tagNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 标签分类
     */
    private Integer tagType;

    /**
     * 打标分类: 1:手动标记 2:系统标记
     */
    private Integer markType;

    /**
     * 标签ID
     */
    private String tagId;

    /**
     * 标签名称
     */
    private String tagName;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;
}
