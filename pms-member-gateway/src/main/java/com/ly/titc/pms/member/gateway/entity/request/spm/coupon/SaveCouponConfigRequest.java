package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;

/**
 * @author: shawn
 * @email: <EMAIL>
 * @date: 2024/11/5 13:36
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveCouponConfigRequest extends BaseRequest {

    /**
     * 短信通知 0-不支持 1-支持
     */
    @NotNull(message = "短信通知设置不能为空")
    private Integer smsNotification;

    /**
     * 配置编号
     */
    private String configNo;

}
