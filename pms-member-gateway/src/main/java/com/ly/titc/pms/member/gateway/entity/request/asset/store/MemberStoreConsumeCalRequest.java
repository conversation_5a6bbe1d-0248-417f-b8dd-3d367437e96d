package com.ly.titc.pms.member.gateway.entity.request.asset.store;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 储值消费
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-20 10:33
 */
@Data
@Accessors(chain = true)
public class MemberStoreConsumeCalRequest extends BaseRequest {
    @NotEmpty(message = "会员号不能为空")
    private String memberNo;

    /**
     * 本次消费金额
     */
    @NotNull(message = "本次消费金额不能为空")
    private BigDecimal amount;

}
