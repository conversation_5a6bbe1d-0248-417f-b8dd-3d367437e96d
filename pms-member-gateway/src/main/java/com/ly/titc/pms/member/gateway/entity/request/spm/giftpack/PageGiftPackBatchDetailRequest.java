package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-29 10:45
 */
@Data
@Accessors(chain = true)
public class PageGiftPackBatchDetailRequest extends PageBaseRequest {


    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 发放批次号
     */
    @NotBlank(message = "发放批次号不能为空")
    private String grantBatchNo;


}
