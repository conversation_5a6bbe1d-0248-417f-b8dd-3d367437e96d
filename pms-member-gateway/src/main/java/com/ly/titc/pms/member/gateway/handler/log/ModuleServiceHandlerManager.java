package com.ly.titc.pms.member.gateway.handler.log;

import org.springframework.util.ObjectUtils;

import java.util.HashMap;
import java.util.Map;


/**
 * @Author：rui
 * @name：AbstractModuleServiceHandler
 * @Date：2023-11-21 17:39
 * @Filename：AbstractModuleServiceHandler
 */
public class ModuleServiceHandlerManager {

	private static Map<Integer, AbstractModuleServiceHandler> instance = new HashMap<>();

	/**
	 * put
	 *
	 * @param moduleId 模块id
	 * @param handler  处理类
	 */
	public static void putInstance(Integer moduleId, AbstractModuleServiceHandler handler) {

		instance.put(moduleId, handler);
	}

	/**
	 * get
	 *
	 * @param moduleId
	 * @return
	 */
	public static AbstractModuleServiceHandler getInstance(Integer moduleId) {

		if (ObjectUtils.isEmpty(moduleId)) {
			return null;
		}
		return instance.get(moduleId);
	}
}
