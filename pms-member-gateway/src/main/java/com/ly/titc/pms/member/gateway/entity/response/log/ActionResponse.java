package com.ly.titc.pms.member.gateway.entity.response.log;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：rui
 * @name：ActionResponse
 * @Date：2023-11-21 17:38
 * @Filename：ActionResponse
 */
@Data
@Accessors(chain = true)
public class ActionResponse {

    /**
     * 行为id
     */
    private Integer actionId;

    /**
     * 行为名称
     */
    private String actionName;

}
