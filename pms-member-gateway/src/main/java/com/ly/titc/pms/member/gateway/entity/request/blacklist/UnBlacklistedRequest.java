package com.ly.titc.pms.member.gateway.entity.request.blacklist;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：UnBlacklistedRequest
 * @Date：2024-12-10 20:07
 * @Filename：UnBlacklistedRequest
 */
@Data
public class UnBlacklistedRequest extends BaseRequest {

    /**
     * 会员编号编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 黑名单编号
     */
    @NotBlank(message = "黑名单编号不能为空")
    private String blacklistNo;
}
