package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-4 19:53
 */
@Data
@Accessors(chain = true)
public class CouponListResponse {
    /**
     * 券编号
     */
    private String couponCode;

    /**
     * 券类型
     */
    private Integer couponType;

    /**
     * 券类型名
     */
    private String couponTypeName;

    /**
     * 券名称
     */
    private String couponName;

    /**
     * 券优惠
     */
    private String couponValue;

    /**
     * 生成门店
     * 中央生成 or  xxx酒店
     */
    private String hotelName;

    /**
     * 发放事件
     */
    private String issueEvent;

    /**
     * 发放日期
     */
    private String issueDate;

    /**
     * 券生效日期类型 1.固定日期区间; 2.按领取日期（自领取后按天算）
     */
    private Integer effectDateType;

    /**
     * 开始时间
     */
    private String startDate;

    /**
     * 过期时间
     * 若永久有效，则2099-12-31
     */
    private String endDate;

    /**
     * 是否永久有效
     */
    private boolean isLongTerm;

    /**
     * 券模板code
     */
    private String templateCode;



    /**
     * 领取后多少天开始生效 0-立即生效
     */
    private Integer effectFixStartDay;

    /**
     * 领取后多少天有效
     */
    private Integer effectFixEndDay;


    /**
     * 状态
     */
    private Integer state;

    /**
     * 所属人名称
     */
    private String owner;


    /**
     * 使用时间
     */
    private String useTime;

}
