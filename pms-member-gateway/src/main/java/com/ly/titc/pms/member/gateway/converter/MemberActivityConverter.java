package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.pms.member.gateway.entity.request.spm.activity.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.activity.*;
import com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity.QueryMemberActiveActivityDto;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.ModifyEventActivityReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.PageEventActivitiesReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.QueryEventActivitiesReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.QueryEventActivityReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.member.*;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityListResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.member.MemberCardSaleActivityDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.member.MemberPointsActivityDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.member.MemberRechargeActivityDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.member.MemberUpgradeActivityDetailResp;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

/**
 * 会员活动converter
 *
 * <AUTHOR>
 * @date 2024/12/31 14:25
 */
@Mapper(componentModel = "spring", builder = @Builder(disableBuilder = true))
public interface MemberActivityConverter {

    @Mappings({
            @Mapping(target = "currPage" , source = "pageIndex")
    })
    PageEventActivitiesReq convertRequestToReq(PageActivityRequest request);

    EventActivityListResponse convertRespToResponse(EventActivityListResp record);

    ModifyEventActivityReq convertRequestToReq(QueryEventActivityRequest request);

    @Mappings({
            @Mapping(target = "carriers" , source = "carriers")
    })
    SaveSaleCardActivityReq convertSaleCardActivityReq(SaveSaleCardActivityRequest request);

    @Mappings({
            @Mapping(target = "carriers" , source = "carriers")
    })
    SaveUpgradeActivityReq convertUpgradeActivityReq(SaveUpgradeActivityRequest request);

    @Mappings({
            @Mapping(target = "carriers" , source = "carriers")
    })
    SaveRechargeActivityReq convertRechargeActivityReq(SaveRechargeActivityRequest request);

    SavePointsActivityReq convertRechargeActivityReq(SavePointsActivityRequest request);

    GetMemberActivityReq convertGetMemberActivityReq(QueryEventActivityRequest request);

    MemberSaleCardActivityDetailResponse convertSaleCardActivityResp(MemberCardSaleActivityDetailResp activity);

    MemberUpgradeActivityDetailResponse convertUpgradeActivityResp(MemberUpgradeActivityDetailResp activity);

    MemberRechargeActivityDetailResponse convertRechargeActivityResp(MemberRechargeActivityDetailResp activity);

    MemberPointsActivityDetailResponse convertPointsActivityResp(MemberPointsActivityDetailResp activity);

    QueryEventActivitiesReq convertBase(QueryMemberActiveActivityRequest request);

    default QueryEventActivitiesReq convertQueryReq(QueryMemberActiveActivityRequest request){
        QueryEventActivitiesReq req = convertBase(request);
        req.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        req.setScopeSource(ScopeSourceEnum.BLOC.getCode());
        QueryEventActivitiesReq.UserInfo userInfo = new QueryEventActivitiesReq.UserInfo();
        userInfo.setMemberPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
//        userInfo.setMemberCardCode(request.getCardId());
        req.setUserInfo(userInfo);
        return req;
    }

    default QueryMemberActiveActivityDto convertQueryDto(QueryMemberActiveActivityRequest request){
        QueryMemberActiveActivityDto dto = convertQueryBaseDto(request);
        dto.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        dto.setScopeSource(ScopeSourceEnum.BLOC.getCode());
        return dto;
    }

    QueryMemberActiveActivityDto convertQueryBaseDto(QueryMemberActiveActivityRequest request);


}
