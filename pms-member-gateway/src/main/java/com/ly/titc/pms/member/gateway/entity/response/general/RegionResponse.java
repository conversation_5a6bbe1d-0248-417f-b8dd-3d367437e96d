package com.ly.titc.pms.member.gateway.entity.response.general;

import com.ly.titc.mdm.entity.response.region.RegionResp;
import lombok.Data;

import java.util.List;

/**
 * 区域信息
 *
 * <AUTHOR>
 * @date 2024/12/17 16:13
 */
@Data
public class RegionResponse {

    /**
     * 省/市/区pid
     */
    private Integer pid;

    /**
     * 省/市/区id
     */
    private Integer id;

    /**
     * 级别 0洲1国2省3市4区5乡6村
     */
    private Integer level;

    /**
     * 0:正常级别(国，省，市，县，乡镇) 1:直辖市 2:自治区 3:特别行政区 4:自治州 5:地区 6:县级市 7:行政区 8:自治县 9:省直辖行政单位 10:市直辖
     */
    private Integer type;

    /**
     * 省/市/区名称
     */
    private String name;

    /**
     * 省/市/区简称
     */
    private String shortName;

    /**
     * 省/市/区 英文名称
     */
    private String enName;

    /**
     * 省/市/区 中文拼音
     */
    private String chinesePinyin;

    /**
     * children
     */
    private List<RegionResponse> children;

}
