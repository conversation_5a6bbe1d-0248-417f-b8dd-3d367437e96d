package com.ly.titc.pms.member.gateway.entity.request.general;

import com.ly.titc.common.constants.Constant;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * 根据pid查询
 *
 * <AUTHOR>
 * @date 2024/12/17 16:10
 */
@Data
public class ListRegionByLevelRequest {

    /**
     * 省/市/区 名称
     */
    private String name;

    /**
     * 级别 0洲1国2省3市4区5乡6村
     */
    private Integer level;

    /**
     * 0:正常级别(国，省，市，县，乡镇) 1:直辖市 2:自治3区 :特别行政区 4:自治州 5:地区 6:县级市 7:行政区 8:自治县 9:省直辖行政单位 10:市直辖
     */
    private Integer type;

    /**
     * pid
     */
    private Integer pid;

}
