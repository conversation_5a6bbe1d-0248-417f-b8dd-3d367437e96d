package com.ly.titc.pms.member.gateway.entity.request.blacklist;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：QueryBlacklistedRequest
 * @Date：2024-12-10 20:12
 * @Filename：QueryBlacklistedRequest
 */
@Data
public class QueryBlacklistedRequest extends BaseRequest {

    /**
     * 会员编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 1 生效 0 取消
     */
    @NotNull(message = "状态不能为空")
    private Integer state;
}
