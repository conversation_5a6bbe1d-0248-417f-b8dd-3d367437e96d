package com.ly.titc.pms.member.gateway.entity.request.cashier;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-10 10:49
 */
@Data
@Accessors
public class RequestBodyPayCashierConfigSaveRequest extends BaseRequest {
    /**
     * 编辑时必填
     */
    private Long id;
    /**
     * 收银产品 PMSPAY(PMS相关收银业务),WXBOOKINGJSPAY(微订房公众号支付),WXBOOKINGAPPLETPAY(微订房小程序支付)
     */
    @NotEmpty(message = "收银产品不能为空")
    private String cashierProductCode;

    /**
     * 收银来源 PUB(集团组) BLOC (集团)，HOTEL(门店)
     * @see com.ly.titc.cashier.dubbo.enums.CashierSourceEnum
     */
    private List<String> cashierSources;


    /**
     * 配置的门店范围 1 全部门店 2 指定门店
     * 如果来源是酒店 该字段必填
     *  @see com.ly.titc.cashier.dubbo.enums.CashierHotelScopeEnum
     */
    private Integer cashierHotelScope;
    /**
     * 收银场景 MEMBER_REGISTER(会员注册),MEMBER_PURCHASE_CARD(会员购卡),MEMBER_RECHARGE(会员充值).....
     * @see com.ly.titc.cashier.dubbo.enums.CashierSceneEnum
     *
     */
    private List<String> cashierScenes;

    /**
     * 指定门店范围
     * 把酒店编号传入
     */
    private List<String> cashierScopeHotelCodes;

    /**
     * 真实收款方 1集团 2 门店 3 优先门店收款
     * @see com.ly.titc.cashier.dubbo.enums.CashierPayeeTypeEnum
     */
    private Integer payeeType;

    /**
     * 外显支付渠道 AGGPAY聚合支付,银行卡（POSCARD）,CASH(现金), CHARGE(挂账)
     * @see com.ly.titc.cashier.dubbo.enums.CashierDisplayPayChannelEnum
     */
    private List<String> displayPayChannels;
}
