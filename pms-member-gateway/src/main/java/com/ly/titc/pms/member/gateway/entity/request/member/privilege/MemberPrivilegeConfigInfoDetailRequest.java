package com.ly.titc.pms.member.gateway.entity.request.member.privilege;

import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberPrivilegeConfigInfoDetailRequest
 * @Date：2024-11-14 18:04
 * @Filename：MemberPrivilegeConfigInfoDetailRequest
 */
@Data
public class MemberPrivilegeConfigInfoDetailRequest {

    /**
     * 权益ID
     */
    private Long privilegeId;

    /**
     * 权益类型 1.仅作展示 2 价格折扣 3 预定保留 4 延迟退房，冗余存储
     */
    private Integer classification;

    /**
     * 适用类型 1 集团 2 品牌 3 门店 这里固定是3
     */
    private Integer applicationType = 3 ;

    /**
     * 适用范围 0 部分 1 全部
     */
    private Integer scopeType;

    /**
     * 适用范围值，全部时不传
     */
    private List<String> scopeValues = new ArrayList<>();

    /**
     * 权益值 折扣 9.5  时间 12:00
     */
    private String value;
}
