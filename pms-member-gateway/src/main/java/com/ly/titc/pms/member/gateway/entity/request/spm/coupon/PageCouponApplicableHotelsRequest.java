package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-12 14:17
 */
@Data
@Accessors(chain = true)
public class PageCouponApplicableHotelsRequest extends PageBaseRequest {

    @NotBlank(message = "券号不能为空")
    private String couponCode;

    /**
     * 酒店编号或名字
     */
    private String hotelInput;

}
