package com.ly.titc.pms.member.gateway.entity.response.menu;


import lombok.Data;

import java.util.List;

/**
 * @Description: 菜单返回
 * @Author: lixu
 * @Date: 2022/7/12
 */
@Data
public class MenuResponse {

    /**
     * 树状的菜单接口
     */
    private List<MenuInfoResponse> menuTrees;

    /**
     * 页面集合
     */
    private List<MenuInfoResponse> pages;

    /**
     * 所有页面+按钮资源的集合
     */
    private List<String> urls;
}
