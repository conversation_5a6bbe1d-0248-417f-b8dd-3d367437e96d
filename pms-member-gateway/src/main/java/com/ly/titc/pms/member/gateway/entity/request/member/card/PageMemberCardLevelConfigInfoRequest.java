package com.ly.titc.pms.member.gateway.entity.request.member.card;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;

/**
 * @Author：rui
 * @name：PageMemberCardLevelConfigInfoRequest
 * @Date：2024-11-12 17:48
 * @Filename：PageMemberCardLevelConfigInfoRequest
 */
@Data
public class PageMemberCardLevelConfigInfoRequest extends PageBaseRequest {

    /**
     * 卡id
     */
    private String cardId;

    /**
     * 等级
     */
    private Integer level;

    /**
     * 状态
     */
    private Integer state;
}
