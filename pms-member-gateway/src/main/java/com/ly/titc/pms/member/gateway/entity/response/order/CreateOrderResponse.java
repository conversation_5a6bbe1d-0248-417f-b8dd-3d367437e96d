package com.ly.titc.pms.member.gateway.entity.response.order;

import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 13:49
 */
@Data
public class CreateOrderResponse {
    /**
     * 会员订单号
     */
    private String memberOrderNo;
    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员业务场景
     */
    private String memberScene;

    /**
     * 会员业务场景描述
     */
    private String memberSceneDesc;

    /**
     * 金额
     */
    private BigDecimal amount;
    /**
     * 售价类型
     */
    private String amountType;
}
