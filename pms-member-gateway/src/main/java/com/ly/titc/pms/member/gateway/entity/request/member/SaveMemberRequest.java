package com.ly.titc.pms.member.gateway.entity.request.member;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.*;
import lombok.Data;

/**
 * @Author：rui
 * @name：SaveMemberRequest
 * @Date：2024-11-18 18:03
 * @Filename：SaveMemberRequest
 */
@Data
public class SaveMemberRequest extends BaseRequest {

    /**
     * 会员基础信息
     */
    private MemberInfoRequest memberInfo;

    /**
     * 会员拓展信息
     */
    private MemberExtendInfoRequest memberExtendInfo;

    /**
     * 会员联系信息
     */
    private MemberContactInfoRequest memberContactInfo;


}
