package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.BlacklistCustomerReq;
import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.CancelBlacklistCustomerReq;
import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.CheckWhetherBlacklistReq;
import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.PageBlacklistParamReq;
import com.ly.titc.pms.customer.dubbo.entity.request.checkIn.GetStatisticsByCustomerNoReq;
import com.ly.titc.pms.customer.dubbo.entity.request.checkIn.PageCheckInRecordParamReq;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.*;
import com.ly.titc.pms.customer.dubbo.entity.response.blacklist.BlacklistInfoResp;
import com.ly.titc.pms.customer.dubbo.entity.response.checkIn.CustomerCheckInRecordResp;
import com.ly.titc.pms.customer.dubbo.entity.response.checkIn.CustomerCheckInStatisticsResp;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerDetailInfoResp;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerInfoResp;
import com.ly.titc.pms.member.gateway.entity.request.customer.*;
import com.ly.titc.pms.member.gateway.entity.response.customer.CustomerBlacklistResponse;
import com.ly.titc.pms.member.gateway.entity.response.customer.CustomerCheckInStatisticsResponse;
import com.ly.titc.pms.member.gateway.entity.response.customer.CustomerDetailInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.customer.CustomerInfoResponse;
import org.mapstruct.Mapper;

/**
 * 客户converter
 *
 * <AUTHOR>
 * @date 2024/12/11 19:18
 */
@Mapper(componentModel = "spring")
public interface CustomerConverter {
    PageCustomerReq convertPageCustomerReq(PageCustomerRequest request);

    CustomerInfoResponse convertRespToResponse(CustomerInfoResp customerInfo);

    GetByCustomerNoReq convertGetCustomerReq(GetByCustomerNoRequest request);

    CustomerDetailInfoResponse convertRespToResponse(CustomerDetailInfoResp customerDetailInfo);

    CheckWhetherBlacklistReq convertBlacklistReq(GetByCustomerNoRequest request);

    CheckExistByMobileReq convertCheckExistReq(CheckExistByMobileRequest request);

    RegisterCustomerReq convertRegisterReq(RegisterCustomerRequest request);

    UpdateCustomerReq convertUpdateReq(UpdateCustomerRequest request);

    GetStatisticsByCustomerNoReq convertGetStatisticsReq(GetByCustomerNoRequest request);

    CustomerCheckInStatisticsResponse convertStatisticsResp(CustomerCheckInStatisticsResp statisticsInfo);

    PageCheckInRecordParamReq convertGetCheckInRecordReq(PageCheckInRecordRequest request);

    CustomerCheckInStatisticsResponse convertRecordResp(CustomerCheckInRecordResp record);

    PageBlacklistParamReq convertPageBlacklistReq(ListBlacklistInfoRequest request);

    CustomerBlacklistResponse convertBlacklistResponse(BlacklistInfoResp blacklistInfoResp);

    CancelBlacklistCustomerReq convertCancelBlacklist(CancelBlacklistCustomerRequest request);

    BlacklistCustomerReq convertBlacklist(BlacklistCustomerRequest request);
}
