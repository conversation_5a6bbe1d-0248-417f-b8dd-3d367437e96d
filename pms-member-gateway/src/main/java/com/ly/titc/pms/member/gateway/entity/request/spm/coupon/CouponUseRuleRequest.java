package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.spm.ApplicableHotelInfoRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.ApplicableMemberLevelRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.ApplicableHotelRatePlanInfoRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.ApplicableHotelRoomTypeInfoRequest;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-5
 */
@Data
public class CouponUseRuleRequest {

    /**
     * 当晚房费最低价格 0-不限
     */
    private BigDecimal roomMinPrice;

    /**
     * 订单房费最低价格 0-不限
     */
    private BigDecimal orderMinPrice;

    /**
     * 提前预订天数 0-不限
     */
    private Integer aheadReserveDay;

    /**
     * 是否叠加 false-不叠加 true-叠加
     */
    private Boolean isStacking;

    /**
     * 不适用星期
     */
    private List<String> unApplicableWeekInfos;

    /**
     * 适用时间段
     */
    private List<CouponTimeBucketInfoRequest> unApplicableTimeBucketInfos;

    /**
     * 适用渠道信息
     */
    private List<String> channelInfos;

    /**
     * 适用酒店范围
     */
    private ApplicableHotelInfoRequest hotelInfos;

    /**
     * 适用会员范围
     */
    private ApplicableMemberLevelRequest memberLevelInfos;

    /*
     * 适用集团价格方案
     */
    private List<String> blocApplicableRatePlanInfos;

    /*
     * 适用酒店价格方案
     */
    private ApplicableHotelRatePlanInfoRequest hotelApplicableRatePlanInfos;

    /*
     * 适用集团房型
     */
    private List<String> blocApplicableRoomTypeInfos;

    /*
     * 适用酒店房型
     */
    private ApplicableHotelRoomTypeInfoRequest hotelApplicableRoomTypeInfos;

}
