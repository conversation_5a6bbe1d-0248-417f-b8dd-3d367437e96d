package com.ly.titc.pms.member.gateway.entity.response.member;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberCardLevelRelegationRuleResponse
 * @Date：2024-11-13 11:03
 * @Filename：MemberCardLevelRelegationRuleResponse
 */
@Data
@Accessors(chain =true)
public class MemberCardLevelRelegationRuleResponse {

    /**
     * id
     */
    private Long id;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 规则名称
     */
    private String name;

    /**
     * 会员卡模版名称
     */
    private String cardName;

    /**
     * 原等级
     */
    private Integer sourceLevel;

    /**
     * 原等级名称
     */
    private String sourceLevelName;

    /**
     * 目标等级
     */
    private Integer targetLevel;

    /**
     * 目标等级名称
     */
    private String targetLevelName;

    /**
     * 描述
     */
    private String description;

    /**
     * 状态 0 无效 1 正常
     */
    private Integer state;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 统计逻辑：ALL-全部条件;ANY-满足任一个条件
     */
    private String successfulPerformType;

    /**
     * 统计周期
     */
    private Integer cycleType;


    private List<MemberCardLevelRelegationRuleDetailResponse> details;
}
