package com.ly.titc.pms.member.gateway.entity.response.spm.activity;

import com.ly.titc.pms.spm.dubbo.entity.dto.activity.common.MemberRechargeActivityGearsDto;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * 会员充值活动
 *
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-17 20:15
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class MemberRechargeActivityDetailResponse extends MemberActivityCommonResponse {

    /**
     * 充值
     */
    private List<MemberRechargeActivityGearsDto> carriers;

    /**
     * 礼金有效期
     */
    private Integer storeLimit;

    /**
     * 礼金有效期单位 年 YEAR 月:MONTH 日; DAY
     */
    private String storeLimitUnit;

    /**
     * 是否永久有效 true-永久有效 false-非永久有效（固定日期区间）
     */
    private Boolean isPerpetualEffect;
}
