package com.ly.titc.pms.member.gateway.entity.response.customer;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 客户入住统计
 *
 * <AUTHOR>
 * @date 2024/12/13 14:46
 */
@Data
public class CustomerCheckInStatisticsResponse {

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 酒店编号
     */
    private String hotelCode;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 总入住次数
     */
    private Integer checkInCount = 0;

    /**
     * 总入住间夜
     */
    private Integer checkInNights = 0;

    /**
     * 总消费金额
     */
    private BigDecimal expenseAmount;

    /**
     * 间夜均价
     */
    private BigDecimal averagePrice;

    /**
     * 上一次房价
     */
    private BigDecimal lastRoomRate;

    /**
     * 上一次离店时间
     */
    private String lastCheckOutDate;


}
