package com.ly.titc.pms.member.gateway.config;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.cc.sdk.log.annotation.LogRecord;
import com.ly.titc.cc.sdk.log.context.LogRecordContext;
import com.ly.titc.cc.sdk.log.holder.LogRecordContextHolder;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.IpUtil;
import com.ly.titc.oauth.client.entity.dto.UserDto;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.member.com.annotation.OperationLog;
import com.ly.titc.pms.member.gateway.entity.response.RegisterMemberResultResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.core.LocalVariableTableParameterNameDiscoverer;
import org.springframework.core.annotation.Order;
import org.springframework.expression.Expression;
import org.springframework.expression.spel.standard.SpelExpressionParser;
import org.springframework.expression.spel.support.StandardEvaluationContext;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestContextHolder;
import org.springframework.web.context.request.ServletRequestAttributes;

import javax.servlet.http.HttpServletRequest;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationHandler;
import java.lang.reflect.Method;
import java.lang.reflect.Proxy;
import java.util.HashMap;
import java.util.Map;
import java.util.Objects;

/**
 * @Author：rui
 * @name：CommonLogAspect
 * @Date：2023-11-22 17:41
 * @Filename：CommonLogAspect
 */
@Component
@Aspect
@Slf4j
@Order(3)
public class CommonLogAspect {

    @Pointcut("@annotation(com.ly.titc.pms.member.com.annotation.OperationLog)")
    public void operation() {
    }


    /**
     * 拦截请求
     *
     * @param joinPoint
     * @return
     * @throws Throwable
     */
    @Around("operation()")
    public Object OperateLogHandler(ProceedingJoinPoint joinPoint) throws Throwable {
        String trackingId = UserThreadHolder.getTrackingId();
        HttpServletRequest req = ((ServletRequestAttributes) RequestContextHolder.getRequestAttributes()).getRequest();
        //得到请求ip
        String clientIp = IpUtil.getClientIp(req);
        log.info("操作日志开始记录,trackingId:{}", trackingId);
        String record = parseExpression(joinPoint);
        if (StringUtils.isNotEmpty(record)) {
            initContext(record);
            LogRecordContext context = LogRecordContextHolder.getContext();
            context.setOpContent(record).setClientIp(clientIp);
        }
        Object result = joinPoint.proceed();
        handleContextAfterFetchResult(record, result);
        log.info("操作日志记录结束, trackingId:{}", trackingId);
        return result;
    }

    /**
     * 解析注解
     *
     * @param joinPoint
     * @return
     */
    private String parseExpression(ProceedingJoinPoint joinPoint) {
        String trackingId = UserThreadHolder.getTrackingId();
        Signature signature = joinPoint.getSignature();
        MethodSignature methodSignature = (MethodSignature) signature;
        //获取方法
        Method method = methodSignature.getMethod();
        // 获取注解
        OperationLog annotation = method.getAnnotation(OperationLog.class);
        boolean flag = method.isAnnotationPresent(LogRecord.class);
        //获取参数
        Object[] args = joinPoint.getArgs();
        String record = annotation.operateRecord();
        try {
            if (flag) {
                changeCategory(method, args);
            }
            //获取被拦截方法参数名列表
            LocalVariableTableParameterNameDiscoverer discoverer = new LocalVariableTableParameterNameDiscoverer();
            String[] paramNameArr = discoverer.getParameterNames(method);
            //Spel解析
            SpelExpressionParser parser = new SpelExpressionParser();
            StandardEvaluationContext context = new StandardEvaluationContext();
            for (int i = 0; i < paramNameArr.length; i++) {
                context.setVariable(paramNameArr[i], args[i]);
            }
            CustomFunctionRegistrar.register(context);
            if (StringUtils.isNotBlank(record)) {
                Expression expression = parser.parseExpression(record);
                record = (String) expression.getValue(context, Object.class);
            }
            return record;
        } catch (Exception e) {
            log.info("操作日志el表达式解析出错, trackingId:{}, exception:{}", trackingId, e);
        } finally {
            return record;
        }
    }

    /**
     * 切换操作日志种类
     *
     * @param method
     * @param args
     * @throws Exception
     */
    private void changeCategory(Method method, Object[] args) throws Exception {
        String name = method.getName();
        Map<String, Object> map = JSONObject.parseObject(JSONObject.toJSONString(args[0]), Map.class);
        LogRecord logRecordAnnotation = method.getAnnotation(LogRecord.class);

        InvocationHandler handler = Proxy.getInvocationHandler(logRecordAnnotation);
        String category = logRecordAnnotation.category();
        Field declaredField = handler.getClass().getDeclaredField("memberValues");
        declaredField.setAccessible(true);
        Map memberValues = (Map) declaredField.get(handler);
//        if (name.equals("updateHotelRatePlanState") && map.containsKey("state")) {
//            //调整启用、停用
//            if (map.containsKey("state") && StatusEnum.INVALID.getStatus() == ((int) (map.get("state")))) {
//                if (ActionConstants.START_CODE.equalsIgnoreCase(category)) {
//                    memberValues.put("category", ActionConstants.STOP_CODE);
//                    memberValues.put("categoryName", ActionConstants.STOP_NAME);
//                }
//            } else if (map.containsKey("state") && StatusEnum.VALID.getStatus() == ((int) (map.get("state")))) {
//                if (ActionConstants.STOP_CODE.equalsIgnoreCase(category)) {
//                    memberValues.put("category", ActionConstants.START_CODE);
//                    memberValues.put("categoryName", ActionConstants.START_NAME);
//                }
//            }else if (map.containsKey("state") && 2 == ((int) (map.get("state")))) {
//                if (!ActionConstants.DELETE_CODE.equalsIgnoreCase(category)) {
//                    memberValues.put("category", ActionConstants.DELETE_CODE);
//                    memberValues.put("categoryName", ActionConstants.DELETE_NAME);
//                }
//            }
//        } else if (name.equals("saveRatePlan")) {
//            if (map.containsKey("id") && (Objects.nonNull(map.get("id")))) {
//                memberValues.put("category", ActionConstants.SAVE_CODE);
//                memberValues.put("categoryName", ActionConstants.SAVE_NAME);
//            } else {
//                memberValues.put("category", ActionConstants.ADD_CODE);
//                memberValues.put("categoryName", ActionConstants.ADD_NAME);
//            }
//        }
//
//        //处理包价
//        if (method.getDeclaringClass().getName().equals("com.ly.titc.ihotel.pms.gateway.controller.packPrice.PackPriceController") && name.equals("save")) {
//            if (map.containsKey("isCreate") && !(Boolean)map.get("isCreate")) {
//                memberValues.put("category", ActionConstants.PACK_PRICE_UPDATE_CODE);
//                memberValues.put("categoryName", ActionConstants.PACK_PRICE_UPDATE_NAME);
//            } else {
//                memberValues.put("category", ActionConstants.PACK_PRICE_ADD_CODE);
//                memberValues.put("categoryName", ActionConstants.PACK_PRICE_ADD_NAME);
//            }
//        }
    }

    /**
     * 初始化
     */
    private void initContext(String record) {
        LogRecordContextHolder.initContext();
        LogRecordContext context = LogRecordContextHolder.getContext();
        UserDto user = UserThreadHolder.getUser();
        String trackingId = UserThreadHolder.getTrackingId();
        context.setOperator(user.getUserName());
        context.setTrackingId(trackingId);
        try {
            JSONObject json = JSONObject.parseObject(record);
            Map<String, Object> tags = new HashMap<>();
            tags.put("blocCode", json.getString("blocCode"));
            tags.put("hotelCode", json.getString("hotelCode"));
            tags.put("name", json.getString("name"));
            context.setTags(tags);

        } catch (Exception e) {
            log.warn("异步记录日志解析结果异常 trackingId:{},{}", trackingId, e);
        }
    }

    /**
     * 特殊处理会员注册获得会员编号
     */
    private void handleContextAfterFetchResult(String record, Object result) {
        LogRecordContext context = LogRecordContextHolder.getContext();
        Map<String, Object> tags = context.getTags();
        try {
            JSONObject json = JSONObject.parseObject(record);
            if (Objects.nonNull(json.getBoolean("needHandler")) && json.getBoolean("needHandler")) {
                // 特殊处理会员注册
                String s =  json.getString("records");
                if (result instanceof Response) {
                    Response<RegisterMemberResultResponse> response = (Response<RegisterMemberResultResponse>) result;
                    RegisterMemberResultResponse data = response.getData();
                    tags.put("name", data.getMemberNo());
                    s.replace("-todo-memberNo", data.getMemberNo());
                    json.put("records", s);
                    context.setOpContent(json.toJSONString());
                    context.setTags(tags);
                }
            }
        } catch (Exception e) {
            log.warn("异步记录日志解析结果异常{}", e);
        }
    }
}
