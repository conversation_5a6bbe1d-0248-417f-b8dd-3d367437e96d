package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.response.spm.ApplicableHotelInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.ApplicableHotelRatePlanInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.ApplicableHotelRoomTypeInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.ApplicableMemberLevelResponse;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-5
 */
@Data
public class CouponUseRuleResponse {

    /**
     * 当晚房费最低价格 0-不限
     */
    private BigDecimal roomMinPrice;

    /**
     * 订单房费最低价格 0-不限
     */
    private BigDecimal orderMinPrice;

    /**
     * 提前预订天数 0-不限
     */
    private Integer aheadReserveDay;

    /**
     * 是否叠加 false-不叠加 true-叠加
     */
    private Boolean isStacking;

    /**
     * 不适用星期
     */
    private List<String> unApplicableWeekInfos;

    /**
     * 不适用时间段
     */
    private List<CouponTimeBucketInfoResponse> unApplicableTimeBucketInfos;

    /**
     * 适用渠道信息
     */
    private List<String> channelInfos;

    /**
     * 适用酒店范围
     */
    private ApplicableHotelInfoResponse hotelInfos;

    /**
     * 适用会员范围
     */
    private ApplicableMemberLevelResponse memberLevelInfos;

    /*
     * 适用集团价格方案
     */
    private List<String> blocRatePlanInfos;

    /*
     * 适用酒店价格方案
     */
    private ApplicableHotelRatePlanInfoResponse hotelRatePlanInfos;

    /*
     * 适用集团房型
     */
    private List<String> blocRoomTypeInfos;

    /*
     * 适用酒店房型
     */
    private ApplicableHotelRoomTypeInfoResponse hotelRoomTypeInfos;

}
