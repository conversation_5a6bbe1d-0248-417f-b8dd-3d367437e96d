package com.ly.titc.pms.member.gateway.entity.response.spm.giftpack;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-28 16:55
 */
@Data
@Accessors(chain = true)
public class GiftPackBatchListResponse {

    /**
     * 发放批次号
     */
    private String grantBatchNo;

    /**
     * 礼包编码
     */
    private String giftPackNo;

    /**
     * 礼包名
     */
    private String giftPackName;

    /**
     * 发放数量
     */
    private Integer grantNum;

    /**
     * 发放时间
     */
    private String grantDate;

    /**
     * 发放事件
     */
    private String grantEventDesc;

    /**
     * 销售类型
     * 1-免费 2-付费
     */
    private Integer saleType;

    /**
     * 发放对象是否多个
     */
    private boolean multiTarget;

    /**
     * 发放对象
     * multiTarget=true 无值, multiTarget=false 有值
     */
    private String targetDesc;

    /**
     * 礼包版本
     */
    private Integer version;

    /**
     * 发放来源
     */
    private String sourceClient;

    /**
     * 操作人
     */
    private String operator;
}
