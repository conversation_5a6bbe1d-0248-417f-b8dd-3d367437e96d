package com.ly.titc.pms.member.gateway.entity.request.member;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

/**
 * @Author：rui
 * @name：MemberExtendInfoRequest
 * @Date：2024-11-18 20:30
 * @Filename：MemberExtendInfoRequest
 */
@Data
public class MemberExtendInfoRequest {
    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 头像地址
     */
    private String avatar;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 民族
     */
    private String nation;

    /**
     * 车牌号
     */
    private String numberPlate;

    /**
     * 备注
     */
    private String remark;

    /**
     * 语言
     */
    private String language;

    /**
     * 会员来源
     */
    private String source;

    /**
     * 注册门店类型 集团:BLOC;门店:HOTEL
     */
    private String registerHotelType;

    /**
     * 注册门店(集团编号; 酒店编号)
     */
    private String registerHotel;

    /**
     * 销售员
     */
    private String salesman;
}
