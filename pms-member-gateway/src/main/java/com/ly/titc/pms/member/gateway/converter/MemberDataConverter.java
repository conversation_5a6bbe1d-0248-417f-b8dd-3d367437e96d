package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.dal.entity.po.MemberCheckInStatistics;
import com.ly.titc.pms.member.gateway.entity.request.member.record.PageCheckinRecordRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.record.PageLevelChangeRecordRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.record.PagePurchaseCardRecordRequest;
import com.ly.titc.pms.member.gateway.entity.response.data.CheckInStatisticsResponse;
import com.ly.titc.pms.member.gateway.entity.response.data.CheckinRecordResponse;
import com.ly.titc.pms.member.gateway.entity.response.data.MemberLevelChangeRecordResponse;
import com.ly.titc.pms.member.gateway.entity.response.data.PurchaseCardRecordResponse;
import com.ly.titc.pms.member.mediator.entity.dto.data.*;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberDataConverter
 * @Date：2024-12-11 21:29
 * @Filename：MemberDataConverter
 */
@Mapper(componentModel = "spring")
public interface MemberDataConverter extends BaseConverter {

    PageCheckinRecordDto convertPageCheckinRecordDto(PageCheckinRecordRequest request);

    PageLevelChangeRecordDto convertPageLevelChangeRecordDto(PageLevelChangeRecordRequest request, Integer masterType, String masterCode);

    PagePurchaseCardRecordDto convertPagePurchaseCardRecordDto(PagePurchaseCardRecordRequest request, Integer masterType, String masterCode);

    List<CheckinRecordResponse> convertPageCheckinRecord(List<CheckinRecordDto> dto);

    CheckInStatisticsResponse convertCheckInStatistics(CheckInStatisticsDto checkInStatistics);

    List<MemberLevelChangeRecordResponse> convertMemberLevelChangeRecordResponse(List<MemberLevelChangeRecordDto> dto);

    List<PurchaseCardRecordResponse> convertPurchaseCardRecordResponse(List<PurchaseCardRecordDto> dto);
}
