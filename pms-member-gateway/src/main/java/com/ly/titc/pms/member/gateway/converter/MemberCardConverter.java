package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.*;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.*;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.SaveCardLevelUpgradeRuleRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.*;
import com.ly.titc.pms.member.gateway.entity.request.member.privilege.PageMemberPrivilegeRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.privilege.SaveMemberPrivilegeConfigInfoRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.tag.PageMemberTagConfigInfoRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.tag.SaveMemberTagConfigRequest;
import org.apache.commons.collections4.CollectionUtils;
import com.ly.titc.pms.member.gateway.entity.response.SelectResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberCardWebConverter
 * @Date：2024-11-12 19:48
 * @Filename：MemberCardWebConverter
 */
@Mapper(componentModel = "spring")
public interface MemberCardConverter extends BaseConverter {

    @Mappings({
            @Mapping(target = "masterType", source = "masterType"),
            @Mapping(target = "masterCode", source = "request.blocCode"),
    })
    SaveCardConfigReq convertReq(SaveMemberCardConfigRequest request, Integer masterType, Integer applicationType);

    default List<MemberCardConfigResponse> convertMemberCardConfigResponse(List<MemberCardConfigResp> cardConfigRespList,
                                                                           List<MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespList,
                                                                           List<MemberCardPrivilegeConfigResp> privilegeConfigRespList,
                                                                           List<MemberCardLevelUpgradeRuleResp> upgradeRuleRespList,
                                                                           List<MemberCardLevelRelegationRuleResp> relegationRuleRespList,
                                                                           List<HotelBaseInfoResp> hotelBaseInfoRespList) {

        Map<Long, List<MemberCardLevelConfigInfoResp>> cardLevelMap = cardLevelConfigInfoRespList.stream().collect(Collectors.groupingBy(MemberCardLevelConfigInfoResp::getCardId));
        Map<String, List<MemberCardPrivilegeConfigResp>> privilegeConfigMap = privilegeConfigRespList.stream().collect(Collectors.groupingBy(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel())));
        Map<Long, List<MemberCardLevelUpgradeRuleResp>> upgradeRuleMap = upgradeRuleRespList.stream().collect(Collectors.groupingBy(MemberCardLevelUpgradeRuleResp::getCardId));
        Map<Long, List<MemberCardLevelRelegationRuleResp>> relegationRuleMap = relegationRuleRespList.stream().collect(Collectors.groupingBy(MemberCardLevelRelegationRuleResp::getCardId));
        Map<String, HotelBaseInfoResp> hotelMap = hotelBaseInfoRespList.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, Function.identity()));
        List<MemberCardConfigResponse> memberCardConfigResponses = cardConfigRespList.stream().map(cardConfigResp -> {
            MemberCardConfigResponse memberCardConfigResponse = convertMemberCardConfigResponse(cardConfigResp);
            if (CollectionUtils.isNotEmpty(cardConfigResp.getScopeValues())) {
                memberCardConfigResponse.setScopeValues(cardConfigResp.getScopeValues().stream().map(item -> {
                    ScopeValueResponse scopeValueResponse = new ScopeValueResponse();
                    scopeValueResponse.setValue(item);
                    scopeValueResponse.setName(hotelMap.get(item).getHotelName());
                    scopeValueResponse.setBrandCode(hotelMap.get(item).getBrandCode());
                    return scopeValueResponse;
                }).collect(Collectors.toList()));
            }
            List<MemberCardLevelConfigInfoResp> cardLevelConfigList = cardLevelMap.getOrDefault(cardConfigResp.getId(), new ArrayList<>());
            Map<Integer, String> cardLevelNameMap = cardLevelConfigList.stream().collect(Collectors.toMap(MemberCardLevelConfigInfoResp::getCardLevel, MemberCardLevelConfigInfoResp::getCardLevelName));
            if (CollectionUtils.isNotEmpty(cardLevelConfigList)) {
                memberCardConfigResponse.setCardLevelConfigList(cardLevelConfigList.stream().sorted(Comparator.comparing(MemberCardLevelConfigInfoResp::getCardLevel)).map(this::convertMemberCardLevelConfigInfoResponse).collect(Collectors.toList()));
                for (MemberCardLevelConfigInfoResponse memberCardLevelConfigInfoResponse : memberCardConfigResponse.getCardLevelConfigList()) {
                    List<MemberCardPrivilegeConfigResp> privilegeItemList = privilegeConfigMap.getOrDefault(String.format("%s-%s", cardConfigResp.getId(), memberCardLevelConfigInfoResponse.getCardLevel()), new ArrayList<>());
                    if (CollectionUtils.isNotEmpty(privilegeItemList)) {
                        memberCardLevelConfigInfoResponse.setRelationList(privilegeItemList.stream().map(item -> {
                            MemberPrivilegeConfigResponse memberPrivilegeConfigResponse = new MemberPrivilegeConfigResponse();
                            memberPrivilegeConfigResponse.setName(item.getName());
                            return memberPrivilegeConfigResponse;
                        }).collect(Collectors.toList()));
                    }
                }
            }
            memberCardConfigResponse.setCardLevelUpgradeRuleList(upgradeRuleMap.getOrDefault(cardConfigResp.getId(), Collections.emptyList()).stream().map(this::convertMemberCardLevelUpgradeRuleResponse).sorted(Comparator.comparing(MemberCardLevelUpgradeRuleResponse::getSort)
                    .reversed().thenComparing(MemberCardLevelUpgradeRuleResponse::getSourceLevel).reversed()).collect(Collectors.toList()));
            memberCardConfigResponse.getCardLevelUpgradeRuleList().forEach(item -> {
                item.setSourceLevelName(cardLevelNameMap.get(item.getSourceLevel()));
                item.setTargetLevelName(cardLevelNameMap.get(item.getTargetLevel()));
            });
            memberCardConfigResponse.setCardLevelRelegationRuleList(relegationRuleMap.getOrDefault(cardConfigResp.getId(), Collections.emptyList()).stream().map(this::convertMemberCardLevelRelegationRuleResp).sorted(Comparator.comparing(MemberCardLevelRelegationRuleResponse::getSort)
                    .reversed().thenComparing(MemberCardLevelRelegationRuleResponse::getTargetLevel)).collect(Collectors.toList()));
            memberCardConfigResponse.getCardLevelRelegationRuleList().forEach(item -> {
                item.setTargetLevelName(cardLevelNameMap.get(item.getTargetLevel()));
                item.setSourceLevelName(cardLevelNameMap.get(item.getSourceLevel()));
            });
            memberCardConfigResponse.setCardLength(cardConfigResp.getCardNoRule().getCardLength());
            memberCardConfigResponse.setExcludeNumber(cardConfigResp.getCardNoRule().getExcludeNumber());
            memberCardConfigResponse.setCardPrefix(cardConfigResp.getCardNoRule().getCardPrefix());
            return memberCardConfigResponse;
        }).collect(Collectors.toList());
        return memberCardConfigResponses;
    }


    MemberCardLevelConfigInfoResponse convertMemberCardLevelConfigInfoResponse(MemberCardLevelConfigInfoResp resp);

    @Mappings({
            @Mapping(target = "scopeValues", ignore = true)
    })
    MemberCardConfigResponse convertMemberCardConfigResponse(MemberCardConfigResp cardConfigResp);

    @Mappings({
            @Mapping(target = "id", source = "request.cardId")
    })
    DeleteBaseReq convertDeleteBaseReq(DeleteMemberCardConfigRequest request);

    @Mappings({
            @Mapping(target = "masterCode", source = "request.blocCode"),
            @Mapping(target = "masterType", source = "masterType")
    })
    PageMemberCardLevelConfigInfoReq convertPageMemberCardLevelConfigInfoReq(PageMemberCardLevelConfigInfoRequest request, Integer masterType);

    List<MemberCardLevelConfigInfoResponse> convertMemberCardLevelConfigInfoResponseList(List<MemberCardLevelConfigInfoResp> resp);

    @Mappings({
            @Mapping(target = "masterCode", source = "request.blocCode"),
            @Mapping(target = "masterType", source = "masterType")
    })
    SaveCardLevelConfigInfoReq convertSaveCardLevelConfigInfoReq(SaveCardLevelConfigInfoRequest request, Integer masterType);

    DeleteMemberCardLevelConfigReq convertDeleteMemberCardLevelConfigReq(DeleteMemberCardLevelConfigRequest request);

    ActionBaseReq convertActionBaseReq(ActionBaseRequest request, Integer state);

    List<MemberCardPrivilegeConfigResponse> convertMemberCardPrivilegeConfigResponse(List<MemberCardPrivilegeConfigResp> resp);


    List<SelectResponse> convertSelectResponse(List<MemberCardLevelConfigInfoResp> resp);

    @Mappings({
            @Mapping(target = "text", source = "cardLevelName"),
            @Mapping(target = "value", source = "cardLevel")
    })
    SelectResponse memberCardLevelConfigInfoRespToSelectResponse(MemberCardLevelConfigInfoResp resp);

    @Mappings({
            @Mapping(target = "masterCode", source = "request.blocCode"),
            @Mapping(target = "masterType", source = "masterType")
    })
    PageRelegationRuleReq convertPageRelegationRuleReq(PageRuleRequest request, Integer masterType);

    @Mappings({
            @Mapping(target = "masterCode", source = "request.blocCode"),
            @Mapping(target = "masterType", source = "masterType")
    })
    PageUpgradeRuleReq convertPageUpgradeRuleReq(PageRuleRequest request, Integer masterType);

    List<MemberCardLevelUpgradeRuleResponse> convertMemberCardLevelUpgradeRuleResponseList(List<MemberCardLevelUpgradeRuleResp> resp);

    default List<MemberCardLevelUpgradeRuleResponse> convertMemberCardLevelUpgradeRuleResponseList(List<MemberCardLevelUpgradeRuleResp> resp, List<MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespList) {
        List<MemberCardLevelUpgradeRuleResponse> responses = convertMemberCardLevelUpgradeRuleResponseList(resp);
        Map<String, MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespMap = cardLevelConfigInfoRespList.stream().collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel()), Function.identity()));
        for (MemberCardLevelUpgradeRuleResponse response : responses) {
            String targetKey = String.format("%s-%s", response.getCardId(), response.getTargetLevel());
            String sourceKey = String.format("%s-%s", response.getCardId(), response.getSourceLevel());

            MemberCardLevelConfigInfoResp targetConfig = cardLevelConfigInfoRespMap.get(targetKey);
            if (targetConfig != null) {
                response.setTargetLevelName(targetConfig.getCardLevelName());
            } else {
                // 处理目标等级配置不存在的情况，例如记录日志或设置默认值
                response.setTargetLevelName("");
            }

            MemberCardLevelConfigInfoResp sourceConfig = cardLevelConfigInfoRespMap.get(sourceKey);
            if (sourceConfig != null) {
                response.setSourceLevelName(sourceConfig.getCardLevelName());
            } else {
                // 处理源等级配置不存在的情况，例如记录日志或设置默认值
                response.setSourceLevelName("");
            }
        }
        return responses;
    }


    @Mappings({
            @Mapping(target = "successfulPerformType", source = "upgradeSuccessfulPerformType")
    })
    MemberCardLevelUpgradeRuleResponse convertMemberCardLevelUpgradeRuleResponse(MemberCardLevelUpgradeRuleResp resp);


    List<MemberCardLevelRelegationRuleResponse> convertMemberCardLevelRelegationRuleResponseList(List<MemberCardLevelRelegationRuleResp> resp);
    default List<MemberCardLevelRelegationRuleResponse> convertMemberCardLevelRelegationRuleResponseList(List<MemberCardLevelRelegationRuleResp> resp, List<MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespList) {

        List<MemberCardLevelRelegationRuleResponse> responses = convertMemberCardLevelRelegationRuleResponseList(resp);
        Map<String, MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespMap = cardLevelConfigInfoRespList.stream().collect(Collectors.toMap(item -> String.format("%s-%s", item.getCardId(), item.getCardLevel()), Function.identity()));
        for (MemberCardLevelRelegationRuleResponse response : responses) {
            String targetKey = String.format("%s-%s", response.getCardId(), response.getTargetLevel());
            String sourceKey = String.format("%s-%s", response.getCardId(), response.getSourceLevel());

            MemberCardLevelConfigInfoResp targetConfig = cardLevelConfigInfoRespMap.get(targetKey);
            if (targetConfig != null) {
                response.setTargetLevelName(targetConfig.getCardLevelName());
            } else {
                // 处理目标等级配置不存在的情况，例如记录日志或设置默认值
                response.setTargetLevelName("");
            }

            MemberCardLevelConfigInfoResp sourceConfig = cardLevelConfigInfoRespMap.get(sourceKey);
            if (sourceConfig != null) {
                response.setSourceLevelName(sourceConfig.getCardLevelName());
            } else {
                // 处理源等级配置不存在的情况，例如记录日志或设置默认值
                response.setSourceLevelName("");
            }
        }
        return responses;
    }

    @Mappings({
            @Mapping(target = "masterCode", source = "request.blocCode"),
            @Mapping(target = "upgradeSuccessfulPerformType", source = "request.successfulPerformType"),
            @Mapping(target = "masterType", source = "masterType"),
    })
    SaveCardLevelUpgradeRuleReq convertSaveCardLevelUpgradeRuleReq(SaveCardLevelUpgradeRuleRequest request, Integer masterType);

    @Mappings({
            @Mapping(target = "masterCode", source = "request.blocCode"),
            @Mapping(target = "relegationSuccessfulPerformType", source = "request.successfulPerformType"),
            @Mapping(target = "masterType", source = "masterType")
    })
    SaveCardLevelRelegationRuleReq convertSaveCardLevelRelegationRuleReq(SaveCardLevelRelegationRuleRequest request, Integer masterType);

    @Mappings({
            @Mapping(target = "successfulPerformType", source = "relegationSuccessfulPerformType")
    })
    MemberCardLevelRelegationRuleResponse convertMemberCardLevelRelegationRuleResp(MemberCardLevelRelegationRuleResp resp);

    @Mappings({
            @Mapping(target = "masterType", source = "masterType"),
            @Mapping(target = "masterCode", source = "request.blocCode"),
    })
    SaveMemberPrivilegeConfigInfoReq convertSaveMemberPrivilegeConfigInfoReq(SaveMemberPrivilegeConfigInfoRequest request, Integer masterType);

    @Mappings({
            @Mapping(target = "masterCode", source = "request.blocCode"),
            @Mapping(target = "masterType", source = "masterType")
    })
    QueryMemberPrivilegeConfigReq convertQueryMemberPrivilegeConfigReq(PageMemberPrivilegeRequest request, Integer masterType);

    List<MemberPrivilegeConfigResponse> convertMemberPrivilegeConfigRespList(List<MemberPrivilegeConfigResp> resp);

    MemberPrivilegeConfigResponse convertMemberPrivilegeConfigResp(MemberPrivilegeConfigResp resp);

    @Mappings({
            @Mapping(target = "masterCode", source = "request.blocCode"),
            @Mapping(target = "masterType", source = "masterType")
    })
    SaveMemberTagConfigReq convertSaveMemberTagConfigReq(SaveMemberTagConfigRequest request, Integer masterType);

    MemberTagConfigInfoResponse convertMemberTagConfigInfoResponse(MemberTagConfigInfoResp resp);

    @Mappings({
            @Mapping(target = "typeList", source = "request.type"),
            @Mapping(target = "type", ignore = true)
    })
    PageMemberTagConfigInfoReq convertPageMemberTagConfigInfoReq(PageMemberTagConfigInfoRequest request, Integer masterType);

    PageMemberTagConfigInfoReq convertPageMemberTagConfigInfoReq(BaseRequest request, Integer masterType, Integer pageSize);

    List<MemberTagConfigInfoResponse> convertMemberTagConfigInfoResponseList(List<MemberTagConfigInfoResp> resp);

    @Mappings({
            @Mapping(target = "text", source = "name"),
            @Mapping(target = "value", source = "id")
    })
    List<SelectResponse> convertTagSelectResponse(List<MemberTagConfigInfoResp> resp);

    @Mappings({
            @Mapping(target = "text", source = "name"),
            @Mapping(target = "value", source = "id")
    })
    List<SelectResponse> convertTagTypeSelectResponse(List<MemberTagConfigInfoResp> resp);

    List<MemberCardConfigResponse> convertMemberCardConfigResponse(List<MemberCardConfigResp> resp);

    default List<MemberCardConfigResponse> convertMemberCardConfigResponse(List<MemberCardConfigResp> resp, List<MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespList) {
        List<MemberCardConfigResponse> responses = convertMemberCardConfigResponse(resp);
        Map<Long, List<MemberCardLevelConfigInfoResp>> cardLevelConfigInfoRespMap = cardLevelConfigInfoRespList.stream().
                collect(Collectors.groupingBy(MemberCardLevelConfigInfoResp::getCardId));
        responses.forEach(response -> {
            List<MemberCardLevelConfigInfoResp> cardLevelConfigInfoResps = cardLevelConfigInfoRespMap.get(response.getId());
            if (CollectionUtils.isNotEmpty(cardLevelConfigInfoResps)) {
                response.setCardLevelConfigList(convertMemberCardLevelConfigInfoResponseList(cardLevelConfigInfoResps));
            }
        });
        return responses;
    }
}

