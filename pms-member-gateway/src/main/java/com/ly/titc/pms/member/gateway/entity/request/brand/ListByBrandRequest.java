package com.ly.titc.pms.member.gateway.entity.request.brand;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @program: cms-parent
 * @description:
 * @author: shawn
 * @create: 2021-06-08 20:21
 */
@Data
@Accessors(chain = true)
public class ListByBrandRequest {

    /**
     * 品牌code
     */
    private List<String> brands;

    /**
     * 酒店名称
     */
    private String hotelName;
}
