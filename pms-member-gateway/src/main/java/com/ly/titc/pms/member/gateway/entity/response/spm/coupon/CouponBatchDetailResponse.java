package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-6 19:50
 */
@Data
@Accessors(chain = true)
public class CouponBatchDetailResponse {

    /**
     * 券号
     */
    private String couponCode;

    /**
     * 门店名称
     */
    private String hotelName;

    /**
     * 客户名称
     */
    private String customerName;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 手机号
     */
    private String mobile;

}
