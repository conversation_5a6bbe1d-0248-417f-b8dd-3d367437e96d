package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-4 14:38
 */
@Data
@Accessors(chain = true)
public class




PageCouponRequest extends PageBaseRequest {

    /**
     * 状态
     * 1-(已发放)未使用 2-已使用 3-已作废 4-已过期
     */
    private Integer state;

    /**
     * 券类型
     */
    private Integer couponType;

    /**
     * 模糊搜索
     */
    private String couponName;

    /**
     * 发放事件
     */
    private Integer grantEvent;

    /**
     * 发放开始日期
     * 格式 yyyy-MM-dd
     */

    private String issueStartDate;
    /**
     * 发放结束日期
     * 格式 yyyy-MM-dd
     */
    private String issueEndDate;

    /**
     * 接收人类型
     */
    @NotBlank(message = "所属人类型不能为空")
    private String receiverType;

    /**
     * 接收人code
     */
    private String receiverCode;

    /**
     * 不同主体类型code
     */
    private String masterCode;
    /**
     * 发放主体类型 1-集团 2-门店 3-艺龙会
     */
    private Integer masterType;

    /**
     * 券码
     * 模糊搜索
     */
    private String couponCode;


    /**
     * 使用开始时间
     * 格式 yyyy-MM-dd HH:mm:ss
     */
    private String useStartTime;

    /**
     * 使用结束时间
     * 格式 yyyy-MM-dd HH:mm:ss
     */
    private String useEndTime;
}
