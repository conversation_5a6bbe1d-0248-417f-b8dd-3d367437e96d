package com.ly.titc.pms.member.gateway.entity.request.member;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

/**
 * @Author：rui
 * @name：MemberContactInfoRequest
 * @Date：2024-11-18 20:30
 * @Filename：MemberContactInfoRequest
 */
@Data
public class MemberContactInfoRequest {
    /**
     * 会员号
     *
     */
    private String memberNo;

    /**
     * 邮箱
     */
    private String email;

    /**
     * QQ号
     */
    private String qq;

    /**
     * 微信号
     */
    private String wechat;

    /**
     * 国家id
     */
    private Integer countryId;

    /**
     * 国家
     */
    private String country;

    /**
     * 省id
     */
    private Integer provinceId;

    /**
     * 省
     */
    private String province;

    /**
     * 城市id
     */
    private Integer cityId;

    /**
     * 城市
     */
    private String city;

    /**
     * 区域id
     */
    private Integer districtId;

    /**
     * 区域
     */
    private String district;

    /**
     * 地址
     */
    private String address;

    /**
     * 邮政编码
     */
    private String postalCode;

}
