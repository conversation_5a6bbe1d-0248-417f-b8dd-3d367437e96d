package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.spm.dubbo.entity.request.BaseReq;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotEmpty;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-16 11:41
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class QueryEventActivityRequest extends BaseRequest {


    @NotEmpty(message = "活动编码不能为空")
    private String activityCode;
}
