package com.ly.titc.pms.member.gateway.entity.response.general;

import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-6
 */
@Data
public class AreaTreeResponse {

    /**
     * 区域id
     */
    private Long areaId;

    /**
     * 父id
     */
    private Long pid;

    /**
     * 区域名称
     */
    private String areaName;

    /**
     * 区域层级
     */
    private Integer level;

    /**
     * 是否可选
     */
    private Boolean enabled = true;

    /**
     * 子区域信息
     */
    private List<AreaTreeResponse> children;
}
