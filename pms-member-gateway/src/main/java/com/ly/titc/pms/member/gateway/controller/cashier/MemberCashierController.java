package com.ly.titc.pms.member.gateway.controller.cashier;

import com.ly.titc.cashier.dubbo.entity.request.CashierStaticQrCodeReq;
import com.ly.titc.cashier.dubbo.entity.request.config.PayCashierConfigSaveReq;
import com.ly.titc.cashier.dubbo.entity.response.CashierStaticQrCodeResp;
import com.ly.titc.cashier.dubbo.entity.response.config.PayCashierConfigSaveResultResp;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.gateway.converter.MemberCashierConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.cashier.CashierConfigRequest;
import com.ly.titc.pms.member.gateway.entity.request.cashier.RequestBodyPayCashierConfigSaveRequest;
import com.ly.titc.pms.member.gateway.entity.request.cashier.StaticQrCodeRequest;
import com.ly.titc.pms.member.gateway.entity.response.cashier.PayCashierConfigResponse;
import com.ly.titc.pms.member.gateway.entity.response.cashier.PayCashierConfigSaveResultResponse;
import com.ly.titc.pms.member.gateway.entity.response.cashier.StaticQrCodeResponse;
import com.ly.titc.pms.member.gateway.entity.response.cashier.TermListResponse;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.PayCashierConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.TermListDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cashier.OnlinePayDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cashier.PayCashierConfigDecorator;
import com.ly.titc.pms.member.mediator.service.MemberCashierMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 会员收银页面
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-3 15:32
 */
@Slf4j
@RestController
@RequestMapping("/member/cashier")
public class MemberCashierController {

    @Resource
    private MemberCashierMedService cashierMedService;
    @Resource
    private MemberCashierConverter cashierConverter;
    @Resource
    private OnlinePayDecorator onlinePayDecorator;


    /**
     * 根据集团编号或会员场景查询收银设置
     */
    @PostMapping("/getCashierConfig")
    public Response<PayCashierConfigResponse> getCashierConfig(@Valid @RequestBody CashierConfigRequest request){
        PayCashierConfigDto dto= cashierMedService.getCashierConfig(cashierConverter.convert(request));
        return Response.success(cashierConverter.convert(dto));

    }

    /**
     * 获取支付终端
     */
    @PostMapping("/getTermList")
    public Response<List<TermListResponse>> getTermList(@Valid @RequestBody BaseRequest request){
        List<TermListDto> dtos=  cashierMedService.getTermList(cashierConverter.convert(request));
        return Response.success(cashierConverter.convert(dtos));
    }

    /**
     * 通联超时补偿获取动态二维码
     */
    @PostMapping("/getStaticQrCode")
    public Response<StaticQrCodeResponse> getStaticQrCode(@Valid @RequestBody StaticQrCodeRequest request){
        CashierStaticQrCodeReq req = cashierConverter.convert(request);
        CashierStaticQrCodeResp resp= onlinePayDecorator.getStaticQrCode(req);
        return Response.success(cashierConverter.convert(resp));
    }


}
