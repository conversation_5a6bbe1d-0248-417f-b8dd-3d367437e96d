package com.ly.titc.pms.member.gateway.entity.response.hotel;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @ClassName: HotelDistributeResponse
 * @Description:
 * @since 2023/1/5 11:14
 */
@Data
@Accessors(chain = true)
public class HotelDistributeResponse {
    /**
     * 酒店代码-（集团定义的酒店代码）
     */
    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 品牌编码
     */
    private String brandCode;

}
