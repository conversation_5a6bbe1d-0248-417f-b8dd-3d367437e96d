package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @author: shawn
 * @email: <EMAIL>
 * @date: 2024/11/8 16:28
 * @description:
 */
@Data
public class CouponRedeemInfoResponse {

    /**
     * 券码
     */
    private String couponCode;

    /**
     * 券名称
     */
    private String couponName;

    /**
     * 券类型 1.折扣券,2.代金券,3.免房券,4.餐饮券,5.抵扣券,6.小时券,7.延时券,8.升房券,9.体验券,10.通用券
     */
    private String couponType;

    /**
     * 券值
     */
    private String couponValue;


    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 酒店编号
     */
    private String hotelCode;

    /**
     * 酒店名称
     */
    private String hotelName;

    /**
     * 核销编号
     */
    private String redeemNo;

    /**
     * 使用方类型:booking-订房订单，offline-线下兑换
     */
    private String usageType;

    /**
     * 使用方的唯一标识
     */
    private String usageCode;

    /**
     * 核销时的房间编号
     */
    private String roomNo;

    /**
     * 核销备注
     */
    private String remark;

    /**
     * 使用日期
     */
    private String useDate;

    /**
     * 核销人
     */
    private String redeemer;

    /**
     * 核销时间
     */
    private LocalDateTime redeemTime;
}
