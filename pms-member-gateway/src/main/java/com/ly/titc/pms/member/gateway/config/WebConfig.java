package com.ly.titc.pms.member.gateway.config;

import com.ly.titc.oauth.client.interceptor.UserAuthenticateInterceptor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * @Author：rui
 * @name：WebConfig
 * @Date：2024-11-11 21:04
 * @Filename：WebConfig
 */
@Configuration
public class WebConfig implements WebMvcConfigurer {

    @Resource
    private UserAuthenticateInterceptor userAuthenticateInterceptor;

    /**
     * SSO OAuth拦截器
     * 用于处理请求权限认证授权
     */
    @Resource
    private LoginInterceptor loginInterceptor;
    /**
     * 权限验证通过后的拦截器
     * 用于处理本系统自有逻辑，比如获取酒店权限等
     */
    @Resource
    private LoginSuccessInterceptor loginSuccessInterceptor;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        // 设置不需要登陆验证的资源
        loginInterceptor.addAllNoNeedLoginRelationPath(getSsoNoNeedLoginRelationPath());
        // 设置需要登陆验证，但不需要验证资源权限的资源
        loginInterceptor.addAllNoNeedValidTokenRelationPath(getSsoNoNeedValidTokenRelationPath());
        // 设置不要登陆的资源
        userAuthenticateInterceptor.setNoLoginUrls(getSsoNoNeedLoginRelationPath());
        // 设置需要登陆，但不需要验证的资源
        userAuthenticateInterceptor.setNoAuthUrls(getSsoNoNeedValidTokenRelationPath());
        userAuthenticateInterceptor.setSource("pms");
        registry.addInterceptor(userAuthenticateInterceptor)
                .addPathPatterns("/**")
                .excludePathPatterns("/health");
        registry.addInterceptor(loginInterceptor).addPathPatterns("/**").excludePathPatterns("/health");
        // 注册权限认证通过后的拦截器
        registry.addInterceptor(loginSuccessInterceptor).addPathPatterns("/**").excludePathPatterns("/health");

    }

    /**
     * 设置不用登陆的资源相对地址
     * @return list
     */
    private List<String> getSsoNoNeedLoginRelationPath() {
        return Arrays.asList("/health",
                "/doc/*",
                "/transfer/*" );
    }

    /**
     * 设置需要登陆但不需要验证的资源相对地址
     * @return list
     */
    private List<String> getSsoNoNeedValidTokenRelationPath() {
        return Arrays.asList("/*");
    }
}
