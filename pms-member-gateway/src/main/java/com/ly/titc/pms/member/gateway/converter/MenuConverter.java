package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.oauth.entity.response.menu.MenuResp;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.gateway.entity.response.menu.MenuResponse;
import org.mapstruct.Mapper;

/**
 * @Author：rui
 * @name：MenuConverter
 * @Date：2024-12-2 19:26
 * @Filename：MenuConverter
 */
@Mapper(componentModel = "spring")
public interface MenuConverter extends BaseConverter {
    MenuResponse convert2Response(MenuResp menuResp);
}
