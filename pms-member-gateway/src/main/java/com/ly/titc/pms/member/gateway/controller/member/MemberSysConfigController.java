package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.MasterTypeEnum;
import com.ly.titc.pms.ecrm.dubbo.entity.BaseMasterReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberPointSysConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MemberPointSysConfigResp;
import com.ly.titc.pms.member.gateway.converter.MemberSysConfigConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.sysConfig.MemberPointSysConfigRequest;
import com.ly.titc.pms.member.gateway.entity.response.config.MemberPointSysConfigResponse;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberSysConfigDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-24 11:59
 */
@Slf4j
@RestController
@RequestMapping("/member/sys/config")
public class MemberSysConfigController {

    @Resource
    private MemberSysConfigDecorator configDecorator;
    @Resource
    private MemberSysConfigConverter configConverter;

    /**
     * 积分设置
     */
    @RequestMapping("/setPoint")
    public Response<Boolean> setPoint(@RequestBody @Valid MemberPointSysConfigRequest request){
        MemberPointSysConfigReq configReq= configConverter.convert(request);
        configReq.setMasterType(MasterTypeEnum.BLOC.getType());
        configReq.setMasterCode(request.getBlocCode());
        configDecorator.savePointConfig(configReq);
        return Response.success(true);
    }

    /**
     * 获取积分设置详情
     */
    @RequestMapping("/getPoint")
    public Response<MemberPointSysConfigResponse> getPoint(@RequestBody @Valid BaseRequest request){
        BaseMasterReq req = configConverter.convert(request);
        MemberPointSysConfigResp resp = configDecorator.getPointConfig(req);
        MemberPointSysConfigResponse response = configConverter.convert(resp);
        return Response.success(response);
    }
}
