package com.ly.titc.pms.member.gateway.entity.request.member.card;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author：rui
 * @name：SaveCardLevelConfigInfoRequest
 * @Date：2024-11-12 17:47
 * @Filename：SaveCardLevelConfigInfoRequest
 */
@Data
public class SaveCardLevelConfigInfoRequest extends BaseRequest {

    /**
     * 新增时不传， 编辑时传入
     */
    private String id;

    /**
     * 会员卡id
     */
    @NotNull(message = "会员卡不能为空")
    private Long cardId;

    /**
     * 会员等级
     */
    @NotNull(message = "会员等级不能为空")
    private Integer cardLevel;

    /**
     * 会员等级名称
     */
    @NotBlank(message = "会员等级名称不能为空")
    private String cardLevelName;

    /**
     * 折扣
     */
    private Integer cardDiscount;

    /**
     * 有效期
     */
    @Max(value = 10000, message = "有效期不能超过10000天")
    @Min(value = 1, message = "有效期不能小于1天")
    private Integer validPeriod;

    /**
     * 是否长期有效 0 否 1 是
     */
    private Integer isLongTerm;

    /**
     * 权益关联列表
     */
    private List<MemberCardPrivilegeConfigInfoRequest> relationList;


}
