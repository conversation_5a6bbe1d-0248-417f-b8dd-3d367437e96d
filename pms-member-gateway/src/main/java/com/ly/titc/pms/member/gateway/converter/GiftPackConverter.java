package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.gateway.entity.request.spm.ApplicableHotelInfoRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.giftpack.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.ApplicableHotelInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.CommonNameInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.giftpack.*;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.GiftPackItemInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.GiftPackSaleInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableRuleDataInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableRuleInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.CommonInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.GrantGiftPackBatchReq;
import com.ly.titc.pms.spm.dubbo.entity.request.giftpack.*;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.*;
import com.ly.titc.pms.spm.dubbo.enums.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-28
 */
@Mapper(componentModel = "spring")
public interface GiftPackConverter {
    Integer UNLIMITED = -1;

    default SaveGiftPackInfoReq convertSaveGiftPackInfoReq(SaveGiftPackInfoRequest request, UserInfoDto user){
        //礼包基础信息
        SaveGiftPackInfoReq req = convertSaveGiftPackBaseInfoReq(request, user);
        //礼包售卖信息
        req.setSaleInfo(convertGiftPackSaleInfoDto(request,user));
        //礼包明细
        req.setItemInfos(convertGiftPackItemInfoDtosByRequest(request.getGiftPackItemInfos()));
        return req;
    }

    default SaveGiftPackInfoReq convertSaveGiftPackBaseInfoReq(SaveGiftPackInfoRequest request,UserInfoDto user){
        SaveGiftPackInfoReq req = new SaveGiftPackInfoReq();
        req.setTrackingId(UUID.randomUUID().toString());
        req.setGiftPackNo(request.getGiftPackNo());
        req.setMasterType(MasterTypeEnum.BLOC.getCode());
        req.setMasterCode(user.getBlocCode());
        req.setGiftPackName(request.getGiftPackName());
        req.setAmount(request.getAmount());
        req.setIsAllowSale(request.getIsAllowSale());
        req.setEffectStartDate(request.getEffectStartDate());
        req.setEffectEndDate(request.getEffectEndDate());
        req.setContent(request.getContent());
        req.setPromotionContent(request.getPromotionContent());
        req.setRemark(request.getRemark());
        req.setOperator(user.getUserName());
        req.setSourceClient(SourceClientEnum.CRM.getCode());
        req.setTotalQuantity(request.getIsUnlimitedQuantity() ? UNLIMITED : request.getTotalQuantity());
        return req;
    }

    default GiftPackSaleInfoDto convertGiftPackSaleInfoDto(SaveGiftPackInfoRequest request,UserInfoDto user){
        if(request.getGiftPackSaleInfoDto() == null){
            return null;
        }
        GiftPackSaleInfoDto dto = new GiftPackSaleInfoDto();
        dto.setOfflineSaleAmount(request.getGiftPackSaleInfoDto().getSaleAmount());
        dto.setIsAllowPointsPay(request.getGiftPackSaleInfoDto().getIsAllowPointsPay());
        dto.setSalePlatform(convertSalePlatform(request.getGiftPackSaleInfoDto()));
        dto.setOnlineSaleAmount(convertOnlineSaleAmount(request.getGiftPackSaleInfoDto()));
        dto.setOnlineSalePoints(convertOnlineSalePoints(request.getGiftPackSaleInfoDto()));
        dto.setSaleHotelInfos(convertApplicableHotelInfo(request.getGiftPackSaleInfoDto().getApplicableHotelInfoRequest(),user));
        return dto;
    }

    default BigDecimal convertOnlineSaleAmount(GiftPackSaleInfoRequest dto){
        if(dto.getIsSelectedOnlineSalePlatform()){
            return dto.getOnlineSaleAmount() == null ? dto.getSaleAmount() : dto.getOnlineSaleAmount();
        }
        return new BigDecimal(Constant.ZERO);
    }

    default Integer convertOnlineSalePoints(GiftPackSaleInfoRequest dto){
        if(dto.getIsSelectedOnlineSalePlatform()){
            return dto.getOnlineSalePoints() == null ? Constant.ZERO : dto.getOnlineSalePoints();
        }
        return Constant.ZERO;
    }

    default Integer convertSalePlatform(GiftPackSaleInfoRequest dto){
        int salePlatform = 0;
        if(dto.getIsSelectedOnlineSalePlatform()){
            salePlatform = salePlatform | dto.getOnlineSalePlatform();
        }
        if(dto.getIsSelectedOfflineSaleHotel()){
            salePlatform = salePlatform | PlatformEnum.OFFLINE.getCode();
        }
        return salePlatform;
    }

    GiftPackItemInfoDto convertGiftPackItemInfoDto(GiftPackItemInfoRequest dto);
    List<GiftPackItemInfoDto> convertGiftPackItemInfoDtosByRequest(List<GiftPackItemInfoRequest> dtos);


    @Mapping(target = "currPage", source = "request.pageIndex")
    @Mapping(target = "sourceClient", ignore = true)
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "blocCode", source = "blocCode")
    GetGiftPackListReq convertGetGiftPackListReq(GetGiftPackListRequest request,String blocCode);

    GiftPackListResponse convertGiftPackListResponse(GiftPackListResp resp);

    List<GiftPackListResponse> convertGiftPackListResponses(List<GiftPackListResp> resps);

    default EnableAndDisableGiftPackReq convertEnableAndDisableGiftPackReq(EnableAndDisableGiftPackRequest request,UserInfoDto user){
        EnableAndDisableGiftPackReq req = new EnableAndDisableGiftPackReq();
        req.setMasterType(MasterTypeEnum.BLOC.getCode());
        req.setMasterCode(user.getBlocCode());
        req.setGiftPackNo(request.getGiftPackNo());
        req.setOperator(user.getUserName());
        req.setTrackingId(UUID.randomUUID().toString());
        return req;
    }

    default GetGiftPackInfoReq convertGetGiftPackInfoReq(GetGiftPackInfoRequest request,UserInfoDto user){
        GetGiftPackInfoReq req = new GetGiftPackInfoReq();
        req.setMasterType(MasterTypeEnum.BLOC.getCode());
        req.setMasterCode(user.getBlocCode());
        req.setGiftPackNo(request.getGiftPackNo());
        req.setVersion(request.getVersion());
        req.setTrackingId(UUID.randomUUID().toString());
        return req;
    }

    @Mapping(target = "saleInfo",ignore = true)
    @Mapping(target = "itemInfos",ignore = true)
    GiftPackInfoResponse convertGiftPackInfoResponse(GiftPackInfoResp resp);

    default GiftPackInfoResponse buildGiftPackInfoResponse(GiftPackInfoResp resp){
        GiftPackInfoResponse response = convertGiftPackInfoResponse(resp);
        response.setSaleInfo(convertGiftPackSaleInfoDto(resp.getSaleInfo()));
        response.setItemInfos(convertGiftPackItemInfoDtos(resp.getItemInfos()));
        return response;
    }

    default GiftPackSaleInfoResponse convertGiftPackSaleInfoDto(GiftPackSaleInfoDto dto){
        if(dto == null){
            return null;
        }
        GiftPackSaleInfoResponse giftPackSaleInfoDto = new GiftPackSaleInfoResponse();
        giftPackSaleInfoDto.setSaleAmount(dto.getOfflineSaleAmount());
        giftPackSaleInfoDto.setOnlineSaleAmount(dto.getOnlineSaleAmount());
        giftPackSaleInfoDto.setOnlineSalePoints(dto.getOnlineSalePoints());
        giftPackSaleInfoDto.setIsAllowPointsPay(dto.getIsAllowPointsPay());
        if(Objects.isNull(dto.getSalePlatform())){
            giftPackSaleInfoDto.setIsSelectedOnlineSalePlatform(Boolean.FALSE);
            giftPackSaleInfoDto.setIsSelectedOfflineSaleHotel(Boolean.FALSE);
        }else{
            giftPackSaleInfoDto.setIsSelectedOnlineSalePlatform(dto.getSalePlatform() > Constant.ONE);
            giftPackSaleInfoDto.setIsSelectedOfflineSaleHotel(
                    Objects.equals(dto.getSalePlatform() & PlatformEnum.OFFLINE.getCode(),PlatformEnum.OFFLINE.getCode())
            );
        }
        giftPackSaleInfoDto.setOnlineSalePlatform(convertOnlineSalePlatform(dto));

        giftPackSaleInfoDto.setSaleHotelInfos(convertApplicableHotelInfoResponse(dto.getSaleHotelInfos()));
        return giftPackSaleInfoDto;
    }

    default ApplicableHotelInfoResponse convertApplicableHotelInfoResponse(ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> applicableRuleInfoDto){
        ApplicableHotelInfoResponse response = new ApplicableHotelInfoResponse();
        if(Objects.isNull(applicableRuleInfoDto)){
            return response;
        }
        response.setApplicableScopeType(applicableRuleInfoDto.getApplicableScopeType());
        if(Objects.equals(applicableRuleInfoDto.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())){
            return response;
        }else{
            List<ApplicableRuleDataInfoDto> applicableRuleDataInfo = applicableRuleInfoDto.getApplicableRuleDataInfo();
            applicableRuleDataInfo.stream().parallel().forEach(ruleDataInfo->{
                List<CommonNameInfoResponse> commonNameInfoResponseList = ruleDataInfo.getBizData().stream().map(item->{
                    CommonNameInfoResponse commonNameInfoResponse = new CommonNameInfoResponse();
                    commonNameInfoResponse.setName(item.getName());
                    commonNameInfoResponse.setCode(item.getCode());
                    return commonNameInfoResponse;
                }).collect(Collectors.toList());
                if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.BRAND.getRuleType())){
                    response.setBrandInfos(commonNameInfoResponseList);
                }else if (Objects.equals(ruleDataInfo.getApplicableRuleType(),ApplicableRuleTypeEnum.AREA.getRuleType())){
                    response.setBrandInfos(commonNameInfoResponseList);
                }else if (Objects.equals(ruleDataInfo.getApplicableRuleType(),ApplicableRuleTypeEnum.REGION.getRuleType())){
                    response.setBrandInfos(commonNameInfoResponseList);
                }else if (Objects.equals(ruleDataInfo.getApplicableRuleType(),ApplicableRuleTypeEnum.HOTEL.getRuleType())){
                    response.setBrandInfos(commonNameInfoResponseList);
                }
            });
            return response;
        }
    }



    default Integer convertOnlineSalePlatform(GiftPackSaleInfoDto dto){
        Integer salePlatform = dto.getSalePlatform();
        if(Objects.isNull(salePlatform)){
            return Constant.ZERO;
        }
        if(Objects.equals(salePlatform & PlatformEnum.WDF.getCode(),PlatformEnum.WDF.getCode())){
            return PlatformEnum.WDF.getCode();
        }
        if(Objects.equals(salePlatform & PlatformEnum.ELONG.getCode(),PlatformEnum.ELONG.getCode())){
            return PlatformEnum.ELONG.getCode();
        }
        return Constant.ZERO;
    }

    GiftPackItemInfoResponse convertGiftPackItemInfoDto(GiftPackItemInfoDto dto);
    List<GiftPackItemInfoResponse> convertGiftPackItemInfoDtos(List<GiftPackItemInfoDto> dtos);


    default DeleteGiftPackReq convertDeleteGiftPackReq(DeleteGiftPackRequest request,UserInfoDto user){
        DeleteGiftPackReq req = new DeleteGiftPackReq();
        req.setMasterType(MasterTypeEnum.BLOC.getCode());
        req.setMasterCode(user.getBlocCode());
        req.setGiftPackNo(request.getGiftPackNo());
        req.setTrackingId(UUID.randomUUID().toString());
        return req;
    }

    GrantGiftPackBatchReq convertGiftPackGrantRequest2ApiReq(GrantGiftPackRequest req);

    @Mapping(target = "currPage", source = "pageIndex")
    QueryGiftPackBatchListReq convertPageBatchRequest2ApiReq(PageGiftPackBatchRequest req);

    QueryGiftGrantBatchInfoReq convertGiftPackSaleInfoRequest2ApiReq(QueryGiftGrantBatchInfoRequest request);

    GiftPackBatchInfoResponse covertGiftPackBatchInfoResp2Response(GiftPackBatchInfoResp giftPackBatchInfo);

    @Mapping(target = "currPage", source = "pageIndex")
    @Mapping(target = "memberCardNo", source = "memberNo")
    QueryGiftPackBatchDetailListReq convertPageBatchDetailRequest2ApiReq(PageGiftPackBatchDetailRequest request);

    GiftPackBatchDetailExportResponse convertGiftPackBatchDetailListResp2Export(GiftPackBatchDetailListResp e);

    GiftPackBatchDetailListResponse convertGiftPackBatchDetailListResp2Response(GiftPackBatchDetailListResp d);

    GiftPackGrantSaleInfoResponse convertGiftPackSaleRespToResponse(GiftPackSaleInfoResp saleInfo);

    GiftPackBatchListResponse convertGiftPackBatchListResp2Response(GiftPackBatchListResp d);

    default GrantGiftPackBatchReq convertGrantGiftPackBatchReq(GrantGiftPackRequest req,UserInfoDto user){
        String blocCode = user.getBlocCode();
        String userName = user.getUserName();

        GrantGiftPackBatchReq grantGiftPackBatchReq = convertGiftPackGrantRequest2ApiReq(req);
        if(Objects.isNull(req.getGrantEvent())){
            grantGiftPackBatchReq.setGrantEvent(GrantEventEnum.MANUAL_GRANT.getEvent());
        }
        grantGiftPackBatchReq.setSourceClient(SourceClientEnum.CRM.getCode());
        grantGiftPackBatchReq.setReceiverCodes(Collections.singletonList(req.getReceiverCode()));
        grantGiftPackBatchReq.setOperator(userName);
        grantGiftPackBatchReq.setBlocCode(blocCode);
        return grantGiftPackBatchReq;
    }

    default ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> convertApplicableHotelInfo(ApplicableHotelInfoRequest applicableHotelInfoRequest,
                                                                                        UserInfoDto userInfoDto){
        if (!Objects.isNull(applicableHotelInfoRequest)) {
            ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> hotelApplicableInfo = new ApplicableRuleInfoDto<>();
            hotelApplicableInfo.setApplicableParentRuleType(ApplicableRuleTypeEnum.HOTEL.getParentType());
            hotelApplicableInfo.setApplicableScopeType(applicableHotelInfoRequest.getApplicableScopeType());
            if (Objects.equals(applicableHotelInfoRequest.getApplicableScopeType(), ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType())) {
                List<ApplicableRuleDataInfoDto> applicableRuleDataInfoDtos = new ArrayList<>();
                if (!CollectionUtils.isEmpty(applicableHotelInfoRequest.getBrandInfos())) {
                    ApplicableRuleDataInfoDto brandApplicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
                    brandApplicableRuleDataInfoDto.setMasterType(MasterTypeEnum.BLOC.getCode());
                    brandApplicableRuleDataInfoDto.setMasterCode(userInfoDto.getBlocCode());
                    brandApplicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
                    brandApplicableRuleDataInfoDto.setApplicableRuleType(ApplicableRuleTypeEnum.BRAND.getRuleType());
                    brandApplicableRuleDataInfoDto.setBizData(
                            applicableHotelInfoRequest.getBrandInfos().stream().map(brandInfo -> {
                                CommonInfoDto commonInfoDto = new CommonInfoDto();
                                commonInfoDto.setName(brandInfo.getName());
                                commonInfoDto.setCode(brandInfo.getCode());
                                return commonInfoDto;
                            }).collect(Collectors.toList())
                    );
                    applicableRuleDataInfoDtos.add(brandApplicableRuleDataInfoDto);
                }
                if (!CollectionUtils.isEmpty(applicableHotelInfoRequest.getAreaInfos())) {
                    ApplicableRuleDataInfoDto areaApplicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
                    areaApplicableRuleDataInfoDto.setMasterType(MasterTypeEnum.BLOC.getCode());
                    areaApplicableRuleDataInfoDto.setMasterCode(userInfoDto.getBlocCode());
                    areaApplicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
                    areaApplicableRuleDataInfoDto.setApplicableRuleType(ApplicableRuleTypeEnum.AREA.getRuleType());
                    areaApplicableRuleDataInfoDto.setBizData(
                            applicableHotelInfoRequest.getAreaInfos().stream().map(areaInfo -> {
                                CommonInfoDto commonInfoDto = new CommonInfoDto();
                                commonInfoDto.setName(areaInfo.getName());
                                commonInfoDto.setCode(areaInfo.getCode());
                                return commonInfoDto;
                            }).collect(Collectors.toList())
                    );
                    applicableRuleDataInfoDtos.add(areaApplicableRuleDataInfoDto);
                }
                if (!CollectionUtils.isEmpty(applicableHotelInfoRequest.getRegionInfos())) {
                    ApplicableRuleDataInfoDto regionApplicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
                    regionApplicableRuleDataInfoDto.setMasterType(MasterTypeEnum.BLOC.getCode());
                    regionApplicableRuleDataInfoDto.setMasterCode(userInfoDto.getBlocCode());
                    regionApplicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
                    regionApplicableRuleDataInfoDto.setApplicableRuleType(ApplicableRuleTypeEnum.REGION.getRuleType());
                    regionApplicableRuleDataInfoDto.setBizData(
                            applicableHotelInfoRequest.getRegionInfos().stream().map(regionInfo -> {
                                CommonInfoDto commonInfoDto = new CommonInfoDto();
                                commonInfoDto.setName(regionInfo.getName());
                                commonInfoDto.setCode(regionInfo.getCode());
                                return commonInfoDto;
                            }).collect(Collectors.toList())
                    );
                    applicableRuleDataInfoDtos.add(regionApplicableRuleDataInfoDto);
                }
                if (!CollectionUtils.isEmpty(applicableHotelInfoRequest.getHotelInfos())) {
                    ApplicableRuleDataInfoDto hotelApplicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
                    hotelApplicableRuleDataInfoDto.setMasterType(MasterTypeEnum.BLOC.getCode());
                    hotelApplicableRuleDataInfoDto.setMasterCode(userInfoDto.getBlocCode());
                    hotelApplicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
                    hotelApplicableRuleDataInfoDto.setApplicableRuleType(ApplicableRuleTypeEnum.HOTEL.getRuleType());
                    hotelApplicableRuleDataInfoDto.setBizData(
                            applicableHotelInfoRequest.getHotelInfos().stream().map(hotelInfo -> {
                                CommonInfoDto commonInfoDto = new CommonInfoDto();
                                commonInfoDto.setName(hotelInfo.getName());
                                commonInfoDto.setCode(hotelInfo.getCode());
                                return commonInfoDto;
                            }).collect(Collectors.toList())
                    );
                    applicableRuleDataInfoDtos.add(hotelApplicableRuleDataInfoDto);
                }
                hotelApplicableInfo.setApplicableRuleDataInfo(applicableRuleDataInfoDtos);
            }
            return hotelApplicableInfo;
        }
        return null;
    }

    QueryGiftPackGrantDetailsReq convertQueryGrantDetailsReq(QueryGiftPackGrantDetailsRequest request);

    List<GrantGiftPackDetailResponse> convertGrantGiftPackDetailResponses(List<GiftPackBatchDetailListResp> grantGiftPackDetails);
}
