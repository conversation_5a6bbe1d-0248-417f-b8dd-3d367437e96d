package com.ly.titc.pms.member.gateway.controller.customer;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.BlacklistCustomerReq;
import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.CancelBlacklistCustomerReq;
import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.CheckWhetherBlacklistReq;
import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.PageBlacklistParamReq;
import com.ly.titc.pms.customer.dubbo.entity.request.checkIn.GetStatisticsByCustomerNoReq;
import com.ly.titc.pms.customer.dubbo.entity.request.checkIn.PageCheckInRecordParamReq;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.*;
import com.ly.titc.pms.customer.dubbo.entity.response.blacklist.BlacklistInfoResp;
import com.ly.titc.pms.customer.dubbo.entity.response.checkIn.CustomerCheckInRecordResp;
import com.ly.titc.pms.customer.dubbo.entity.response.checkIn.CustomerCheckInStatisticsResp;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerDetailInfoResp;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerInfoResp;
import com.ly.titc.pms.member.gateway.converter.CustomerConverter;
import com.ly.titc.pms.member.gateway.entity.request.customer.*;
import com.ly.titc.pms.member.gateway.entity.response.customer.CustomerBlacklistResponse;
import com.ly.titc.pms.member.gateway.entity.response.customer.CustomerCheckInStatisticsResponse;
import com.ly.titc.pms.member.gateway.entity.response.customer.CustomerDetailInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.customer.CustomerInfoResponse;
import com.ly.titc.pms.member.mediator.rpc.dubbo.customer.CustomerBlacklistDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.customer.CustomerCheckInDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.customer.CustomerDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 客户管理
 *
 * <AUTHOR>
 * @date 2024/12/11 17:41
 */
@Slf4j
@RestController
@RequestMapping("/customer")
public class CustomerController {

    @Resource
    private CustomerDecorator customerDecorator;

    @Resource
    private CustomerBlacklistDecorator customerBlacklistDecorator;

    @Resource
    private CustomerCheckInDecorator customerCheckInDecorator;

    @Resource
    private CustomerConverter customerConverter;

    /**
     * 分页查询客户
     *
     * @param request
     * @return
     */
    @PostMapping("/pageCustomer")
    public Response<Pageable<CustomerInfoResponse>> pageCustomer(@RequestBody @Valid PageCustomerRequest request){
        if (request.getIsLive() == Constant.ONE) {
            PageCustomerReq req = customerConverter.convertPageCustomerReq(request);
            Pageable<CustomerInfoResp> pageable = customerDecorator.pageStayCustomer(req);
            return Response.success(PageableUtil.convert(pageable, customerInfo -> customerConverter.convertRespToResponse(customerInfo)));
        } else {
            PageCustomerReq req = customerConverter.convertPageCustomerReq(request);
            Pageable<CustomerInfoResp> pageable = customerDecorator.pageCustomer(req);
            return Response.success(PageableUtil.convert(pageable, customerInfo -> customerConverter.convertRespToResponse(customerInfo)));
        }
    }

    /**
     * 查询客户详情
     *
     * @param request
     * @return
     */
    @PostMapping("/getInfo")
    public Response<CustomerDetailInfoResponse> getCustomerInfo(@RequestBody @Valid GetByCustomerNoRequest request){
        GetByCustomerNoReq req = customerConverter.convertGetCustomerReq(request);
        CustomerDetailInfoResp customerDetailInfo = customerDecorator.getDetailByCustomerNo(req);
        CustomerDetailInfoResponse response = customerConverter.convertRespToResponse(customerDetailInfo);
        // 黑名单
        CheckWhetherBlacklistReq blacklistReq = customerConverter.convertBlacklistReq(request);
        response.setBlack(customerBlacklistDecorator.checkWhetherBlacklist(blacklistReq));
        return Response.success(response);
    }

    /**
     * 校验手机号是否被客户占有
     *
     * @param request
     * @return
     */
    @PostMapping("/checkExistByMobile")
    public Response<Boolean> checkExistByMobile(@RequestBody @Valid CheckExistByMobileRequest request){
        CheckExistByMobileReq req = customerConverter.convertCheckExistReq(request);
        return Response.success(customerDecorator.checkExistByMobile(req));
    }

    /**
     * 客户注册
     *
     * @param request
     * @return
     */
    @PostMapping("/register")
    public Response<String> register(@RequestBody @Valid RegisterCustomerRequest request){
        RegisterCustomerReq req = customerConverter.convertRegisterReq(request);
        return Response.success(customerDecorator.register(req));
    }

    /**
     * 客户信息保存
     *
     * @param request
     * @return
     */
    @PostMapping("/update")
    public Response<String> updateCustomer(@RequestBody @Valid UpdateCustomerRequest request){
        UpdateCustomerReq req = customerConverter.convertUpdateReq(request);
        return Response.success(customerDecorator.updateCustomer(req));
    }

    /**
     * 拉黑客户
     *
     * @param request
     * @return
     */
    @PostMapping("/blacklist")
    public Response<String> blacklist(@RequestBody @Valid BlacklistCustomerRequest request){
        BlacklistCustomerReq req = customerConverter.convertBlacklist(request);
        String blacklistNo = customerBlacklistDecorator.blacklist(req);
        return Response.success(blacklistNo);
    }

    /**
     * 查询客户黑名单列表
     *
     * @param request
     * @return
     */
    @PostMapping("/listBlacklistInfo")
    public Response<List<CustomerBlacklistResponse>> listBlacklistInfo(@RequestBody @Valid ListBlacklistInfoRequest request){
        PageBlacklistParamReq paramReq= customerConverter.convertPageBlacklistReq(request);
        paramReq.setPageSize(Constant.THOUSAND);
        Pageable<BlacklistInfoResp> pageable = customerBlacklistDecorator.pageBlacklist(paramReq);
        List<BlacklistInfoResp> blacklistInfos = pageable.getDatas();
        List<CustomerBlacklistResponse> list = blacklistInfos.stream().map(customerConverter::convertBlacklistResponse).collect(Collectors.toList());
        return Response.success(list);
    }

    /**
     * 取消黑名单
     *
     * @param request
     * @return
     */
    @PostMapping("/cancelBlacklist")
    public Response<String> cancelBlacklist(@RequestBody @Valid CancelBlacklistCustomerRequest request){
        CancelBlacklistCustomerReq req = customerConverter.convertCancelBlacklist(request);
        customerBlacklistDecorator.cancelBlacklist(req);
        return Response.success(null);
    }

    /**
     * 客户入住统计
     *
     * @param request
     * @return
     */
    @PostMapping("/getCheckInStatistics")
    public Response<CustomerCheckInStatisticsResponse> getCheckInStatistics(@RequestBody @Valid GetByCustomerNoRequest request){
        GetStatisticsByCustomerNoReq req = customerConverter.convertGetStatisticsReq(request);
        CustomerCheckInStatisticsResp statisticsInfo = customerCheckInDecorator.getStatisticsByCustomerNo(req);
        return Response.success(customerConverter.convertStatisticsResp(statisticsInfo));
    }

    /**
     * 查询客户入住记录
     *
     * @param request
     * @return
     */
    @PostMapping("/pageCheckInRecord")
    public Response<CustomerCheckInStatisticsResponse> pageCheckInRecord(@RequestBody @Valid PageCheckInRecordRequest request){
        PageCheckInRecordParamReq req = customerConverter.convertGetCheckInRecordReq(request);
        Pageable<CustomerCheckInRecordResp> pageable = customerCheckInDecorator.pageCheckInRecord(req);
        return Response.success(PageableUtil.convert(pageable, record -> customerConverter.convertRecordResp(record)));
    }

}
