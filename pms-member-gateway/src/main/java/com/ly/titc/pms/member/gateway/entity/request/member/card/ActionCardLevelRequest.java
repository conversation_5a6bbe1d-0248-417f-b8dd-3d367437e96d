package com.ly.titc.pms.member.gateway.entity.request.member.card;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：ActionCardLevelRequest
 * @Date：2024-11-13 16:46
 * @Filename：ActionCardLevelRequest
 */
@Data
public class ActionCardLevelRequest extends ActionBaseRequest {

    /**
     * 卡等级
     */
    @NotNull(message = "卡等级不能为空")
    private Integer cardLevel;

    /**
     * 卡id
     */
    @NotBlank(message = "卡id不能为空")
    private String cardId;

    /**
     * 卡等级名称
     */
    @NotBlank(message = "卡等级名称不能为空")
    private String cardLevelName;
}
