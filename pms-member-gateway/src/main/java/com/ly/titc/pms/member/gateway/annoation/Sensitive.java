package com.ly.titc.pms.member.gateway.annoation;

import com.ly.titc.pms.member.gateway.entity.enums.SensitiveFieldTypeEnum;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 敏感字段
 *
 * <AUTHOR>
 * @date 2024/12/4 14:44
 */
@Retention(RetentionPolicy.RUNTIME)
@Target(ElementType.FIELD)
public @interface Sensitive {

    /**
     * 敏感字段类型
     *
     * @return
     */
    SensitiveFieldTypeEnum fieldType();

}
