package com.ly.titc.pms.member.gateway.entity.request.sysConfig;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-24 13:43
 */
@Data
@Accessors(chain = true)
public class MemberPointSysConfigRequest extends BaseRequest {
    private Long id;

    /**
     * 积分有效期
     */
    private Integer pointLimit;


    /**
     * 积分有效期单位 年 YEAR 月:MONTH 日; DAY
     */
    private String pointLimitUnit;

    /**
     * 长期有效 0 否 1 是
     */
    @NotNull(message = "是否长期有效")
    private Integer pointLimitLong;
}
