package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.cashier.dubbo.entity.request.CashierStaticQrCodeReq;
import com.ly.titc.cashier.dubbo.entity.request.config.PayCashierConfigSaveReq;
import com.ly.titc.cashier.dubbo.entity.response.CashierStaticQrCodeResp;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.cashier.CashierConfigRequest;
import com.ly.titc.pms.member.gateway.entity.request.cashier.RequestBodyPayCashierConfigSaveRequest;
import com.ly.titc.pms.member.gateway.entity.request.cashier.StaticQrCodeRequest;
import com.ly.titc.pms.member.gateway.entity.response.cashier.PayCashierConfigResponse;
import com.ly.titc.pms.member.gateway.entity.response.cashier.StaticQrCodeResponse;
import com.ly.titc.pms.member.gateway.entity.response.cashier.TermListResponse;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.PayCashierConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.PayCashierConfigQueryDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.TermListDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-3 19:36
 */
@Mapper(componentModel = "spring")
public interface MemberCashierConverter extends BaseConverter {

    BaseDto convert(BaseRequest request);

    PayCashierConfigQueryDto convert(CashierConfigRequest request);

    List<TermListResponse> convert(List<TermListDto> dtos);

    PayCashierConfigResponse convert(PayCashierConfigDto dto);

    @Mappings({
            @Mapping(target = "tradeNo",source = "tradeOperatorNo")
    })
    CashierStaticQrCodeReq convert(StaticQrCodeRequest request);

    StaticQrCodeResponse convert(CashierStaticQrCodeResp resp);

    PayCashierConfigSaveReq convert(RequestBodyPayCashierConfigSaveRequest request);



}
