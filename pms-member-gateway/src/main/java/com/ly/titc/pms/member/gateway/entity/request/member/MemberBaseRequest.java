package com.ly.titc.pms.member.gateway.entity.request.member;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotEmpty;

/**
 * @Author：rui
 * @name：MemberBaseRequest
 * @Date：2024-12-10 20:24
 * @Filename：MemberBaseRequest
 */
@Data
public class MemberBaseRequest extends BaseRequest {

    /**
     * 归属类型
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员编号
     */
    @NotEmpty(message = "会员号不能为空")
    private String memberNo;
}
