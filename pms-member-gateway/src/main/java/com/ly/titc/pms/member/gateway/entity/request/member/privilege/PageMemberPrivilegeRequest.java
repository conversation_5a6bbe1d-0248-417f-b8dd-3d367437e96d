package com.ly.titc.pms.member.gateway.entity.request.member.privilege;

import com.ly.titc.pms.member.dubbo.entity.request.BasePageReq;
import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;

/**
 * @Author：rui
 * @name：PageMemberPrivilegeReq
 * @Date：2024-11-14 19:58
 * @Filename：PageMemberPrivilegeReq
 */
@Data
public class PageMemberPrivilegeRequest extends PageBaseRequest {

    /**
     * 权益分类 1 价格权益 2 积分权益 3 线下权益 4 生态权益
     */
    private Integer type;

    /**
     * 权益名称
     */
    private String name;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;
}
