package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.*;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Mapper(componentModel = "spring")
public interface CouponCommonConverter {

    CouponTypeInfoResponse convertCouponTypeInfoResponse(CouponTypeResp couponTypeInfoDto);
    List<CouponTypeInfoResponse> convertCouponTypeInfoResponses(List<CouponTypeResp> couponTypeInfos);

    CouponTemplateStateResponse convertCouponTemplateStateResponse(CouponTemplateStateResp dto);
    List<CouponTemplateStateResponse> convertCouponTemplateStateResponses(List<CouponTemplateStateResp> infos);

    CouponSourceClientResponse convertCouponSourceClientResponse(CouponSourceClientResp resp);
    List<CouponSourceClientResponse> convertCouponSourceClientResponses(List<CouponSourceClientResp> infos);

    CouponValueTypeResponse convertCouponValueTypeResponse(CouponValueTypeResp resp);
    List<CouponValueTypeResponse> convertCouponValueTypeResponses(List<CouponValueTypeResp> infos);

    CouponPostingTypeResponse convertCouponPostingTypeResponse(CouponPostingTypeResp resp);
    List<CouponPostingTypeResponse> convertCouponPostingTypeResponses(List<CouponPostingTypeResp> infos);

    CouponRedeemTypeResponse convertCouponRedeemTypeResponse(CouponRedeemTypeResp resp);
    List<CouponRedeemTypeResponse> convertCouponRedeemTypeResponses(List<CouponRedeemTypeResp> infos);
}
