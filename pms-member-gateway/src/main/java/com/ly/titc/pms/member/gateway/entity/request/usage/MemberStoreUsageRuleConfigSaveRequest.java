package com.ly.titc.pms.member.gateway.entity.request.usage;

import com.ly.titc.pms.ecrm.dubbo.entity.request.member.usageRule.MemberUsageRuleConfigBaseReq;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-29 10:01
 */
@Data
@Accessors(chain = true)
public class MemberStoreUsageRuleConfigSaveRequest extends MemberUsageRuleConfigBaseRequest {

    /**
     * 可用场景
     */
//    @NotEmpty(message = "可用场景不能为空")
    private List<String> scenes;

    /**
     * 储值扣款模式 1 优先本金 2 优先礼金 3 比例扣减
     */
    @NotNull(message = "储值扣款模式不能为空")
    private Integer deductionType;

    /**
     * 礼金扣减比例
     */
    private String deductionRatio;


}
