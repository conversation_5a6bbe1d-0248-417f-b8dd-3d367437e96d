package com.ly.titc.pms.member.gateway.entity.response.member.profile;

import com.baomidou.mybatisplus.annotation.TableLogic;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：MemberCommonInvoiceHeaderResponse
 * @Date：2024-11-18 14:11
 * @Filename：MemberCommonInvoiceHeaderResponse
 */
@Data
public class MemberCommonInvoiceHeaderResponse {

    /**
     * 发票抬头编号
     */
    private String invoiceHeaderNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 发票类型；1:个人;2:企业;3:非企业性单位
     */
    private Long invoiceType;

    /**
     * 发票抬头
     */
    private String headerName;

    /**
     * 税号
     */
    private String taxCode;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 公司电话
     */
    private String companyTel;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;
}
