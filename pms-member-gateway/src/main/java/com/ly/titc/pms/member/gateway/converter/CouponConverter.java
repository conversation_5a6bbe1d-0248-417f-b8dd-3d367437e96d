package com.ly.titc.pms.member.gateway.converter;

import cn.hutool.core.util.ReUtil;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.request.hotel.PageHotelsReq;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.*;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.*;
import com.ly.titc.pms.spm.dubbo.enums.CouponTypeEnum;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-14 9:53
 */
@Mapper(componentModel = "spring")
public interface CouponConverter {

    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "currPage", source = "pageIndex")
    QueryCouponListReq convertPageCouponRequest2ApiReq(PageCouponRequest req);

    default List<CouponExportResponse> convertCouponExportResponse(List<CouponListResp> couponListRespList){
        return couponListRespList.stream().map(e->{
            CouponExportResponse couponExportResponse = new CouponExportResponse();
            couponExportResponse.setCouponCode(e.getCouponCode());
            couponExportResponse.setCouponType(CouponTypeEnum.getEnum(e.getCouponType()).getDesc());
            couponExportResponse.setCouponName(e.getCouponName());
            couponExportResponse.setCouponValue(e.getCouponValue());
//            couponExportData.setGenerateObject();
            couponExportResponse.setIssueEvent(e.getIssueEvent());
            couponExportResponse.setIssueDate(e.getIssueDate());
//            couponExportData.setEffectiveTime();
//            couponExportData.setState();
//            couponExportData.setOwner();
            couponExportResponse.setUseTime(e.getUseTime());
            return couponExportResponse;
        }).collect(Collectors.toList());
    }

    CouponListResponse convertCouponListResp2Response(CouponListResp d);

    GrantCouponBatchReq convertGrantCouponRequest2ApiReq(GrantCouponRequest req);

    @Mapping(target = "currPage", source = "pageIndex")
    @Mapping(target = "createUser", source = "operator")
    QueryCouponBatchListReq convertPageBatchRequest2ApiReq(PageCouponBatchRequest req);

    @Mapping(target = "crossShop",source = "isAllowCrossStore")
    @Mapping(target = "smsNotice",source = "isSmsNotification")
    @Mapping(target = "grantTime",source = "grantDate")
    @Mapping(target = "operator",source = "createUser")
    @Mapping(target = "sourceBizCode",source = "sourceClient")
    CouponBatchListResponse convertCouponBatchListResp2Response(CouponBatchListResp d);

    @Mapping(target = "currPage", source = "pageIndex")
    QueryCouponBatchDetailsReq convertPageBatchDetailsRequest2ApiReq(PageCouponBatchDetailsRequest req);

    default List<CouponExportSimpleResponse> convertCouponExportSimpleResponse(List<CouponBatchDetailResp> couponBatchDetailResps){
        return couponBatchDetailResps.stream().map(e->{
            CouponExportSimpleResponse couponExportSimpleResponse = new CouponExportSimpleResponse();
            couponExportSimpleResponse.setCouponCode(e.getCouponCode());
//            couponExportSimpleData.setHotelName();
//            couponExportSimpleData.setCustomerName();
//            couponExportSimpleData.setCustomerNo();
//            couponExportSimpleData.setMemberCardNo();
//            couponExportSimpleData.setMobile();
            return couponExportSimpleResponse;
        }).collect(Collectors.toList());
    }

    CouponBatchDetailResponse convertCouponBatchDetailResp2Response(CouponBatchDetailResp d);

    default Pageable<CouponBatchDetailResponse> convertPageableCouponBatchDetailResponse(Pageable<CouponBatchDetailResp> pageable){
        return PageableUtil.convert(pageable, d->{
            CouponBatchDetailResponse couponBatchDetailResponse = convertCouponBatchDetailResp2Response(d);
//            couponBatchDetailResponse.setHotelName();
//            couponBatchDetailResponse.setMobile();
//            couponBatchDetailResponse.setCustomerName();
//            couponBatchDetailResponse.setMemberCardNo();
//            couponBatchDetailResponse.setCustomerNo();
            return couponBatchDetailResponse;
        });
    }

    default CouponDetailResponse convertCouponDetailResponse(CouponIssueResp couponGrantInfo){
        CouponDetailResponse couponDetailResponse = new CouponDetailResponse();
        couponDetailResponse.setGrantTime(couponGrantInfo.getGrantDate());
        String receiverCode = couponGrantInfo.getReceiverCode();
        String receiverType = couponGrantInfo.getReceiverType();
        //TODO 依赖客户服务
//        couponDetailResp.setOwner();
//        couponDetailResp.setMobile();
//        couponDetailResp.setOwnerCustomerNo();
//        couponDetailResp.setOwnerMemberCardNo();
        return couponDetailResponse;
    }

    GetCouponInfoReq convertGetCouponRequest2GetRedeemReq(GetCouponRequest request);

    CouponRedeemInfoResponse convertCouponRedeemInfoResp2Response(CouponRedeemInfoResp apiResp);


    default Pageable<CouponListResponse> convertPageableCouponListResponse(Pageable<CouponListResp> pageable){
        return PageableUtil.convert(pageable, d->{
            CouponListResponse couponListResponse = convertCouponListResp2Response(d);
            couponListResponse.setCouponTypeName(CouponTypeEnum.getEnum(d.getCouponType()).getDesc());
//            couponListResponse.setHotelName();
//            couponListResponse.setOwner();
            return couponListResponse;
        });
    }

    default PageHotelsReq convertPageHotelsReq(PageCouponApplicableHotelsRequest request, List<String> hotelCodes, String blocCode){
        PageHotelsReq pageHotelsReq = new PageHotelsReq();
        pageHotelsReq.setPageIndex(request.getPageIndex());
        pageHotelsReq.setPageSize(request.getPageSize());
        pageHotelsReq.setHotelVids(hotelCodes.stream().map(Long::valueOf).collect(Collectors.toList()));
        if(StringUtils.isNotBlank(request.getHotelInput())){
            boolean isMatch = ReUtil.isMatch(".*[\\u4e00-\\u9fa5]+.*", request.getHotelInput());
            if(isMatch){
                pageHotelsReq.setHotelName(request.getHotelInput());
            }else{
                pageHotelsReq.setHotelCode(request.getHotelInput());
            }
        }
        pageHotelsReq.setBlocCode(blocCode);
        return pageHotelsReq;
    }

    default Pageable<CouponApplicableHotelResponse> convertPageableCouponApplicableHotelResponse(Pageable<PageHotelsResp> pageHotelsRespPageable){
        Pageable<CouponApplicableHotelResponse> result = new Pageable<>();
        result.setDatas(pageHotelsRespPageable.getDatas().stream().map(pageHotelsResp -> {
            CouponApplicableHotelResponse couponApplicableHotelResp = new CouponApplicableHotelResponse();
            couponApplicableHotelResp.setHotelCode(pageHotelsResp.getHotelCode());
            couponApplicableHotelResp.setHotelName(pageHotelsResp.getHotelName());
            return couponApplicableHotelResp;
        }).collect(Collectors.toList()));
        result.setCurrPage(pageHotelsRespPageable.getCurrPage());
        result.setTotal(pageHotelsRespPageable.getTotal());
        result.setTotalPage(pageHotelsRespPageable.getTotalPage());
        return result;
    }

    QueryCouponStatisticsReq convertCouponStatisticsRequest2ApiReq(CouponStatisticsRequest req);

    CouponStatisticsResponse convertCouponStatisticsResponse(CouponStatisticsResp apiResp);
}
