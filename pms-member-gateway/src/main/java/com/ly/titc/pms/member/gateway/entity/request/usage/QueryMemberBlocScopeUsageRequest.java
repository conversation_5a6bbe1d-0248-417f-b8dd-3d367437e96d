package com.ly.titc.pms.member.gateway.entity.request.usage;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-11 11:10
 */
@Data
@Accessors(chain = true)
public class QueryMemberBlocScopeUsageRequest extends PageBaseRequest {

    /**
     * 集团code
     */
    @NotEmpty(message = "集团code不能为空")
    private String blocCode;

    /**
     * 适用来源
     */
    @NotEmpty(message = "适用来源不能为空")
    private List<String> scopeSources;

    @NotEmpty(message = "适用来源code不能为空")
    private List<String> scopeSourceCodes;

    /**
     * 适用平台渠道
     */
    @NotEmpty(message = "适用平台渠道不能为空")
    private List<String> scopePlatformChannels;



}
