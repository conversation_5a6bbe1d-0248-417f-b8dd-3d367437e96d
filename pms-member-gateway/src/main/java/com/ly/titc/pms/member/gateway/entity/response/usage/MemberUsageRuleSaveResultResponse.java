package com.ly.titc.pms.member.gateway.entity.response.usage;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-29 14:54
 */
@Data
@Accessors(chain = true)
public class MemberUsageRuleSaveResultResponse {
    /**
     * 配置主键ID
     */
    private Long ruleId;
    /**
     * 主体（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 主体code
     */
    private String masterCode;


    /**
     * 适用渠道
     */
    private String scopePlatformChannel;
    /**
     * 适用渠道名称
     */
    private String scopePlatformChannelName;

    /**
     * 适用来源
     */
    private String scopeSource;

    /**
     * 适用门店
     */
    private String scopeHotelCode;


    /**
     * 适用们店名称
     */
    private String scopeHotelName;

}
