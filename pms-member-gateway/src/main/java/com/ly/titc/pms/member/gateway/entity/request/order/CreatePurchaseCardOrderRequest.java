package com.ly.titc.pms.member.gateway.entity.request.order;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 购卡订单
 *
 * <AUTHOR>
 * @date 2024/12/10 14:04
 */
@Data
@Accessors(chain = true)
public class CreatePurchaseCardOrderRequest extends CreateOrderBaseRequest{

    /**
     * 会员卡ID
     */
    @NotNull(message = "会员卡ID不能为空")
    private Long cardId;

    /**
     * 会员卡名称
     */
    @NotBlank(message = "会员卡名称不能为空")
    private String cardName;

    /**
     * 会员卡类型
     */
    @NotNull(message = "会员卡类型不能为空")
    private Integer cardType;

    /**
     * 会员卡等级
     */
    @NotNull(message = "会员卡等级不能为空")
    private Integer cardLevel;

    /**
     * 会员卡等级名称
     */
    @NotBlank(message = "会员卡等级名称不能为空")
    private String cardLevelName;

    /**
     * 会员卡号
     */
    @NotBlank(message = "会员卡号不能为空")
    private String memberCardNo;

}
