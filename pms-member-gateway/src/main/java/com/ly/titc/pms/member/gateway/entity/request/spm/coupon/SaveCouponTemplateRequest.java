package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-6
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveCouponTemplateRequest extends BaseRequest {

    /**
     * 模板code
     */
    private String templateCode;

    /**
     * 券名称
     */
    @NotBlank(message = "券名称不能为空")
    private String couponName;

    /**
     * 券类型 1.折扣券,2.代金券,3.免房券,4.餐饮券,5.抵扣券,6.小时券,7.延时券,8.升房券,9.体验券,10.通用券
     */
    @NotNull(message = "券类型不能为空")
    private Integer couponType;

    /**
     * 券值类型1.固定金额 2-折扣率 3-延长小时 4-延长小时点 5-券后房价
     */
    private Integer couponValueType;

    /**
     * 券值 0-不限
     */
    private String couponValue;

    /**
     * 券使用提醒
     */
    private String couponTips;

    /**
     * 券使用说明
     */
    private String couponContent;

    /**
     * 成本归属 1-集团 2-门店
     */
    @NotNull(message = "券成本不能为空")
    private Integer costAttribution;

    /**
     * 核销方式 0-全部 1-仅券号 2-二维码 3-一维码
     */
    @NotNull(message = "核销方式不能为空")
    private Integer redeemType;

    /**
     * 总库存 默认为-1 (-1-不限制)
     */
    private Integer totalQuantity;

    /**
     * 备注
     */
    private String remark;

    /**
     * 上一个操作人 (用于校验权限 编辑的时候需要传)
     */
    private Long preOperatorId;

    /**
     * 入账方式 0-不入账 1-消费项 2-结算项
     */
    private Integer postingType = 0;

    /**
     * 入账项code
     */
    private String postingItemCode;

    /**
     * 是否生效一个房晚 0-否 1-是
     */
    private Integer isEffectOneNight;

    /**
     * 夜审或加收房费前是否允许取消使用券
     */
    private Boolean nightAuditBeforeCancel;

    /**
     * 订单取消后返回
     */
    private Boolean orderCancelReturn;

    /**
     * 券有效期配置
     */
    @Valid
    private CouponEffectDateConfigRequest effectDateInfo;


    /**
     * 券使用规则
     */
    @Valid
    private CouponUseRuleRequest useRuleInfo;


    /**
     * 优惠券发放规则
     */
    @Valid
    private CouponGrantRuleRequest grantRuleInfo;
}
