package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import lombok.Data;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-5
 */
@Data
public class CouponEffectDateConfigResponse{

    /**
     * 有效期 券生效日期类型 1.固定日期区间; 2.按领取日期（自领取后按天算
     */
    private Integer effectDateType;

    /**
     * 券生效日期 （固定日期区间）
     */
    private String startDate;

    /**
     * 券结束日期 （固定日期区间）
     */
    private String endDate;

    /**
     * 是否永久有效 true-永久有效 false-非永久有效（固定日期区间） 2099-12-31
     */
    private Boolean isPerpetualEffect;

    /**
     * 领取后多少天开始生效 0-立即生效 (按领取日期（自领取后按天算)
     */
    private Integer effectFixStartDay;

    /**
     * 领取后多少天有效 (按领取日期（自领取后按天算)
     */
    private Integer effectFixEndDay;

    /**
     * 统一过期日期；yyyy-MM-dd 归属于按领取日期类型 (按领取日期（自领取后按天算)
     */
    private String unifiedEndDate;

}
