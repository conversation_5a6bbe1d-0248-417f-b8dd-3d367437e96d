package com.ly.titc.pms.member.gateway.entity.request.member.tag;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：PageMemberTagConfigInfoRequest
 * @Date：2024-11-15 9:57
 * @Filename：PageMemberTagConfigInfoRequest
 */
@Data
public class PageMemberTagConfigInfoRequest extends PageBaseRequest {

    private String name;

    private List<Integer> type;
}
