package com.ly.titc.pms.member.gateway.entity.request.member.card;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：SaveCardLevelUpgradeRuleRequest
 * @Date：2024-11-12 17:47
 * @Filename：SaveCardLevelUpgradeRuleRequest
 */
@Data
public class SaveCardLevelUpgradeRuleRequest extends BaseRequest {

    /**
     * 编辑时必传
     */
    private Long id;

    /**
     * 名称
     */
    private String name;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 原等级
     */
    private Integer sourceLevel;

    /**
     * 目标等级
     */
    private Integer targetLevel;

    /**
     * 统计逻辑 ALL - 全部  ANY-满足任何一个条件
     */
    private String successfulPerformType;

    /**
     * 统计范围 1 会员注册器 2 上次升降级起
     */
    private Integer cycleType;

    /**
     * 描述
     */
    private String description;

    /**
     * 排序值，越小越靠前
     */
    private Integer sort;

    /**
     * 指标列表
     */
    private List<CardLevelUpgradeRuleDetailRequest> details;

}
