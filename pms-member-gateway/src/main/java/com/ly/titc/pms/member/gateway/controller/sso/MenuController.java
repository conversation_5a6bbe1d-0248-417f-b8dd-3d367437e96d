package com.ly.titc.pms.member.gateway.controller.sso;


import com.ly.titc.common.entity.Response;
import com.ly.titc.oauth.client.service.MenuInfoService;
import com.ly.titc.oauth.entity.response.menu.MenuResp;
import com.ly.titc.pms.member.gateway.converter.MenuConverter;
import com.ly.titc.pms.member.gateway.entity.request.menu.MenuRequest;
import com.ly.titc.pms.member.gateway.entity.response.menu.MenuInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.menu.MenuResponse;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.oauth.MenuDecorator;
import com.ly.titc.sso.entity.dto.AccountDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 查询页面菜单以及按钮相关数据接口
 */
@Slf4j
@RestController
@RequestMapping(value = "menu")
public class MenuController {

    /**
     * 应用接入账号密码
     */
    @Resource(type = AccountDto.class)
    private AccountDto accountDto;

    @Resource(type = MenuInfoService.class)
    private MenuInfoService menuInfoService;

    @Resource(type = MenuConverter.class)
    private MenuConverter menuConverter;

    @Resource
    private MenuDecorator menuDecorator;

    /**
     * sso2.0 获取菜单列表
     *
     * @return
     */
    @PostMapping(value = "/newList")
    public Response<MenuResponse> listNewMenus(@RequestBody MenuRequest request) {
        UserInfoDto user = UserInfoThreadHolder.getUser();
        MenuResponse menu = new MenuResponse();
        if (null != user){
            String tenantCode = user.getTenantCode();
            String blocCode = user.getBlocCode();
            Long userId = user.getUserId();
            MenuResp resp = menuDecorator.listTrees(tenantCode, blocCode, userId, accountDto.getProjectCode(), request.getResourceId());
            if (null != resp){
                menu = menuConverter.convert2Response(resp);
            }else{
                List<MenuInfoResponse> menuTrees = new ArrayList<>();
                menu.setMenuTrees(menuTrees);
                menu.setPages(menuTrees);
                menu.setUrls(new ArrayList<>());
            }
        }
        return Response.success(menu);
    }
}
