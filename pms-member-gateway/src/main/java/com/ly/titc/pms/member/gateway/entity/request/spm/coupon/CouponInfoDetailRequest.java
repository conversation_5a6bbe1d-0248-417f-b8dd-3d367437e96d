package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-6 17:31
 */
@Data
@Accessors(chain = true)
public class CouponInfoDetailRequest extends BaseRequest {

    @NotBlank
    private String couponCode;
}
