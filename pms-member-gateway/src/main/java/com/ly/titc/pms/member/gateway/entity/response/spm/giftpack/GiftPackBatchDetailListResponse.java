package com.ly.titc.pms.member.gateway.entity.response.spm.giftpack;

import com.ly.titc.pms.member.gateway.annoation.Sensitive;
import com.ly.titc.pms.member.gateway.entity.enums.SensitiveFieldTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-29 10:41
 */
@Data
@Accessors(chain = true)
public class GiftPackBatchDetailListResponse {

    /**
     * 礼包明细产品类型
     * 1-优惠券 2-会员卡 3-积分 4-会员权益 5-联名权益
     */
    private Integer grantItemType;

    /**
     * 明细业务类型描述
     */
    private String grantItemTypeDesc;

    /**
     * 明细业务编码(产品ID)
     */
    private String grantBizNo;

    /**
     * 会员名
     */
    private String memberName;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 手机号
     */
    @Sensitive(fieldType = SensitiveFieldTypeEnum.MOBILE)
    private String mobile;
}
