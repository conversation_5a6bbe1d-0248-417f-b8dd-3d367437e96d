package com.ly.titc.pms.member.gateway.entity.request.cashier;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-4 9:47
 */
@Data
@Accessors(chain = true)
public class CashierConfigRequest extends BaseRequest {

    @NotEmpty(message = "收银场景不能为空")
    private String memberScene;

}
