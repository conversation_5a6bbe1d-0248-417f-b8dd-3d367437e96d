package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.gateway.converter.MemberDataConverter;
import com.ly.titc.pms.member.gateway.entity.request.member.MemberBaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.record.PageCheckinRecordRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.record.PageLevelChangeRecordRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.record.PagePurchaseCardRecordRequest;
import com.ly.titc.pms.member.gateway.entity.response.data.CheckInStatisticsResponse;
import com.ly.titc.pms.member.gateway.entity.response.data.CheckinRecordResponse;
import com.ly.titc.pms.member.gateway.entity.response.data.MemberLevelChangeRecordResponse;
import com.ly.titc.pms.member.gateway.entity.response.data.PurchaseCardRecordResponse;
import com.ly.titc.pms.member.mediator.entity.dto.data.CheckinRecordDto;
import com.ly.titc.pms.member.mediator.entity.dto.data.MemberLevelChangeRecordDto;
import com.ly.titc.pms.member.mediator.entity.dto.data.PurchaseCardRecordDto;
import com.ly.titc.pms.member.mediator.service.MemberDataRecordMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 会员数据记录
 * @Author：rui
 * @name：MemberDataController
 * @Date：2024-12-11 21:22
 * @Filename：MemberDataController
 */
@Slf4j
@RestController
@RequestMapping("/member/data")
public class MemberDataController {

    @Resource
    private MemberDataConverter converter;

    @Resource
    private MemberDataRecordMedService memberDataRecordMedService;

    /**
     * 查询会员入住记录
     */
    @RequestMapping("/pageCheckinRecord")
    public Response<Pageable<CheckinRecordResponse>> pageCheckinRecord(@RequestBody @Valid PageCheckinRecordRequest request) {
        Pageable<CheckinRecordDto> pageable = memberDataRecordMedService.pageCheckinRecord(converter.convertPageCheckinRecordDto(request));
        return Response.success(Pageable.convert(pageable, converter.convertPageCheckinRecord(pageable.getDatas())));
    }

    /**
     * 查询会员入住统计
     */
    @RequestMapping("/getCheckInStatistics")
    public Response<CheckInStatisticsResponse> getCheckInStatistics(@RequestBody @Valid MemberBaseRequest request) {
        return Response.success(converter.convertCheckInStatistics(memberDataRecordMedService.queryMemberCheckInStatistics(request.getMemberNo())));
    }

    /**
     * 查询会员购卡记录
     */
    @RequestMapping("/pagePurchaseCardRecord")
    public Response<Pageable<PurchaseCardRecordResponse>> pagePurchaseCardRecord(@RequestBody @Valid PagePurchaseCardRecordRequest request) {
        TwoTuple<Integer, String> tuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        Pageable<PurchaseCardRecordDto> pageable = memberDataRecordMedService.pagePurchaseCardRecordDto(converter.convertPagePurchaseCardRecordDto(request, tuple.getFirst(), tuple.getSecond()));
        return Response.success(Pageable.convert(pageable, converter.convertPurchaseCardRecordResponse(pageable.getDatas())));
    }

    /**
     * 查询会员升降级记录
     */
    @RequestMapping("/pageLevelChangeRecord")
    public Response<Pageable<MemberLevelChangeRecordResponse>> pageLevelChangeRecord(@RequestBody @Valid PageLevelChangeRecordRequest request) {
        TwoTuple<Integer, String> tuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        Pageable<MemberLevelChangeRecordDto> pageable = memberDataRecordMedService.pageMemberLevelChangeRecord(converter.convertPageLevelChangeRecordDto(request, tuple.getFirst(), tuple.getSecond()));
        return Response.success(Pageable.convert(pageable, converter.convertMemberLevelChangeRecordResponse(pageable.getDatas())));
    }

}
