package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.pms.spm.dubbo.entity.dto.activity.common.MemberActivityCommonDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * 保存积分活动
 *
 * <AUTHOR>
 * @date 2024/12/28 14:19
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SavePointsActivityRequest extends MemberActivityCommonRequest {

    /**
     * 消费金额
     */
    private Integer exchangeAmount;
    /**
     * 兑换积分
     */
    private Integer exchangePoint;

    /**
     * 发放节点
     */
    private List<String> issueNodes;

}
