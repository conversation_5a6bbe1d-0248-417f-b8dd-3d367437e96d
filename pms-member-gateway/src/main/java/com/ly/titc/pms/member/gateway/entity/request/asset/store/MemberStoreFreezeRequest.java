package com.ly.titc.pms.member.gateway.entity.request.asset.store;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * 储值冻结
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-20 10:33
 */
@Data
@Accessors(chain = true)
public class MemberStoreFreezeRequest extends BaseRequest {

    @NotEmpty(message = "会员号不能为空")
    private String memberNo;


    /**
     * 本金金额
     */
    @NotNull(message = "本金金额不能为空")
    private BigDecimal capitalAmount;

    /**
     * 礼金金额
     */
    @NotNull(message = "礼金金额不能为空")
    private BigDecimal giftAmount;
    /**
     * 冻结原因
     */
    @NotEmpty(message = "冻结原因不能为空")
    private String goodsDes;

    /**
     * 冻结有效期 yyyy-mm-dd
     */
    private String freezeDate;

    /**
     * 是否长期冻结 0否 1是
     */
    @NotNull(message = "是否长期冻结不能为空")
    private Integer isFreezeLong;


    /**
     * 会员密码
     */
    private String password;


    /**
     * 备注
     */
    private String remark;
}
