package com.ly.titc.pms.member.gateway.entity.request.asset.point;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-20 10:33
 */
@Data
@Accessors(chain = true)
public class MemberPointAdjustRequest extends BaseRequest {

    /**
     * 积分调整项目
     */
    @NotBlank(message = "积分调整项目不能为空")
    private String actionItem;

    /**
     * 积分项目描述
     */
    @NotBlank(message = "积分项目描述不能为空")
    private String actionItemDesc;

    /**
     * 积分数（加为正，减为负）
     */
    @NotNull(message = "积分数不能为空")
    private Integer score;

    /**
     * 会员编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 调整原因
     */
    @NotBlank(message = "调整原因不能为空")
    private String remark;
}
