package com.ly.titc.pms.member.gateway.entity.response.member;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberPrivilegeConfigDetailResponse
 * @Date：2024-11-14 20:21
 * @Filename：MemberPrivilegeConfigDetailResponse
 */
@Data
public class MemberPrivilegeConfigDetailResponse {

    /**
     * 权益ID
     */
    private Long privilegeId;

    /**
     * 权益类型 1.仅作展示 2 价格折扣 3 预定保留 4 延迟退房
     */
    private Integer classification;

    /**
     * 适用类型 1 集团 2 品牌 3 门店
     */
    private Integer applicationType;

    /**
     * 适用范围 0 全部 1 部分
     */
    private Integer scopeType;

    /**
     * 适用范围值
     */
    private List<String> scopeValues;

    /**
     * 适用范围值 名称+品牌
     */
    private List<ScopeValueResponse> scopeValueResponses;

    /**
     * 权益值
     */
    private String value;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;
}
