package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：DeleteCommonInvoiceHeader
 * @Date：2024-11-18 14:24
 * @Filename：DeleteCommonInvoiceHeader
 */
@Data
public class DeleteCommonInvoiceHeaderRequest extends BaseRequest {

    /**
     * 发票抬头编号
     */
    @NotNull(message = "发票抬头编号不可为空")
    private String invoiceHeaderNo;

    /**
     * 会员号
     */
    @NotBlank(message = "会员号不可为空")
    private String memberNo;

    /**
     * 发票抬头
     */
    @NotBlank(message = "发票抬头不可为空")
    private String headerName;
}
