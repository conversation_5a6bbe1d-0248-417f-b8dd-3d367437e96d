package com.ly.titc.pms.member.gateway.entity.response.sso;


import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description:
 * @Author: lixu
 * @Date: 2023/4/17
 */
@Data
@Accessors(chain = true)
public class UserInfoResponse {
    /**
     * 租户Code
     */
    private String tenantCode;

    /**
     * 租户名称
     */
    private String tenantName;

    /**
     * 租户类型 类型 1-内部 2-外部 3-酒管组
     */
    private int tenantType;

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 集团名称
     */
    private String blocName;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 用户名称
     */
    private String userName;

    /**
     * 用户账号
     */
    private String userAccount;

    /**
     * 用户手机号
     */
    private String mobile;

    /**
     * 用户邮箱地址
     */
    private String email;

    /**
     * 微信openId
     */
    private String weChatId;

    /**
     * 微信昵称
     */
    private String weChatNickName;

    /**
     * 微信头像
     */
    private String headImgUrl;

    /**
     * 是否是超管 超管 / 租户管理员
     */
    private Boolean isAdmin;

    private String jobNo;
}
