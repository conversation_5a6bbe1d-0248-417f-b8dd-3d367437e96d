package com.ly.titc.pms.member.gateway.handler.log;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.pms.member.com.annotation.LogRecordFunction;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.entity.request.member.profile.DeleteMemberProfileTagRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.profile.MemberProfileTagRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.profile.SaveMemberProfileTagRequest;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberInfoDto;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberTagProfileLogFunctionHandler
 * @Date：2024-11-18 20:47
 * @Filename：MemberTagProfileLogFunctionHandler
 */
@LogRecordFunction("memberTagProfile")
public class MemberTagProfileLogFunctionHandler extends AbstractModuleServiceHandler implements ApplicationContextAware {

    private static MemberMedService memberMedService;

    @Override
    public List<String> execute(String blocCode, String hotelCode, String name, String trackingId) {
        return null;
    }

    @Override
    public Integer getModuleId() {
        return null;
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        memberMedService = applicationContext.getBean(MemberMedService.class);
    }

    @LogRecordFunction("addMemberProfileTag")
    public static String addMemberProfileTag(SaveMemberProfileTagRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("");
        String tagName = String.join("、", request.getMemberProfileTagList().stream().map(MemberProfileTagRequest::getTagName)
                        .collect(Collectors.toList()));
        stringBuilder.append(String.format("【%s】-【%s】新增标签:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), tagName));
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("deleteMemberProfileTag")
    public static String deleteMemberProfileTag(DeleteMemberProfileTagRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo());
        String memberNo = request.getMemberNo();
        JSONObject json = new JSONObject();
        json.put("name", memberNo);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(String.format("【%s】-【%s】删除标签:%s", memberInfo.getRealName(), memberInfo.getMemberNo(), request.getTagName()));
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }
}
