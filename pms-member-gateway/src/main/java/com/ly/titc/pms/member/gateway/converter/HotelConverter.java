package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.mdm.entity.request.hotel.PageHotelsReq;
import com.ly.titc.mdm.entity.request.hotel.SelectHotelsByFuzzyReq;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.gateway.entity.request.hotel.SelectHotelsByFuzzyRequest;
import com.ly.titc.pms.member.gateway.entity.response.SelectResponse;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.util.List;

/**
 * @Author：rui
 * @name：HotelWebConverter
 * @Date：2024-11-12 20:05
 * @Filename：HotelWebConverter
 */
@Mapper(componentModel = "spring")
public interface HotelConverter extends BaseConverter {

    @Mappings({
            @Mapping(target = "value", source = "hotelCode"),
            @Mapping(target = "text", source = "hotelName")
    })
    SelectResponse convertHotelInfoResp2SelectResponse(HotelBaseInfoResp hotelBaseInfoResp);
    List<SelectResponse> convertHotelInfoResp2SelectResponse(List<HotelBaseInfoResp> hotelBaseInfoRespList);


    @Mapping(target = "fuzzyNameAndCode", source = "fuzzyNameOrCode")
    SelectHotelsByFuzzyReq convertSelectHotelsByFuzzyReq(SelectHotelsByFuzzyRequest request);

    @Mappings({
            @Mapping(target = "value", source = "hotelCode"),
            @Mapping(target = "text", source = "hotelName")
    })
    SelectResponse convertSelectResponseByPageHotel(PageHotelsResp pageHotelsResp);
    List<SelectResponse> convertSelectResponseByPageHotels(List<PageHotelsResp> pageHotels);
}
