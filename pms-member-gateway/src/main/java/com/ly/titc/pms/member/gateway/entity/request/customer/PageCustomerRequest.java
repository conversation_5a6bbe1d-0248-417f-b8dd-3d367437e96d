package com.ly.titc.pms.member.gateway.entity.request.customer;

import com.ly.titc.common.annotation.LegalPhoneNumber;
import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 分页查询客户
 *
 * <AUTHOR>
 * @date 2024/12/11 19:10
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class PageCustomerRequest extends PageBaseRequest {

    /**
     * 酒店编码
     */
    @NotBlank(message = "酒店编码不能为空")
    private String hotelCode;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 手机号
     */
    @LegalPhoneNumber(message = "客户手机号不合法")
    private String mobile;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 是否查询在住客人
     */
    private Integer isLive = 1;

}
