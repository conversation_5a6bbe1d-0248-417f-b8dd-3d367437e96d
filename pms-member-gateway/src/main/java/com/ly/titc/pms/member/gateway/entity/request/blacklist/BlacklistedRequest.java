package com.ly.titc.pms.member.gateway.entity.request.blacklist;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import java.util.List;

/**
 * 拉黑
 *
 * @Author：rui
 * @name：BlacklistedRequest
 * @Date：2024-12-10 19:23
 * @Filename：BlacklistedRequest
 */
@Data
public class BlacklistedRequest extends BaseRequest {


    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 场景适用范围 0 部分 1 全部
     */
    private Integer sceneScope;

    /**
     * 适用场景列表
     */
    private List<String> scenes;

    /**
     * 渠道适用范围 0 部分 1 全部
     */
    private Integer platformChannelScope;

    /**
     * 适用渠道列表
     */
    private List<String> platformChannels;

    /**
     * 原因
     */
    private String reason;

}
