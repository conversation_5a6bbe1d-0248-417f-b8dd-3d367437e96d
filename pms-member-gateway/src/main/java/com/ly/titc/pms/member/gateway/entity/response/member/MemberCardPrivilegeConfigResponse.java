package com.ly.titc.pms.member.gateway.entity.response.member;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：MemberCardPrivilegeConfigResponse
 * @Date：2024-11-13 20:50
 * @Filename：MemberCardPrivilegeConfigResponse
 */
@Data
public class MemberCardPrivilegeConfigResponse {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 权益ID
     */
    private Long privilegeId;

    /**
     * 权益类型 1 价格权益 2 积分权益 3 线下权益 4 生态权益
     */
    private Integer type;

    /**
     * 权益名称
     */
    private String name;

    /**
     * 权益描述
     */
    private String description;

    /**
     * 权益说明
     */
    private String instruction;

    /**
     * 权益排序
     */
    private Integer sort;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;
}
