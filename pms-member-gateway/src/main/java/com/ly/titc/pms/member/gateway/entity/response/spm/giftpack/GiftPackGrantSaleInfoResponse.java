package com.ly.titc.pms.member.gateway.entity.response.spm.giftpack;

import com.ly.titc.pms.member.gateway.annoation.Sensitive;
import com.ly.titc.pms.member.gateway.entity.enums.SensitiveFieldTypeEnum;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-28 19:23
 */
@Data
@Accessors(chain = true)
public class GiftPackGrantSaleInfoResponse {

    private String giftPackNo;
    /**
     * 礼包名称
     */
    private String giftPackName;

    /**
     * 售卖类型
     */
    private Integer saleType;

    /**
     * 发放数量
     */
    private Integer grantNum;

    /**
     * 会员名
     */
    private String memberName;
    /**
     * 会员编号
     */
    private String memberNo;
    /**
     * 手机号
     */
    @Sensitive(fieldType = SensitiveFieldTypeEnum.MOBILE)
    private String mobile;

    /**
     * 付款方式
     */
    private String postingItemCode;

    /**
     * 入账门店
     */
    private String postingHotelCode;

    /**
     * 挂账房单
     */
    private String accountNo;
}
