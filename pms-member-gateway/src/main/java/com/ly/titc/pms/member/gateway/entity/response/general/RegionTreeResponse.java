package com.ly.titc.pms.member.gateway.entity.response.general;

import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-6
 */
@Data
public class RegionTreeResponse {

    /**
     * 省市区Id
     */
    private Integer regionId;

    /**
     * 省市区名称
     */
    private String name;

    /**
     * 层级 0洲 1国 2-省 3-市 4-区 5-乡 6-村
     */
    private Integer level;

    /**
     * 是否可选
     */
    private Boolean enabled = true;

    /**
     * region子级
     */
    private List<RegionTreeResponse> children;
}
