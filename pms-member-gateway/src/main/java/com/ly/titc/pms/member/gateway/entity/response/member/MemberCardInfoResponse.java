package com.ly.titc.pms.member.gateway.entity.response.member;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：MemberCardInfoResponse
 * @Date：2024-11-18 18:00
 * @Filename：MemberCardInfoResponse
 */
@Data
public class MemberCardInfoResponse {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @PrimaryKey(column = "master_type", value = 1)
    private Integer masterType;

    /**
     * 归属值
     */
    @PrimaryKey(column = "master_code", value = 2)
    private String masterCode;

    /**
     * 归属名称
     */
    private String masterName;

    /**
     * 会员卡号
     */
    @PrimaryKey(column = "member_card_no", value = 3)
    private String memberCardNo;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡类型
     */
    private Integer cardType;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 卡名称
     */
    private String cardName;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 会员卡等级名称
     */
    private String cardLevelName;

    /**
     * 会员卡生效时间
     */
    private String effectBeginDate;

    /**
     * 会员卡失效时间
     */
    private String effectEndDate;

    /**
     * 等级有效期
     */
    private Integer validPeriod;

    /**
     * 是否长期有效 0 否 1 是
     */
    private Integer isLongTerm;

    /**
     * 最近一次等级变更时间
     */
    @JsonFormat(pattern="yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private LocalDateTime lastLevelChangeDate;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
