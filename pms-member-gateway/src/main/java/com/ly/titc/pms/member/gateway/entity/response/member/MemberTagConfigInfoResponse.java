package com.ly.titc.pms.member.gateway.entity.response.member;

import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberTagConfigInfoResponse
 * @Date：2024-11-15 9:56
 * @Filename：MemberTagConfigInfoResponse
 */
@Data
public class MemberTagConfigInfoResponse{


    /**
     * 自增id
     */
    private Long id;

    /**
     * 主类型 1:ELONG 2:集团 3:门店
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签分类 1 客房偏好、2 餐饮喜好、3 兴趣喜好、4 客户标签、5 其它标签
     */
    private Integer type;

    /**
     * 打标分类 1 自动打标 2 手动达标
     */
    private Integer markType;

    /**
     * 满足条件 ALL - 全部  ANY-满足任何一个条件
     */
    private String satisfyPerformType;

    /**
     * 标签描述
     */
    private String remark;

    /**
     * 统计周期 0 会员注册起 1 上次升降机起
     */
    private Integer cycleType;

    /**
     * 状态；0 无效 1 正常
     */
    private Integer state;

    /**
     * 排序值
     */
    private Integer sort;

    /**
     * 自动删除
     */
    private Integer autoDelete;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * 修改时间
     */
    private String gmtModified;

    /**
     * 统计规则
     */
    private List<MemberTagMarkRuleInfoResponse> markRuleList;

    /**
     * 会员数量
     */
    private Integer memberCount;
}
