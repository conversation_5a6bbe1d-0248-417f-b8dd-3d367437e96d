package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.cc.sdk.log.annotation.LogRecord;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.GetDetailBaseReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.QueryMemberPrivilegeConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberPrivilegeConfigResp;
import com.ly.titc.pms.member.com.annotation.OperationLog;
import com.ly.titc.pms.member.com.constant.ActionConstants;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.ModuleConstants;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.converter.MemberCardConverter;
import com.ly.titc.pms.member.gateway.entity.request.member.GetDetailBaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.ActionPrivilegeRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.DeletePrivilegeRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.privilege.MemberPrivilegeConfigInfoDetailRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.privilege.PageMemberPrivilegeRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.privilege.SaveMemberPrivilegeConfigInfoRequest;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberPrivilegeConfigDetailResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberPrivilegeConfigResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.ScopeValueResponse;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberPrivilegeDecorator;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * 权益管理
 *
 * @Author：rui
 * @name：MemberPrivilegeController
 * @Date：2024-11-14 17:38
 * @Filename：MemberPrivilegeController
 */
@Slf4j
@RestController
@RequestMapping("/member/privilege")
public class MemberPrivilegeController {

    @Resource
    private MemberCardConverter converter;

    @Resource
    private MemberPrivilegeDecorator privilegeDecorator;

    @Resource
    private HotelDecorator hotelDecorator;

    /**
     * 新增权益
     *
     * @param request request
     * @return id
     */
    @PostMapping("/addMemberPrivilege")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_PRIVILEGE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_PRIVILEGE_MANAGE_NAME,
            category = ActionConstants.ADD_CODE, categoryName = ActionConstants.ADD_NAME)
    @OperationLog(operateRecord = "#memberPrivilege_addMemberPrivilege(#request)")
    public Response<Long> addMemberPrivilege(@RequestBody @Valid SaveMemberPrivilegeConfigInfoRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        // 强校验，不可同时传入多个相同值
        List<String> valueList = request.getScopeValues().stream().map(MemberPrivilegeConfigInfoDetailRequest::getValue).collect(Collectors.toList());
        if (hasDuplicate(valueList)) {
            throw new ServiceException("不能同时传入多个相同值", "500");
        }
        return Response.success(privilegeDecorator.saveMemberPrivilege(converter.convertSaveMemberPrivilegeConfigInfoReq(request, masterTuple.getFirst())));
    }

    private boolean hasDuplicate(List<String> scopeValues) {
        Set<String> uniqueValues = new HashSet<>();
        for (String value : scopeValues) {
            if (!uniqueValues.add(value)) {
                return true; // 发现重复值
            }
        }
        return false; // 没有重复值
    }

    /**
     * 更新权益
     *
     * @param request request
     * @return id
     */
    @PostMapping("/updateMemberPrivilege")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_PRIVILEGE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_PRIVILEGE_MANAGE_NAME,
            category = ActionConstants.SAVE_CODE, categoryName = ActionConstants.SAVE_NAME)
    @OperationLog(operateRecord = "#memberPrivilege_updateMemberPrivilege(#request)")
    public Response<Long> updateMemberPrivilege(@RequestBody @Valid SaveMemberPrivilegeConfigInfoRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(privilegeDecorator.saveMemberPrivilege(converter.convertSaveMemberPrivilegeConfigInfoReq(request, masterTuple.getFirst())));
    }

    /**
     * 删除权益
     *
     * @param request request
     * @return
     */
    @PostMapping("/deleteMemberPrivilege")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_PRIVILEGE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_PRIVILEGE_MANAGE_NAME,
            category = ActionConstants.DELETE_CODE, categoryName = ActionConstants.DELETE_NAME)
    @OperationLog(operateRecord = "#memberPrivilege_deleteMemberPrivilege(#request)")
    public Response deleteMemberPrivilege(@RequestBody @Valid DeletePrivilegeRequest request) {
        privilegeDecorator.deleteMemberPrivilege(Long.parseLong(request.getId()), request.getTrackingId(), request.getOperator());
        return Response.success();
    }

    /**
     * 分页查询
     */
    @PostMapping("/pageMemberPrivilege")
    public Response<Pageable<MemberPrivilegeConfigResponse>> pageMemberPrivilege(@RequestBody @Valid PageMemberPrivilegeRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        QueryMemberPrivilegeConfigReq req = converter.convertQueryMemberPrivilegeConfigReq(request, masterTuple.getFirst());
        Pageable<MemberPrivilegeConfigResp> pageable = privilegeDecorator.page(req);
        List<MemberPrivilegeConfigResp> list = pageable.getDatas();
        if (CollectionUtils.isEmpty(list)) {
            return Response.success(pageable);
        }
        return Response.success(PageableUtil.convert(pageable, converter.convertMemberPrivilegeConfigRespList(list)));
    }

    /**
     * 启用
     *
     * @param request request
     */
    @PostMapping("/enableMemberPrivilege")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_PRIVILEGE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_PRIVILEGE_MANAGE_NAME,
            category = ActionConstants.START_CODE, categoryName = ActionConstants.START_NAME)
    @OperationLog(operateRecord = "#memberPrivilege_enableMemberPrivilege(#request)")
    public Response enableMemberPrivilege(@RequestBody @Valid ActionPrivilegeRequest request) {
        privilegeDecorator.action(Long.parseLong(request.getId()), StatusEnum.VALID.getStatus(), request.getTrackingId(), request.getOperator());
        return Response.success();
    }

    /**
     * 禁用
     *
     * @param request request
     */
    @PostMapping("/disableMemberPrivilege")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_PRIVILEGE_MANAGE_CODE, bizName = ModuleConstants.MEMBER_PRIVILEGE_MANAGE_NAME,
            category = ActionConstants.STOP_CODE, categoryName = ActionConstants.STOP_NAME)
    @OperationLog(operateRecord = "#memberPrivilege_disableMemberPrivilege(#request)")
    public Response disableMemberPrivilege(@RequestBody @Valid ActionPrivilegeRequest request) {
        privilegeDecorator.action(Long.parseLong(request.getId()), StatusEnum.INVALID.getStatus(), request.getTrackingId(), request.getOperator());
        return Response.success();
    }

    /**
     * 获取详情
     */
    @PostMapping("/detail")
    public Response<MemberPrivilegeConfigResponse> detail(@RequestBody @Valid GetDetailBaseRequest request) {
        MemberPrivilegeConfigResponse memberPrivilegeConfigResponse = converter.convertMemberPrivilegeConfigResp(privilegeDecorator.getMemberPrivilegeConfigDetail(Long.parseLong(request.getId()), request.getTrackingId()));
        List<HotelBaseInfoResp> hotelBaseInfoRespList = hotelDecorator.listHotelBaseInfos(request.getBlocCode(), null, request.getTrackingId());
        Map<String, HotelBaseInfoResp> hotelMap = hotelBaseInfoRespList.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, item -> item));
        for (MemberPrivilegeConfigDetailResponse scopeValue : memberPrivilegeConfigResponse.getScopeValues()) {
            if (CollectionUtils.isNotEmpty(scopeValue.getScopeValues())) {
                scopeValue.setScopeValueResponses(scopeValue.getScopeValues().stream().map(item -> {
                    ScopeValueResponse ret = new ScopeValueResponse();
                    ret.setValue(item);
                    ret.setName(hotelMap.containsKey(item) ? hotelMap.get(item).getHotelName() : "");
                    ret.setBrandCode(hotelMap.containsKey(item) ? hotelMap.get(item).getBrandCode() : "");
                    return ret;
                }).collect(Collectors.toList()));
            }
        }
        return Response.success(memberPrivilegeConfigResponse);
    }

    /**
     * 校验权益是否被使用
     */
    @PostMapping("/checkPrivilegeUsed")
    public Response<Boolean> checkPrivilegeUsed(@RequestBody @Valid GetDetailBaseRequest request) {
        return Response.success(privilegeDecorator.checkPrivilegeUsed(Long.parseLong(request.getId()), request.getTrackingId()));
    }


}
