package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.gateway.converter.MemberUsageConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.usage.*;
import com.ly.titc.pms.member.gateway.entity.response.usage.MemberPointUsageRuleResponse;
import com.ly.titc.pms.member.gateway.entity.response.usage.MemberUsageRuleSaveResultResponse;
import com.ly.titc.pms.member.mediator.entity.dto.usage.MemberPointUsageRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.MemberUsageRuleSaveResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.QueryMemberMasterUsageDto;
import com.ly.titc.pms.member.mediator.service.MemberPointUsageMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * 积分规则设置
 * <AUTHOR>
 * @classname
 * @descrition 积分设置
 * @since 2024-11-15 11:54
 */
@Slf4j
@RestController
@RequestMapping("/member/pointUsage")
public class MemberPointUsageController {
    @Resource
    private MemberPointUsageMedService pointUsageMedService;
    @Resource
    private MemberUsageConverter usageConverter;

    /**
     * 根据搜索条件分页查询配置
     * CRM调用
     */
    @RequestMapping("/page")
    public Response<Pageable<MemberPointUsageRuleResponse>> page(@Valid @RequestBody QueryMemberMasterUsageRequest request){
        QueryMemberMasterUsageDto  dto = usageConverter.convert(request);
        Pageable<MemberPointUsageRuleDto> page=  pointUsageMedService.page(dto);
        return Response.success(PageableUtil.convert(page, d-> usageConverter.convert(d)));
    }

    /**
     * 新增积分设置
     */
    @RequestMapping("/add")
    public Response<List<MemberUsageRuleSaveResultResponse>> add(@Valid @RequestBody MemberPointUsageRuleConfigSaveRequest request){
        List<MemberUsageRuleSaveResultDto> resultDtos =  pointUsageMedService.save(usageConverter.convert(request));
        return Response.success(usageConverter.convert(resultDtos));

    }

    /**
     * 编辑积分设置
     */
    @RequestMapping("/save")
    public Response<List<MemberUsageRuleSaveResultResponse>> save(@Valid @RequestBody MemberPointUsageRuleConfigSaveRequest request){
        List<MemberUsageRuleSaveResultDto> resultDtos =  pointUsageMedService.save(usageConverter.convert(request));
        return Response.success(usageConverter.convert(resultDtos));
    }

    /**
     * 更新状态
     */
    @RequestMapping("/updateState")
    public  Response<Boolean> updateState(@Valid @RequestBody UpdateMemberUsageStateRequest request){
       Boolean result= pointUsageMedService.updateState(usageConverter.convert(request));
       return Response.success(result);

    }

    /**
     * 删除
     */
    @RequestMapping("/delete")
    public Response<Boolean> delete(@Valid @RequestBody DeleteMemberUsageRequest request){
        Boolean result= pointUsageMedService.delete(usageConverter.convert(request));
        return Response.success(result);
    }


    /**
     * 未设置提醒
     */
    @RequestMapping("/remind")
    public Response<List<MemberUsageRuleSaveResultResponse>> remind(BaseRequest request){
        List<MemberUsageRuleSaveResultDto>  resultDtos= pointUsageMedService.remind(usageConverter.convert(request));
        return Response.success(usageConverter.convert(resultDtos));
    }


}
