package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-6
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class DeleteCouponTemplateRequest extends BaseRequest {

    /**
     * 模版code
     */
    @NotBlank(message = "模版code不能为空")
    private String templateCode;

    /**
     * 版本号
     */
    @NotNull(message = "模板版本不能为空")
    private Integer version;
}
