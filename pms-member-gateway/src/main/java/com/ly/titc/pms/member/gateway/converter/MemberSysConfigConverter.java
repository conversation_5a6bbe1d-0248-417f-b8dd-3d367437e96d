package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.common.enums.MasterTypeEnum;
import com.ly.titc.pms.ecrm.dubbo.entity.BaseMasterReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberPointSysConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MemberPointSysConfigResp;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.sysConfig.MemberPointSysConfigRequest;
import com.ly.titc.pms.member.gateway.entity.response.config.MemberPointSysConfigResponse;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-24 22:52
 */
@Mapper(componentModel = "spring")
public interface MemberSysConfigConverter {

    MemberPointSysConfigReq convert(MemberPointSysConfigRequest request);

    MemberPointSysConfigResponse convert(MemberPointSysConfigResp resp);

    default BaseMasterReq convert(BaseRequest request){
        BaseMasterReq masterReq = new BaseMasterReq();
        masterReq.setMasterType(MasterTypeEnum.BLOC.getType());
        masterReq.setMasterCode(request.getBlocCode());
        masterReq.setTrackingId(request.getTrackingId());
        return masterReq;
    }
}
