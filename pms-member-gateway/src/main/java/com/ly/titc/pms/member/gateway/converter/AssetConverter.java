package com.ly.titc.pms.member.gateway.converter;

import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.util.LocalDateTimeUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.account.dubbo.enums.pay.TradeOpetatorTypeEnum;
import com.ly.titc.pms.ecrm.dubbo.entity.BaseMasterReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.MemberStoreConsumeReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.MemberStoreFreezeReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.UnfreezeConsumeRecordNoReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeCalResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeRecordResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTotalAmountResp;
import com.ly.titc.pms.member.asset.dubbo.enums.AssetBusinessTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.AssetConsumeTypeEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.PointActionItemEnum;
import com.ly.titc.pms.member.asset.dubbo.enums.PointActionTypeEnum;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.com.utils.GenerateUtils;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.entity.request.asset.point.MemberPointAdjustRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreConsumeCalRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreConsumeRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreFreezeRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.store.MemberStoreUnFreezeRequest;
import com.ly.titc.pms.member.gateway.entity.response.asset.*;
import com.ly.titc.pms.member.mediator.entity.dto.asset.ConsumeMemberPointDto;
import com.ly.titc.pms.member.mediator.entity.dto.asset.ReceiveMemberPointDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberOrderDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberOrderDto;
import org.apache.commons.lang3.StringUtils;
import org.mapstruct.Mapper;

import java.awt.*;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：AssetConverter
 * @Date：2024-12-9 16:17
 * @Filename：AssetConverter
 */
@Mapper(componentModel = "spring")
public interface AssetConverter {

    default List<MemberStoreConsumeRecordResponse> convertMemberStoreConsumeRecord(List<MemberStoreConsumeRecordResp> list, List<HotelBaseInfoResp> hotelBaseInfos) {

        Map<String, String> hotelMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, HotelBaseInfoResp::getHotelName, (v1, v2) -> v1));
        return list.stream().map(item -> {
            MemberStoreConsumeRecordResponse response = convertMemberStoreConsumeRecord(item);
            if (response.getMasterType().equals(MasterTypeEnum.BLOC.getType())) {
                response.setSource("集团");
            } else {
                response.setSource(hotelMap.getOrDefault(item.getHotelCode(), ""));
            }
            return response;
        }).collect(Collectors.toList());
    }

    MemberStoreConsumeRecordResponse convertMemberStoreConsumeRecord(MemberStoreConsumeRecordResp resp);

    default List<MemberOrderRechargeResponse> convertMemberOrderRecharge(List<MemberOrderDto> list, List<HotelBaseInfoResp> hotelBaseInfos) {
        Map<String, String> hotelMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, HotelBaseInfoResp::getHotelName, (v1, v2) -> v1));
        return list.stream().map(item -> {
            MemberOrderRechargeResponse response = convertMemberOrderRecharge(item);
            if (response.getMasterType().equals(MasterTypeEnum.BLOC.getType())) {
                response.setSource("集团");
            } else {
                response.setSource(hotelMap.getOrDefault(item.getMasterCode(), ""));
            }
            return response;
        }).collect(Collectors.toList());
    }

    MemberOrderRechargeDetailResponse convert(MemberOrderDetailDto dto);

    MemberOrderRechargeResponse convertMemberOrderRecharge(MemberOrderDto info);

    default List<MemberPointConsumeRecordResponse> convertMemberPointConsumeRecord(List<MemberPointsFlowInfoResp> list, List<HotelBaseInfoResp> hotelBaseInfos) {
        Map<String, String> hotelMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, item -> item.getHotelName(), (v1, v2) -> v1));
        return list.stream().map(item -> {
            MemberPointConsumeRecordResponse response = convertMemberPointConsumeRecord(item);
            if (response.getMasterType().equals(MasterTypeEnum.BLOC.getType())) {
                response.setSource("集团");
            } else {
                response.setSource(hotelMap.getOrDefault(item.getHotelCode(), ""));
            }
            response.setCreateTime(timestampToString(item.getCreateTime()));
            response.setUpdateTime(timestampToString(item.getUpdateTime()));
            if (StringUtils.isNotEmpty(item.getTradeType())) {
                TradeOpetatorTypeEnum tradeOpetatorTypeEnum = TradeOpetatorTypeEnum.getByType(item.getTradeType());
                if (Objects.nonNull(tradeOpetatorTypeEnum)) {
                    response.setReason(tradeOpetatorTypeEnum.getDesc());
                }
            }
            response.setActionItemName(Arrays.stream(PointActionItemEnum.values()).filter(it -> Objects.equals(it.getActionItem(), item.getActionItem())).findFirst().map(it -> it.getActionItemDesc()).orElse(""));
            return response;
        }).collect(Collectors.toList());
    }

    MemberPointConsumeRecordResponse convertMemberPointConsumeRecord(MemberPointsFlowInfoResp resp);

    MemberTotalPointResponse convertMemberTotalPoint(MemberTotalPointResp resp);

    MemberTotalAmountResponse convertMemberTotalAmount(MemberTotalAmountResp resp);

    default ReceiveMemberPointDto convert(MemberPointAdjustRequest request) {
        ReceiveMemberPointDto dto = new ReceiveMemberPointDto();
        dto.setTrackingId(request.getTrackingId());
        dto.setMasterType(MasterTypeEnum.BLOC.getType());
        dto.setMasterCode(request.getBlocCode());
        dto.setBlocCode(request.getBlocCode());
        dto.setMemberNo(request.getMemberNo());
        dto.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        dto.setBusinessType(AssetBusinessTypeEnum.MEMBER.getType());
        dto.setBusinessNo(GenerateUtils.generate(SystemConstant.MEMBER_NO_PREFIX));
        dto.setActionType(PointActionTypeEnum.ADJUST.getType());
        dto.setActionItem(request.getActionItem());
        dto.setActionItemDesc(request.getActionItemDesc());
        dto.setScore(request.getScore());
        dto.setRemark(request.getRemark());
        dto.setOperator(request.getOperator());
        return dto;
    }

    default BaseMasterReq convertReq(MemberPointAdjustRequest request) {
        BaseMasterReq req = new BaseMasterReq();
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(request.getBlocCode());
        req.setTrackingId(request.getTrackingId());
        return req;
    }

    default ConsumeMemberPointDto convertConsumeReq(MemberPointAdjustRequest request) {
        ConsumeMemberPointDto pointDto = new ConsumeMemberPointDto();
        pointDto.setMemberNo(request.getMemberNo());
        pointDto.setTrackingId(request.getTrackingId());
        pointDto.setMasterType(MasterTypeEnum.BLOC.getType());
        pointDto.setMasterCode(request.getBlocCode());
        pointDto.setBlocCode(request.getBlocCode());
        pointDto.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        pointDto.setBusinessType(AssetBusinessTypeEnum.MEMBER.getType());
        pointDto.setBusinessNo(GenerateUtils.generate(SystemConstant.MEMBER_NO_PREFIX));
        pointDto.setConsumeType(AssetConsumeTypeEnum.ADJUST.getType());
        pointDto.setConsumeDesc(request.getActionItemDesc() + request.getRemark());
        pointDto.setActionItem(request.getActionItem());
        pointDto.setActionItemDesc(request.getActionItemDesc());
        pointDto.setScore(Math.abs(request.getScore()));
        pointDto.setScene("");
        pointDto.setRemark(request.getRemark());
        pointDto.setOperator(request.getOperator());
        return pointDto;
    }

    default MemberStoreConsumeReq convert(MemberStoreConsumeRequest request) {
        MemberStoreConsumeReq req = convertBase(request);
        convert(req, request.getGoodsDes());
        return req;
    }

    MemberStoreConsumeReq convertBase(MemberStoreConsumeRequest request);

    default MemberStoreConsumeReq convert(MemberStoreConsumeCalRequest request) {
        MemberStoreConsumeReq req = convertBase(request);
        convert(req, "消费预计算");
        return req;
    }

    default MemberStoreFreezeReq convert(MemberStoreFreezeRequest request) {
        MemberStoreFreezeReq req = convertBase(request);
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(request.getBlocCode());
        req.setBlocCode(request.getBlocCode());
        req.setMemberNo(request.getMemberNo());
        req.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        req.setConsumeType(AssetConsumeTypeEnum.FREEZE.getType());
        req.setBusinessType(AssetBusinessTypeEnum.MEMBER.getType());
        req.setBusinessNo(GenerateUtils.generate(SystemConstant.MEMBER_NO_PREFIX));
        return req;
    }

    MemberStoreFreezeReq convertBase(MemberStoreFreezeRequest request);

    MemberStoreConsumeReq convertBase(MemberStoreConsumeCalRequest request);

    UnfreezeConsumeRecordNoReq convert(MemberStoreUnFreezeRequest request);

    default MemberStoreConsumeReq convert(MemberStoreConsumeReq req, String goodDes) {
        req.setGoodsDes(goodDes);
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(req.getBlocCode());
        req.setConsumeType(AssetConsumeTypeEnum.PAY.getType());
        req.setBusinessType(AssetBusinessTypeEnum.MEMBER.getType());
        req.setBusinessNo(GenerateUtils.generate(SystemConstant.MEMBER_NO_PREFIX));
        req.setPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        return req;
    }

    MemberStoreConsumeCalResponse convert(MemberStoreConsumeCalResp calResp);

    default String timestampToString(Timestamp timestamp) {
        if (Objects.isNull(timestamp)) {
            return "";
        }
        // 将Timestamp转换为Instant
        LocalDateTime localDateTime = timestamp.toInstant()
                .atZone(ZoneId.systemDefault())
                .toLocalDateTime();
        return LocalDateTimeUtil.formatByNormalDateTime(localDateTime);
    }
}
