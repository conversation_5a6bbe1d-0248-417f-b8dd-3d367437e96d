package com.ly.titc.pms.member.gateway.thread.local;


import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;

/**
 * @Description: 用户信息
 * @Author: lixu
 * @Date: 2022/11/27
 */
public class UserInfoThreadHolder {

    private static ThreadLocal<UserInfoDto> contextHolder = new ThreadLocal<>();
    private static ThreadLocal<String> trackingId = new ThreadLocal<>();

    private UserInfoThreadHolder(){

    }

    /**
     * 设置用户信息
     * @param dto
     */
    public static void setUser(UserInfoDto dto) {
        contextHolder.set(dto);
    }

    /**
     * 清空变量
     */
    public static void clear() {
        contextHolder.set(null);
        trackingId.set(null);
    }

    /**
     * 获取用户信息
     * @return
     */
    
    public static UserInfoDto getUser() {
        return contextHolder.get();
    }

    public static String getTrackingId() {
        return trackingId.get();
    }

    public static void setTrackingId(String id) {
        trackingId.set(id);
    }
}
