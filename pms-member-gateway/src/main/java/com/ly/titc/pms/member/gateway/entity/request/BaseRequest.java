package com.ly.titc.pms.member.gateway.entity.request;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：rui
 * @name：BaseRequest
 * @Date：2024-11-11 21:16
 * @Filename：BaseRequest
 */
@Data
@Accessors(chain = true)
public class BaseRequest {

    /**
     * 集团code 不需要传值 后端根据SSO获取
     */
    private String blocCode;

    /**
     * 操作人,不需要传值 后端根据SSO获取
     */
    private String operator;

    /**
     * 追踪ID，后端自动生成
     */
    private String trackingId;
}
