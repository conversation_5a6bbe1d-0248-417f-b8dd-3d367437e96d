package com.ly.titc.pms.member.gateway.aspect;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;


/**
 * @Author：rui
 * @name：OauthAspect
 * @Date：2024-11-11 21:02
 * @Filename：OauthAspect
 */
@Component
@Aspect
@Slf4j
@Order(2)
public class OauthAspect {

    @Pointcut("within(@org.springframework.web.bind.annotation.RestController com.ly.titc.pms.member.gateway.controller..*)")
    public void pointcut() {

    }


    @Before("pointcut() && args(request)")
    public void setOauthArgs(BaseRequest request) throws Throwable {
        if (request == null) {
            request = new BaseRequest();
        }
        request.setBlocCode(UserThreadHolder.getUser().getBlocCode())
                .setOperator(UserThreadHolder.getUser().getUserName())
                .setTrackingId(UserThreadHolder.getTrackingId());
    }


}
