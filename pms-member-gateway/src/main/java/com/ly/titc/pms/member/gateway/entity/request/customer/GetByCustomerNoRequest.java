package com.ly.titc.pms.member.gateway.entity.request.customer;

import com.ly.titc.pms.member.dubbo.entity.request.BaseReq;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 根据客户编号查询客户详情
 *
 * <AUTHOR>
 * @date 2024/12/11 19:27
 */
@Data
public class GetByCustomerNoRequest extends BaseRequest {

    /**
     * 酒店编码
     */
    @NotBlank(message = "酒店编码不能为空")
    private String hotelCode;

    /**
     * 客户编号
     */
    @NotBlank(message = "客户编号不能为空")
    private String customerNo;

}
