package com.ly.titc.pms.member.gateway.entity.request.member;

import com.ly.titc.common.annotation.LegalPhoneNumber;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * 获取会员号
 *
 * <AUTHOR>
 * @date 2024/12/13 11:21
 */
@Data
public class GetMemberNoRequest extends BaseRequest {

    /**
     * 手机号
     */
    @NotBlank(message = "手机号不能为空")
    @LegalPhoneNumber(message = "手机号不合法")
    private String mobile;

}
