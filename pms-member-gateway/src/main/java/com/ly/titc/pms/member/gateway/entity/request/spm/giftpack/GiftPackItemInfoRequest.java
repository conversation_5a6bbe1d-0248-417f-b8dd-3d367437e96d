package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import lombok.Data;

import java.io.Serializable;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-22
 */
@Data
public class GiftPackItemInfoRequest implements Serializable {

    /**
     * 礼包明细类型 1-优惠券 2-会员卡 3-积分 4-会员权益 5-联名权益
     */
    private Integer itemType;

    /**
     * 礼包明细业务编号
     * 优惠券编号，会员等级
     */
    private String bizNo;

    /**
     * 礼包明细业务名称
     */
    private String bizName;

    /**
     * 明细数量
     */
    private Integer num;
}
