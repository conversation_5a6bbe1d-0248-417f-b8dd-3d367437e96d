package com.ly.titc.pms.member.gateway.entity.response.asset;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * @Author：rui
 * @name：MemberTotalPointResponse
 * @Date：2024-12-11 20:59
 * @Filename：MemberTotalPointResponse
 */
@Accessors(chain = true)
@Data
public class MemberTotalPointResponse {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 总积分
     */
    private Integer totalScore;

    /**
     * 总积分余额
     */
    private Integer totalScoreBalance;

    /**
     * 总已过期积分
     */
    private Integer totalExpireScore;

    /**
     * 总已使用积分
     */
    private Integer totalUsedScore;
}
