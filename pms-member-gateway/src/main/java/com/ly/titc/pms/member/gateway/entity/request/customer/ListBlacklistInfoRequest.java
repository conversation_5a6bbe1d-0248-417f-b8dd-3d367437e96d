package com.ly.titc.pms.member.gateway.entity.request.customer;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * 查询黑名单
 *
 * <AUTHOR>
 * @date 2024/12/13 15:12
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ListBlacklistInfoRequest extends BaseRequest {

    /**
     * 酒店编码
     */
    @NotBlank(message = "酒店编码不能为空")
    private String hotelCode;

    /**
     * 客户编号
     */
    @NotBlank(message = "客户编号不能为空")
    private String customerNo;

    /**
     * 状态 0 已取消 1 生效
     */
    private Integer state;
}
