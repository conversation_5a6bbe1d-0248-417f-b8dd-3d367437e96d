package com.ly.titc.pms.member.gateway.entity.response.customer;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 客户黑名单信息
 *
 * <AUTHOR>
 * @date 2024/12/13 15:04
 */
@Data
public class CustomerBlacklistResponse {

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 酒店编号
     */
    private String hotelCode;

    /**
     * 客户编号
     */
    private String customerNo;

    /**
     * 场景适用范围 0 部分 1 全部
     */
    private Integer sceneScope;

    /**
     * 适用场景列表
     */
    private List<String> scenes;

    /**
     * 原因
     */
    private String reason;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;

}
