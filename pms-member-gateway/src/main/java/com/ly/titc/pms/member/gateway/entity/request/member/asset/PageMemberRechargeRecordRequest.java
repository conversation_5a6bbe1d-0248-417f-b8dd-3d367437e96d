package com.ly.titc.pms.member.gateway.entity.request.member.asset;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;

/**
 * @Author：rui
 * @name：PageMemberStoredRequest
 * @Date：2024-12-5 11:01
 * @Filename：PageMemberStoredRequest
 */
@Data
public class PageMemberRechargeRecordRequest extends PageBaseRequest {

    /**
     * 会员编号
     */
    private String memberNo;


    /**
     * 充值门店类型 1 集团  2 门店
     */
    private Integer masterType;

    /**
     * 具体的值 集团编码 、 门店编码
     */
    private String masterCode;

    /**
     * 状态
     */
    private Integer state;

    /**
     * 充值开始时间
     * eg: 2024-12-10 12:12:33
     */
    private String beginTime;

    /**
     * 充值结束时间
     * eg: 2024-12-10 12:12:33
     */
    private String endTime;

}
