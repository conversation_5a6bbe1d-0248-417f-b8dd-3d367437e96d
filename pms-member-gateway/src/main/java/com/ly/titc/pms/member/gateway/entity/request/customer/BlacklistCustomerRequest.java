package com.ly.titc.pms.member.gateway.entity.request.customer;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 拉黑
 *
 * <AUTHOR>
 * @date 2024/12/13 15:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class BlacklistCustomerRequest extends BaseRequest {

    /**
     * 客户编号
     */
    @NotBlank(message = "客户编号不能为空")
    private String customerNo;

    /**
     * 场景适用范围 0 部分 1 全部
     */
    @NotNull(message = "场景适用范围不能为空")
    private Integer sceneScope;

    /**
     * 适用场景列表
     */
    private List<String> scenes;

    /**
     * 原因
     */
    @NotBlank(message = "原因不能为空")
    private String reason;

}
