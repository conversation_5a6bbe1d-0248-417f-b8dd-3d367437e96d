package com.ly.titc.pms.member.gateway.aspect;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.common.base.Stopwatch;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.BaseRespCodeEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.TraceNoUtil;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;
import javax.servlet.ServletRequest;
import javax.servlet.ServletResponse;
import javax.validation.ConstraintViolation;
import javax.validation.ConstraintViolationException;

import com.ly.titc.pms.member.gateway.annoation.Sensitive;
import com.ly.titc.pms.member.gateway.entity.enums.SensitiveFieldTypeEnum;
import com.ly.titc.pms.member.gateway.handler.sensitive.SensitiveProtectHandler;
import com.ly.titc.pms.member.gateway.manager.SensitiveProtectManager;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.Signature;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Pointcut;
import org.aspectj.lang.reflect.CodeSignature;
import org.springframework.core.annotation.Order;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.web.multipart.MultipartFile;


/**
 * @ClassName: RestControllerAspect
 * @Description:
 * <AUTHOR>
 * @Date 2019/5/27 15:37
 * @Version 1.0
 */
@Component
@Aspect
@Slf4j
@Order(1)
public class RestControllerAspect {

	@Pointcut("within(@org.springframework.web.bind.annotation.RestController com.ly.titc.pms.member.gateway.controller..*)")
	public void requestLog() {

	}

	@Around("requestLog()")
	public Object around(ProceedingJoinPoint pjp) throws Throwable {
		TraceNoUtil.initTraceNo();
		Map<String, Object> args = getNameAndValue(pjp);
		Signature signature = pjp.getSignature();
		String methodName = signature.getName();
		String className = pjp.getTarget().getClass().getName();
		log.info("[{}] [{}] request start, args:{}", className, methodName, JSON.toJSONString(args, SerializerFeature.WriteMapNullValue));
		Stopwatch stopwatch = Stopwatch.createStarted();
		Object obj;
		try {
			obj = pjp.proceed();
			processSensitiveFields(obj);
			log.info("[{}] [{}]，request end, cost：{}ms", className, methodName, stopwatch.elapsed(TimeUnit.MILLISECONDS));
		} finally {
			TraceNoUtil.clearTraceNo();
		}
		return obj;
	}

	@AfterThrowing(value = "requestLog()", throwing = "e")
	public void afterThrowing(Throwable e) {
		log.error(e.getMessage(), e);
	}

	/**
	 * assemble map
	 *
	 * @param joinPoint
	 * @return
	 */
	private Map<String, Object> getNameAndValue(ProceedingJoinPoint joinPoint) {
		Map<String, Object> param = new HashMap<>();

		Object[] paramValues = joinPoint.getArgs();
		Class[] classes = ((CodeSignature) joinPoint.getSignature()).getParameterTypes();
		String[] paramNames = ((CodeSignature) joinPoint.getSignature()).getParameterNames();
		for (int i = 0; i < paramNames.length; i++) {

			if (paramValues[i] instanceof ServletRequest
					|| paramValues[i] instanceof ServletResponse
					|| paramValues[i] instanceof MultipartFile) {
				continue;
			}
			String simpleName = classes[i].getSimpleName();
			if (simpleName.equalsIgnoreCase("MultipartFile[]") ||
					simpleName.equalsIgnoreCase("MultipartFile")) {
				continue;
			}
			param.put(paramNames[i], paramValues[i]);
		}
		return param;
	}

	/**
	 * 敏感字段保护
	 *
	 * @param obj
	 */
	private void processSensitiveFields(Object obj) {
		Field[] fields = obj.getClass().getDeclaredFields();

		for (Field field : fields) {
			if (field.isAnnotationPresent(Sensitive.class)) {
				Sensitive annotation = field.getAnnotation(Sensitive.class);
				SensitiveFieldTypeEnum sensitiveFieldTypeEnum = annotation.fieldType();
				if (sensitiveFieldTypeEnum == null) {
					continue;
				}
				try {
					SensitiveProtectHandler instance = SensitiveProtectManager.getInstance(sensitiveFieldTypeEnum.name());
					if (instance == null) {
						continue;
					}
					field.setAccessible(true);
					Object value = field.get(obj);
					if (value instanceof String) {
						String processedValue = instance.handle((String) value);
						field.set(obj, processedValue);
					}
				} catch (Exception e) {
					log.error("处理敏感字段失败，error:", e);
				}
			}
		}
	}

}
