package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.spm.ApplicableHotelInfoRequest;
import lombok.Data;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-10
 */
@Data
public class CouponGrantRuleRequest {

    /**
     * 早餐券截止时间
     */
    private String deadline;

    /**
     * 是否仅集团发放
     */
    private Boolean isOnlyBloc;

    /**
     * 是否是适用门店发放
     */
    private Boolean isApplicableHotel;

    /**
     * 自定义酒店范围
     */
    private ApplicableHotelInfoRequest customizedHotelInfos;


}
