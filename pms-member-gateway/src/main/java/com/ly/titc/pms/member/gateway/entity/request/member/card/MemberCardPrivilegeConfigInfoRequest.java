package com.ly.titc.pms.member.gateway.entity.request.member.card;

import lombok.Data;

/**
 * @Author：rui
 * @name：MemberCardPrivilegeConfigInfoRequest
 * @Date：2024-11-13 16:03
 * @Filename：MemberCardPrivilegeConfigInfoRequest
 */
@Data
public class MemberCardPrivilegeConfigInfoRequest {
    /**
     * id，指的是绑定关系本身的id，新增的等级关联权益为空
     */
    private Long id;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 卡id
     */
    private Long cardId;

    /**
     * 卡等级
     */
    private Integer cardLevel;

    /**
     * 权益id
     */
    private Long privilegeId;

    /**
     * 权益类型
     */
    private Integer type;

    /**
     * 权益名称
     */
    private String name;

}
