package com.ly.titc.pms.member.gateway.entity.response.asset;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * 查询会员资产
 *
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-13 14:28
 */
@Data
@Accessors(chain = true)
public class MemberAssetResponse {

    /**
     * 积分
     */
   private MemberTotalPointResponse point;

    /**
     * 储值
     */
   private MemberTotalAmountResponse store;

    /**
     * 优惠券数量
     */
   private Integer couponNum;

}
