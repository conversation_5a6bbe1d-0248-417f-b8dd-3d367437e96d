package com.ly.titc.pms.member.gateway.entity.request.member.privilege;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author：rui
 * @name：SaveMemberPrivilegeConfigInfoRequest
 * @Date：2024-11-12 17:34
 * @Filename：SaveMemberPrivilegeConfigInfoRequest
 */
@Data
public class SaveMemberPrivilegeConfigInfoRequest extends BaseRequest {

    /**
     * id，编辑时必传
     */
    private String id;

    /**
     * 权益分类 1 价格权益 2 积分权益 3 线下权益 4 生态权益
     */
    @NotNull(message = "不能为空")
    private Integer type;

    /**
     * 权益名称
     */
    private String name;

    /**
     * 权益图标
     */
    private String pic;

    /**
     * 权益说明
     */
    private String instruction;

    /**
     * 权益描述
     */
    private String description;

    /**
     * 权益排序
     */
    private Integer sort;

    /**
     // 权益类型 1.仅作展示 2 价格折扣 3 预定保留 4 延迟退房
     */
    private Integer classification;

    /**
     * 适用类型 值集合
     */
    private List<MemberPrivilegeConfigInfoDetailRequest> scopeValues;
}
