package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-11-28 16:19
 */
@Data
@Accessors(chain = true)
public class GrantGiftPackRequest {

    /**
     * 礼包所属主体类型
     */
    @NotNull(message = "礼包所属主体类型不能为空")
    private Integer masterType;

    @NotBlank(message = "礼包所属主体code不能为空")
    private String masterCode;
    /**
     * 礼包编号
     */
    @NotBlank(message = "礼包编号不能为空")
    private String giftPackNo;
    /**
     * 礼包名称
     */
    @NotBlank(message = "礼包名称不能为空")
    private String giftPackName;

    @NotNull(message = "礼包版本不能为空")
    private Integer version;

    /**
     * 发放类型
     * 1-单个发放 2-批量发放
     */
    @NotNull(message = "发放类型不能为空")
    private Integer grantType;

    /**
     * 销售类型
     * 1-免费 2-付费
     */
    @NotNull(message = "销售类型不能为空")
    private Integer saleType;
    /**
     * 发放数量
     */
    @NotNull(message = "发放数量不能为空")
    private Integer grantNum;

    /**
     * 领取人类型
     * customer-客户 member-会员 room_order-房单
     */
    @NotBlank(message = "领取人类型不能为空")
    private String receiverType;
    /**
     * 会员编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String receiverCode;

    /**
     * 发放事件
     */
    private Integer grantEvent;

    /**
     * 付款方式
     */
    private String postingItemCode;

    /**
     * 入账门店
     */
    private String postingHotelCode;
    /**
     * 挂账房单
     */
    private String accountNo;

}
