package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberProfileTagRequest
 * @Date：2024-12-2 14:59
 * @Filename：MemberProfileTagRequest
 */
@Data
public class MemberProfileTagRequest {

    /**
     * 会员号
     */
    @NotBlank(message = "会员号不可为空")
    private String memberNo;

    /**
     * 标签分类
     */
    @NotNull(message = "标签分类不可为空")
    private Integer tagType;

    /**
     * 打标分类: 1:手动标记 2:系统标记
     */
    @NotNull(message = "打标分类不可为空")
    private Integer markType;

    /**
     * 标签ID
     */
    @NotBlank(message = "标签ID不可为空")
    private String tagId;

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不可为空")
    private String tagName;

    /**
     * 排序
     */
    @NotNull(message = "排序不可为空")
    private Integer sort;
}
