package com.ly.titc.pms.member.gateway.entity.response.blacklist;

import lombok.Data;

/**
 * @Author：rui
 * @name：BlacklistedDataMappingResponse
 * @Date：2024-12-10 20:16
 * @Filename：BlacklistedDataMappingResponse
 */
@Data
public class BlacklistedDataMappingResponse {

    /**
     * id
     */
    private Long id;

    /**
     * 黑名单编号
     */
    private String blacklistNo;

    /**
     * 适用类型 1 渠道 2 场景
     */
    private Integer applicableType;

    /**
     * 归属值
     */
    private String scopeValue;

    /**
     * 名称
     */
    private String scopeName;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;
}
