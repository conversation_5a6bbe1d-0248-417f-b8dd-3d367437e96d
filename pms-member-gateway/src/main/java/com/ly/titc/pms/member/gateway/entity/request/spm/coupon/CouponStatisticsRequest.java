package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotBlank;


/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-25 15:58
 */
@Data
@Accessors(chain = true)
public class CouponStatisticsRequest extends BaseRequest {


    /**
     * 券类型
     */
    private Integer couponType;

    /**
     * 模糊搜索
     */
    private String couponName;


    /**
     * 发放开始日期
     * 格式 yyyy-MM-dd
     */

    private String issueStartDate;
    /**
     * 发放结束日期
     * 格式 yyyy-MM-dd
     */
    private String issueEndDate;

    /**
     * 接收人类型
     */
    @NotBlank(message = "所属人类型不能为空")
    private String receiverType;

    /**
     * 接收人code
     */
    @NotBlank(message = "所属人code不能为空")
    private String receiverCode;

    /**
     * 券码
     * 模糊搜索
     */
    private String couponCode;
}
