package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeRecordResp;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.gateway.converter.AssetConverter;
import com.ly.titc.pms.member.gateway.entity.request.asset.PageMemberPointConsumeRecordRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.PageMemberStoreConsumeRequest;
import com.ly.titc.pms.member.gateway.entity.request.asset.RechargeRecordDetailRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.MemberBaseRequest;
import com.ly.titc.pms.member.gateway.entity.response.asset.*;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberOrderDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberOrderDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.AssetDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.coupon.CouponDecorator;
import com.ly.titc.pms.member.mediator.service.MemberAssetMedService;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.QueryCouponStatisticsReq;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.CouponStatisticsResp;
import com.ly.titc.pms.spm.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.ReceiverTypeEnum;
import com.ly.watcher.common.utils.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Collections;
import java.util.List;
import java.util.UUID;
import java.util.stream.Collectors;

/**
 * 会员资产
 *
 * @Author：rui
 * @name：MemberAssetController
 * @Date：2024-12-5 10:59
 * @Filename：MemberAssetController
 */
@Slf4j
@RestController
@RequestMapping("/member/asset")
public class MemberAssetController {

    @Resource
    private AssetDecorator assetDecorator;

    @Resource
    private CouponDecorator couponDecorator;

    @Resource
    private AssetConverter assetConverter;

    @Resource
    private MemberAssetMedService memberAssetMedService;

    @Resource
    private HotelDecorator hotelDecorator;

    /**
     * 分页查询会员消费、冻结记录
     *
     * @param request
     * @return
     */
    @PostMapping("/pageConsumeRecord")
    public Response<Pageable<MemberStoreConsumeRecordResponse>> pageConsumeRecord(@RequestBody @Valid PageMemberStoreConsumeRequest request) {
        // 这里的主体，是查询中的下拉框选择传入
        Pageable<MemberStoreConsumeRecordResp> pageable = assetDecorator.pageConsumeRecord(request.getMemberNo(), request.getMasterCode(), request.getMasterType(), request.getBeginTime(),
                request.getEndTime(), request.getPageIndex(), request.getPageSize(), request.getConsumeType(), request.getTrackingId(), request.getPlatformChannel());
        List<MemberStoreConsumeRecordResp> respList = pageable.getDatas();
        if (CollectionUtils.isEmpty(respList)) {
            return Response.success(Pageable.empty());
        }
        List<String> hotelCodes = respList.stream().filter(item -> item.getMasterCode().equals(MasterTypeEnum.HOTEL.getCode()))
                .map(MemberStoreConsumeRecordResp::getMasterCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hotelCodes)) {
            return Response.success(Pageable.convert(pageable, assetConverter.convertMemberStoreConsumeRecord(pageable.getDatas(), Collections.emptyList())));
        }
        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(null, hotelCodes, request.getTrackingId());
        return Response.success(Pageable.convert(pageable, assetConverter.convertMemberStoreConsumeRecord(pageable.getDatas(), hotelBaseInfos)));
    }

    /**
     * 分页查询会员充值记录
     */
    @PostMapping("/pageRechargeRecord")
    public Response<Pageable<MemberOrderRechargeResponse>> pageRechargeRecord(@RequestBody @Valid PageMemberStoreConsumeRequest request) {
        // 这里的主体，是查询中的下拉框选择传入
        Pageable<MemberOrderDto> pageable = memberAssetMedService.pageMemberStoredRechargeRecord(request.getMasterType(), request.getMasterCode(), request.getMemberNo(),
                request.getPageIndex(), request.getPageSize(), request.getState(), request.getBeginTime(), request.getEndTime(), request.getTrackingId());
        List<MemberOrderDto> respList = pageable.getDatas();
        if (CollectionUtils.isEmpty(respList)) {
            return Response.success(Pageable.empty());
        }
        List<String> hotelCodes = respList.stream().filter(item -> item.getMasterCode().equals(MasterTypeEnum.HOTEL.getCode()))
                .map(MemberOrderDto::getMasterCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hotelCodes)) {
            return Response.success(Pageable.convert(pageable, assetConverter.convertMemberOrderRecharge(respList, Collections.emptyList())));
        }
        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(null, hotelCodes, request.getTrackingId());
        return Response.success(Pageable.convert(pageable, assetConverter.convertMemberOrderRecharge(respList, hotelBaseInfos)));
    }

    /**
     * 充值记录详情
     */
    @PostMapping("/rechargeRecordDetail")
    public Response<MemberOrderRechargeDetailResponse> rechargeRecordDetail(@RequestBody @Valid RechargeRecordDetailRequest request) {
        MemberOrderDetailDto dto = memberAssetMedService.getMemberStoredRechargeRecord(request.getMemberOderNo());
        return Response.success(assetConverter.convert(dto));
    }

    /**
     * 积分记录查询
     */
    @PostMapping("/pagePointRecord")
    public Response<Pageable<MemberPointConsumeRecordResponse>> pagePointRecord(@RequestBody @Valid PageMemberPointConsumeRecordRequest request) {
        // 这里的主体，是查询中的下拉框选择传入
        Pageable<MemberPointsFlowInfoResp> pageable = assetDecorator.pagePointRecord(request.getPageIndex(), request.getPageSize(), request.getMemberNo(),
                request.getMasterCode(), request.getMasterType(), request.getBeginTime(), request.getEndTime(), request.getTrackingId());
        List<MemberPointsFlowInfoResp> respList = pageable.getDatas();
        if (CollectionUtils.isEmpty(respList)) {
            return Response.success(Pageable.empty());
        }
        List<String> hotelCodes = respList.stream().filter(item -> item.getMasterCode().equals(MasterTypeEnum.HOTEL.getCode()))
                .map(MemberPointsFlowInfoResp::getMasterCode).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(hotelCodes)) {
            return Response.success(Pageable.convert(pageable, assetConverter.convertMemberPointConsumeRecord(respList, Collections.emptyList())));
        }
        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(null, hotelCodes, request.getTrackingId());
        return Response.success(Pageable.convert(pageable, assetConverter.convertMemberPointConsumeRecord(respList, hotelBaseInfos)));
    }

    /**
     * 会员积分统计列表头查询 (积分调整头查询)
     */
    @PostMapping("getMemberTotalAccountPoints")
    public Response<MemberTotalPointResponse> getMemberTotalAccountPoints(@RequestBody @Valid MemberBaseRequest request) {
        return Response.success(assetConverter.convertMemberTotalPoint(assetDecorator.getTotalAccountPoints(request.getMemberNo(),request.getTrackingId())));
    }

    /**
     * 会员储值统计列表头查询
     */
    @PostMapping("getMemberTotalAccountAmount")
    public Response<MemberTotalAmountResponse> getMemberTotalAccountAmount(@RequestBody @Valid MemberBaseRequest request) {
        return Response.success(assetConverter.convertMemberTotalAmount(assetDecorator.getTotalAccountAmount(request.getMemberNo(),request.getTrackingId())));
    }

    /**
     * 查询会员资产
     *
     * @param request
     * @return
     */
    @PostMapping("getMemberAsset")
    public Response<MemberAssetResponse> getMemberAsset(@RequestBody @Valid MemberBaseRequest request){
        MemberAssetResponse memberAsset = new MemberAssetResponse();
        memberAsset.setPoint(assetConverter.convertMemberTotalPoint(assetDecorator.getTotalAccountPoints(request.getMemberNo(),request.getTrackingId())));
        memberAsset.setStore(assetConverter.convertMemberTotalAmount(assetDecorator.getTotalAccountAmount(request.getMemberNo(),request.getTrackingId())));

        QueryCouponStatisticsReq apiReq = new QueryCouponStatisticsReq();
        apiReq.setReceiverType(ReceiverTypeEnum.MEMBER.getCode());
        apiReq.setReceiverCode(request.getMemberNo());
        apiReq.setBlocCode(request.getBlocCode());
        apiReq.setTrackingId(request.getTrackingId());
        CouponStatisticsResp couponStatistics = couponDecorator.getCouponStatistics(apiReq);

        memberAsset.setCouponNum(couponStatistics == null ? 0 : couponStatistics.getUnUsedCouponNum());
        return Response.success(memberAsset);
    }

}
