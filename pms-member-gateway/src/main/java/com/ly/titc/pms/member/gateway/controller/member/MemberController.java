package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.cc.sdk.log.annotation.LogRecord;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardConfigResp;
import com.ly.titc.pms.member.biz.MemberCheckInRecordBiz;
import com.ly.titc.pms.member.com.annotation.OperationLog;
import com.ly.titc.pms.member.com.constant.ActionConstants;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.ModuleConstants;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.enums.SmsSceneEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dubbo.enums.HotelTypeEnum;
import com.ly.titc.pms.member.gateway.converter.MemberConverter;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.*;
import com.ly.titc.pms.member.gateway.entity.request.member.config.GetMemberRelatedConfigRequest;
import com.ly.titc.pms.member.gateway.entity.response.MemberRelatedConfigResponse;
import com.ly.titc.pms.member.gateway.entity.response.RegisterMemberResultResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberCardInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberResponse;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.CheckWhetherBlacklistDto;
import com.ly.titc.pms.member.mediator.entity.dto.data.CheckInStatisticsDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.message.MessageDecorator;
import com.ly.titc.pms.member.mediator.service.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 会员管理
 *
 * @Author：rui
 * @name：MemberController
 * @Date：2024-11-18 17:03
 * @Filename：MemberController
 */
@Slf4j
@RestController
@RequestMapping("/member")
public class MemberController {

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private MemberMedService memberMedService;

    @Resource
    private MemberConverter memberConverter;

    @Resource
    private MemberBlacklistMedService memberBlacklistMedService;

    @Resource
    private MemberCardInfoDecorator decorator;

    @Resource
    private HotelDecorator hotelDecorator;

    @Resource
    private MessageDecorator messageDecorator;

    @Resource
    private MessageMedService messageMedService;

    @Resource
    private MemberDataRecordMedService memberDataRecordMedService;

    /**
     * 会员分页查询
     *
     * @param request
     * @return
     */
    @PostMapping("/page")
    public Response<Pageable<MemberResponse>> page(@RequestBody @Valid PageMemberRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        PageMemberParamDto pageMemberParamDto = memberConverter.convertPageMemberParamBO(request, masterTuple.getFirst(), masterTuple.getSecond());
        if (StringUtils.isNotEmpty(request.getTagId())) {
            pageMemberParamDto.setTagIds(Arrays.asList(Long.valueOf(request.getTagId())));
        }
        Pageable<MemberDetailDto> pageable = memberMedService.pageMemberByFinalMode(pageMemberParamDto);
        List<MemberDetailDto> records = pageable.getDatas();
        if (CollectionUtils.isEmpty(records)) {
            return Response.success(Pageable.empty());
        }
        // 酒店配置
        List<HotelBaseInfoResp> hotelBaseInfoRespList = hotelDecorator.listHotelBaseInfos(request.getBlocCode(), null, request.getTrackingId());
        Map<String, HotelBaseInfoResp> hotelMap = hotelBaseInfoRespList.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, item -> item));
        // 卡配置
        List<Long> cardIdList = records.stream().flatMap(item -> item.getMemberCardInfos().stream()).map(MemberCardInfoDto::getCardId).collect(Collectors.toList());
        List<MemberCardConfigResp> cardConfigs = decorator.listMemberCard(cardIdList, request.getTrackingId());

        List<String> memberNos = records.stream().map(MemberDetailDto::getMemberNo).collect(Collectors.toList());
        // 查询会员入住次数
        List<CheckInStatisticsDto> checkInStatistics = memberDataRecordMedService.listByMemberNos(memberNos);
        Map<String, Integer> checkInMap = checkInStatistics.stream().collect(Collectors.toMap(CheckInStatisticsDto::getMemberNo, CheckInStatisticsDto::getCheckInCount));
        return Response.success(PageableUtil.convert(pageable, memberConverter.convertMemberResponseList(records, cardConfigs, hotelMap, checkInMap)));
    }

    /**
     * 会员注册
     */
    @PostMapping("/register")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_REGISTER_CODE, categoryName = ActionConstants.MEMBER_REGISTER_NAME)
    @OperationLog(operateRecord = "#member_register(#request)")
    public Response<RegisterMemberResultResponse> register(@RequestBody @Valid MemberRegisterRequest request) {
        RegisterMemberDto registerMemberDto = memberConverter.convertRegisterMemberDto(request);
        RegisterMemberResultDto registerResult = memberMedService.register(registerMemberDto);
        return Response.success(memberConverter.convertRegisterMemberResultResponse(registerResult));
    }

    /**
     * 获取会员相关配置(注册，验证，列表展示等)
     * @param request
     */
    @PostMapping("/relatedConfig")
    public Response<MemberRelatedConfigResponse> relatedConfig(@RequestBody @Valid GetMemberRelatedConfigRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(memberConverter.convertMemberRegisterCheckConfigResponse(decorator.getMemberRelatedConfig(masterTuple.getFirst(),
                request.getBlocCode(), request.getType(), request.getTrackingId())));
    }

    /**
     * 保存会员相关配置(注册，验证，列表展示等)
     * @param request
     */
    @PostMapping("/saveRelatedConfig")
    public Response saveRegisterCheckConfig(@RequestBody @Valid SaveMemberRegisterCheckConfigRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        decorator.saveMemberRelatedConfig(masterTuple.getFirst(), request.getBlocCode(), request.getParams(), request.getTrackingId(), request.getOperator(), StringUtils.isEmpty(request.getId()) ? null : Long.parseLong(request.getId()), request.getType());
        return Response.success();
    }

    /**
     * 校验卡号是否已经存在
     */
    @PostMapping("/checkCardNo")
    public Response<Boolean> checkCardNo(@RequestBody @Valid CheckCardNoRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        return Response.success(memberCardMedService.checkCardNoExist(masterTuple.getFirst(), request.getBlocCode(), request.getMemberCardNo()));
    }

    /**
     * 查询短信余额
     */
    @PostMapping("/querySmsBalance")
    public Response<Integer> querySmsBalance(@RequestBody @Valid BaseRequest request) {
        return Response.success(messageDecorator.querySmsBalance(request.getBlocCode()));
    }

    /**
     * 发送验证码
     */
    @PostMapping("/sendRegisterSms")
    public Response sendRegisterSms(@RequestBody @Valid SendRegisterSmsRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        messageMedService.sendSceneSms(masterTuple.getFirst(), request.getBlocCode(), request.getMobile(), SmsSceneEnum.REGISTER.getScene());
        return Response.success();
    }

    /**
     * 验证码验证--这里可能要考虑一下有没有必要单独走接口验证，目前在注册的接口会验证
     */
    @PostMapping("/verifyRegisterSms")
    public Response verifyRegisterSms(@RequestBody @Valid VerifyRegisterSmsRequest request) {
        messageMedService.verifyRegisterSms(request.getMobile(), request.getCode(), SmsSceneEnum.REGISTER.getScene());
        return Response.success();
    }

    /**
     * 会员详情
     */
    @PostMapping("/detail")
    public Response<MemberResponse> detail(@RequestBody @Valid MemberDetailRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberDetailDto memberDetailDto = memberMedService.getDetailByMemberNo(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo());
        if (memberDetailDto == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }
        // 酒店配置
        List<HotelBaseInfoResp> hotelBaseInfoRespList = hotelDecorator.listHotelBaseInfos(request.getBlocCode(), null, request.getTrackingId());
        Map<String, HotelBaseInfoResp> hotelMap = hotelBaseInfoRespList.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, item -> item));
        // 卡配置
        List<MemberCardInfoDto> memberCardInfos = memberCardMedService.listByMemberNo(masterTuple.getFirst(), request.getBlocCode(), request.getMemberNo());
        List<Long> cardIdList = memberCardInfos.stream().map(MemberCardInfoDto::getCardId).collect(Collectors.toList());
        List<MemberCardConfigResp> cardConfigs = decorator.listMemberCard(cardIdList, request.getTrackingId());
        // 查询会员入住次数
        List<CheckInStatisticsDto> checkInStatistics = memberDataRecordMedService.listByMemberNos(Collections.singletonList(memberDetailDto.getMemberNo()));
        Map<String, Integer> checkInMap = checkInStatistics.stream().collect(Collectors.toMap(CheckInStatisticsDto::getMemberNo, CheckInStatisticsDto::getCheckInCount));
        MemberResponse memberResponse = memberConverter.convertMemberResponseList(Collections.singletonList(memberDetailDto), cardConfigs, hotelMap, checkInMap).get(0);
        // 是否黑名单
        memberResponse.setBlackFlag(memberBlacklistMedService.checkWhetherBlacklist(request.getMemberNo(), null, null) ? Constant.ONE : Constant.ZERO);
        return Response.success(memberResponse);
    }

    /**
     * 保存会员信息
     */
    @PostMapping("/update")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_MANAGE_CODE, bizName = ModuleConstants.MEMBER_MANAGE_NAME,
            category = ActionConstants.MEMBER_INFO_UPDATE_CODE, categoryName = ActionConstants.MEMBER_INFO_UPDATE_NAME)
    @OperationLog(operateRecord = "#member_update(#request)")
    public Response update(@RequestBody @Valid SaveMemberRequest request) {
        UpdateMemberInfoDto dto = memberConverter.convertUpdateMemberInfoDto(request);
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        dto.getMemberInfo().setMasterType(masterTuple.getFirst());
        dto.getMemberInfo().setMasterCode(masterTuple.getSecond());
        memberMedService.updateMember(dto);
        return Response.success();
    }

    /**
     * 生成会员卡号
     */
    @PostMapping("/generateCardNo")
    public Response<String> generateCardNo(@RequestBody @Valid GenerateCardNoRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        String cardNo = memberCardMedService.generateCardNo(masterTuple.getFirst(), request.getBlocCode(), Long.parseLong(request.getCardId()));
        return Response.success(cardNo);
    }

    /**
     * 会员注销
     */
    @PostMapping("/cancel")
    public Response cancel(@RequestBody @Valid MemberBaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberMedService.cancelMember(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo(), request.getOperator());
        return Response.success();
    }

    /**
     * 会员恢复
     *
     * @param request
     * @return
     */
    @PostMapping("/recover")
    public Response recover(@RequestBody @Valid MemberBaseRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        memberMedService.recoverMember(masterTuple.getFirst(), masterTuple.getSecond(), request.getMemberNo(), request.getOperator());
        return Response.success();
    }

    /**
     * 修改密码
     */
    @PostMapping("/changePassword")
    public Response changePassword(@RequestBody @Valid ChangePasswordRequest request) {
        memberMedService.changePassword(request.getMemberNo(), request.getNewPassword(), request.getConfirmPassword(), request.getOperator());
        return Response.success();
    }

    /**
     * 根据手机号查询会员（传入当前会员，判断时排除当前会员）
     */
    @PostMapping("/queryByMobile")
    public Response<MemberResponse> queryByMobile(@RequestBody @Valid QueryByMobileRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        MemberInfoDto memberInfoDto = memberMedService.getByMobile(masterTuple.getFirst(), masterTuple.getSecond(), request.getMobile(), request.getMemberNo());
        return Response.success(memberConverter.convertMemberResponse(memberInfoDto));
    }

    /**
     * 根据手机号查询会员号
     *
     * @param request
     * @return
     */
    @PostMapping("/getMemberNo")
    public Response<String> getMemberNoByMobile(@RequestBody @Valid GetMemberNoRequest request){
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        String memberNo = memberMedService.getMemberNoByMobile(masterTuple.getFirst(), masterTuple.getSecond(), request.getMobile());
        return Response.success(memberNo);
    }

}
