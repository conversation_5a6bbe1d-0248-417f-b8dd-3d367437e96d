package com.ly.titc.pms.member.gateway.entity.request.member;

import com.ly.titc.pms.member.dubbo.enums.MemberStateEnum;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import com.ly.titc.pms.member.mediator.entity.dto.member.CustomerInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberContactInfoDto;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import java.math.BigDecimal;

/**
 * @Author：rui
 * @name：MemberRegisterRequest
 * @Date：2024-11-18 17:05
 * @Filename：MemberRegisterRequest
 */
@Data
public class MemberRegisterRequest extends BaseRequest {

    /**
     * 自定义会员号
     * <p>支持传入外部会员编号</p>
     */
    private String customizeMemberNo;

    /**
     * 会员手机号
     */
    @NotEmpty(message = "手机号不能为空")
    private String mobile;

    /**
     * 验证码，二次校验
     */
    private String verifyCode;

    /**
     * 证件类型
     */
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 会员昵称
     */
    private String nickName;

    /**
     * 会员真实姓名
     */
    @NotEmpty(message = "姓名不能空")
    private String realName;

    /**
     * 会员英文名
     */
    private String enName;

    /**
     * 性别1：男；2：女
     */
    private Integer gender;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 民族
     */
    private String nation;

    /**
     * 语言
     */
    private String language;

    /**
     * 车牌号
     */
    private String numberPlate;

    /**
     * 会员来源
     */
    private String source;

    /**
     * 注册门店类型 集团:BLOC;门店:HOTEL
     */
    @NotBlank(message = "注册门店类型不能为空")
    private String registerHotelType;

    /**
     * 注册门店(集团编号; 酒店编号)
     */
    @NotBlank(message = "注册门店不能为空")
    private String registerHotel;

    /**
     * 销售员
     */
    private String salesman;

    /**
     * 会员状态
     */
    private Integer state = MemberStateEnum.VALID.getState();

    /**
     * 备注
     */
    private String remark;

    /**
     * 联系方式
     */
    @Valid
    private MemberContactInfoRequest memberContactInfo;

    /**
     * 会员卡
     */
    @Valid
    private IssueMemberCardRequest memberCardInfo;

    /**
     * 客户信息（客户转会员必填）
     */
    @Valid
    private CustomerInfoDto customerInfo;

}
