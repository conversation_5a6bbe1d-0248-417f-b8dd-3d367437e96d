package com.ly.titc.pms.member.gateway.entity.request.member.record;

import com.ly.titc.pms.member.gateway.entity.request.PageBaseRequest;
import lombok.Data;

/**
 * @Author：rui
 * @name：PageCheckinRecordRequest
 * @Date：2024-12-11 21:32
 * @Filename：PageCheckinRecordRequest
 */
@Data
public class PagePurchaseCardRecordRequest extends PageBaseRequest {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 开始日期
     */
    private String beginTime;

    /**
     * 结束日期
     */
    private String endTime;


}
