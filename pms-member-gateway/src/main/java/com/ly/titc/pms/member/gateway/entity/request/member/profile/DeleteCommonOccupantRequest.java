package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：DeleteCommonOccupantRequest
 * @Date：2024-11-18 15:15
 * @Filename：DeleteCommonOccupantRequest
 */
@Data
public class DeleteCommonOccupantRequest extends BaseRequest {

    /**
     * 会员编号
     */
    @NotBlank(message = "会员编号不可为空")
    private String memberNo;

    /**
     * 常住人编号
     */
    @NotBlank(message = "常住人编号不可为空")
    private String occupantsNo;

    /**
     * 常住人姓名
     */
    @NotBlank(message = "常住人姓名不可为空")
    private String name;
}
