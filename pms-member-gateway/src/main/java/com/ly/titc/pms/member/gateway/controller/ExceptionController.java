package com.ly.titc.pms.member.gateway.controller;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.BaseRespCodeEnum;
import com.ly.titc.common.exceptions.ServiceException;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.converter.HttpMessageNotReadableException;
import org.springframework.validation.BindingResult;
import org.springframework.web.HttpRequestMethodNotSupportedException;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * 统一异常捕捉
 * <AUTHOR>
 * @classname CheckHealthController
 * @descrition 统一异常捕
 * @since 2019/10/22 18:41
 */
@ControllerAdvice
@Slf4j
public class ExceptionController {

  /**
   * HttpRequestMethodNotSupportedException
   *
   * @param e
   * @return
   */
  @ExceptionHandler(value = {HttpRequestMethodNotSupportedException.class})
  @ResponseBody
  public Response<Object> handleMethodNotSupportedException(Exception e) {

    log.warn("ExceptionController.handleMethodNotSupportedException;{}", e);
    return Response.set(BaseRespCodeEnum.CODE_400, null);
  }


  /**
   * HttpMessageNotReadableException
   *
   * @param e
   * @return
   */
  @ExceptionHandler(value = {HttpMessageNotReadableException.class})
  @ResponseBody
  public Response<Object> handleMessageNotReadableException(Exception e) {

    log.warn("ExceptionController.handleMessageNotReadableException;{}", e);
    return Response.set(BaseRespCodeEnum.CODE_400, null);
  }

  /**
   * ServiceException
   *
   * @param e
   * @return
   */
  @ExceptionHandler(value = {ServiceException.class})
  @ResponseBody
  public Response<Object> handleServiceException(Exception e) {

    ServiceException se = (ServiceException) e;
    return Response.set(se.getErrorCode(), se.getMessage(), null);
  }

  /**
   * Exception
   *
   * @param e
   * @return
   */
  @ExceptionHandler(value = {Exception.class})
  @ResponseBody
  public Response<Object> handleException(Exception e) {
    log.error(String.format("handler Exception,message:%s",e.getMessage()), e);
    return Response.unknownFailure(null);
  }

  /**
   * MethodArgumentNotValidException
   *
   * @param e
   * @return
   */
  @ExceptionHandler(value = {MethodArgumentNotValidException.class})
  @ResponseBody
  public Response<Object> handleMethodArgumentNotValidException(MethodArgumentNotValidException e) {

    log.warn("ExceptionController.handleMethodArgumentNotValidException;{}", e.toString());
    BindingResult br = e.getBindingResult();
    if (null == br) {
      return Response.set(BaseRespCodeEnum.CODE_400, null);
    }
    if (br.hasErrors()) {
      return Response.set(BaseRespCodeEnum.CODE_400.getCode(), br.getFieldError().getDefaultMessage(), null);
    }
    return Response.set(BaseRespCodeEnum.CODE_400, null);
  }

}
