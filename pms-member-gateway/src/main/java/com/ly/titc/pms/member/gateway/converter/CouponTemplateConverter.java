package com.ly.titc.pms.member.gateway.converter;


import com.ly.titc.cdm.dubbo.entity.response.plan.HotelRatePlanListResp;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.mdm.entity.response.hotel.room.type.RoomTypeBaseInfoResp;
import com.ly.titc.pms.member.gateway.entity.request.spm.ApplicableHotelInfoRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.ApplicableHotelRatePlanInfoRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.ApplicableHotelRoomTypeInfoRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.*;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.CouponEffectDateConfigDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.accounting.CouponAccountingRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.cancel.CouponCancelRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.grant.CouponGrantRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.CouponUseRuleDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableRuleDataInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.ApplicableRuleInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.rule.use.applicable.CommonInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.template.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateListResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.SaveCouponTemplateInfoResp;
import com.ly.titc.pms.spm.dubbo.enums.*;
import org.apache.logging.log4j.util.Strings;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Mapper(componentModel = "spring")
public interface CouponTemplateConverter {

    @Mapping(target = "pageSize", ignore = true)
    @Mapping(target = "sourceClient", ignore = true)
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "currPage", ignore = true)
    GetCouponTemplateListReq convertGetCouponTemplateListReqByListRequest(ListCouponTemplateRequest request);

    @Mapping(target = "sourceClient", ignore = true)
    @Mapping(target = "hotelCode", ignore = true)
    @Mapping(target = "currPage", source = "pageIndex")
    GetCouponTemplateListReq convertGetCouponTemplateListReq(PageCouponTemplateListRequest request);


    CouponTemplateListResponse convertCouponTemplateListResponse(CouponTemplateListResp resp);

    List<CouponTemplateListResponse> convertCouponTemplateListResponses(List<CouponTemplateListResp> respList);

    GetCouponTemplateDetailReq convertGetCouponTemplateDetailReq(GetCouponTemplateDetailRequest request);

    CouponApplicableHotelResponse convertCouponApplicableHotelResponse(PageHotelsResp pageHotelsResp);

    List<CouponApplicableHotelResponse> convertCouponApplicableHotelResponses(List<PageHotelsResp> pageHotelsRespList);

    @Mappings({
            @Mapping(target = "isEffectOneNight", source = "accountingRuleInfo.isEffectOneNight"),
            @Mapping(target = "postingItemCode", source = "accountingRuleInfo.postingItemCode"),
            @Mapping(target = "postingType", source = "accountingRuleInfo.postingType"),
            @Mapping(target = "orderCancelReturn", source = "cancelRuleInfo.orderCancelReturn"),
            @Mapping(target = "nightAuditBeforeCancel", source = "cancelRuleInfo.nightAuditBeforeCancel"),
            @Mapping(target = "useRuleInfo", ignore = true),
            @Mapping(target = "grantRuleInfo", ignore = true),
    })
    CouponTemplateInfoResponse convertCouponTemplateInfoResponse(CouponTemplateDetailResp resp);

    @Mappings({
            @Mapping(target = "blocRatePlanInfos", ignore = true),
            @Mapping(target = "blocRoomTypeInfos", ignore = true),
            @Mapping(target = "channelInfos", ignore = true),
            @Mapping(target = "hotelInfos", ignore = true),
            @Mapping(target = "hotelRoomTypeInfos", ignore = true),
            @Mapping(target = "memberLevelInfos", ignore = true),
            @Mapping(target = "hotelRatePlanInfos", ignore = true),
            @Mapping(target = "unApplicableTimeBucketInfos", ignore = true),
            @Mapping(target = "unApplicableWeekInfos", ignore = true)
    })
    CouponUseRuleResponse convertCouponUseRuleResponse(CouponUseRuleDto couponUseRuleDto);

    default List<CouponTimeBucketInfoResponse> convertCouponTimeBucketInfoResponse(List<String> applicableRuleDataInfo) {
        return applicableRuleDataInfo.stream().map(timeBucket -> {
            String[] split = timeBucket.split(Constant.STRING_COMMA);
            CouponTimeBucketInfoResponse response = new CouponTimeBucketInfoResponse();
            response.setStartTime(split[0]);
            response.setEndTime(split[1]);
            return response;
        }).collect(Collectors.toList());
    }

    @Mapping(target = "customizedHotelInfos", ignore = true)
    CouponGrantRuleResponse convertCouponGrantRuleResponse(CouponGrantRuleDto couponGrantRuleDto);


    default SaveCouponTemplateReq convertSaveCouponTemplateReq(SaveCouponTemplateRequest request, UserInfoDto user) {
        SaveCouponTemplateReq req = new SaveCouponTemplateReq();
        req.setTemplateCode(request.getTemplateCode());
        req.setMasterType(MasterTypeEnum.BLOC.getCode());
        req.setMasterCode(user.getBlocCode());
        req.setCouponName(request.getCouponName());
        req.setCouponType(request.getCouponType());
        req.setCouponValueType(Objects.isNull(request.getCouponValueType()) ? Constant.ONE : request.getCouponValueType());
        req.setCouponValue(StringUtils.isEmpty(request.getCouponValue()) ? String.valueOf(Constant.ZERO) : request.getCouponValue());
        req.setCouponTips(StringUtils.isEmpty(request.getCouponTips()) ? Strings.EMPTY : request.getCouponTips());
        req.setCouponContent(StringUtils.isEmpty(request.getCouponContent()) ? Strings.EMPTY : request.getCouponContent());
        req.setCostAttribution(request.getCostAttribution());
        req.setRedeemType(request.getRedeemType());
        req.setTotalQuantity(Objects.isNull(request.getTotalQuantity()) ? -1 : request.getTotalQuantity());
        req.setOperator(user.getUserName());
        req.setOperatorId(user.getUserId());
        req.setSourceClient(SourceClientEnum.CRM.getCode());
        req.setRemark(StringUtils.isEmpty(request.getRemark()) ? Strings.EMPTY : request.getRemark());
        req.setEffectDateInfo(convertCouponEffectDateConfigDto(request.getEffectDateInfo()));
        req.setAccountingRuleInfo(convertCouponAccountingRuleDto(request));
        req.setUseRuleInfo(convertCouponUseRuleDto(request, user));
        req.setCancelRuleInfo(convertCouponCancelRuleDto(request));
        req.setGrantRuleInfo(convertCouponGrantRuleDto(request.getGrantRuleInfo(), user));
        return req;
    }

    CouponEffectDateConfigDto convertCouponEffectDateConfigDto(CouponEffectDateConfigRequest request);

    default CouponAccountingRuleDto convertCouponAccountingRuleDto(SaveCouponTemplateRequest request) {
        CouponAccountingRuleDto dto = new CouponAccountingRuleDto();
        dto.setPostingType(Objects.isNull(request.getPostingType())
                ? Constant.ZERO : request.getPostingType());//默认不入账
        dto.setPostingItemCode(Objects.isNull(request.getPostingItemCode())
                ? Strings.EMPTY : request.getPostingItemCode()); //默认入账项目为空
        dto.setIsEffectOneNight(Objects.isNull(request.getIsEffectOneNight())
                ? Constant.ONE : request.getIsEffectOneNight());//默认仅生效一个房晚
        return dto;
    }

    default CouponCancelRuleDto convertCouponCancelRuleDto(SaveCouponTemplateRequest request) {
        CouponCancelRuleDto dto = new CouponCancelRuleDto();
        dto.setOrderCancelReturn(Objects.isNull(request.getOrderCancelReturn())
                ? Boolean.FALSE : request.getOrderCancelReturn());//订单取消后返回 默认False
        dto.setNightAuditBeforeCancel(Objects.isNull(request.getNightAuditBeforeCancel())
                ? Boolean.FALSE : request.getNightAuditBeforeCancel()); //夜审或加收房费前是否允许取消使用券 默认false
        return dto;
    }

    default CouponGrantRuleDto convertCouponGrantRuleDto(CouponGrantRuleRequest request, UserInfoDto userInfoDto) {
        CouponGrantRuleDto couponGrantRuleDto = new CouponGrantRuleDto();
        couponGrantRuleDto.setDeadline(request.getDeadline());
        couponGrantRuleDto.setIsOnlyBloc(request.getIsOnlyBloc());
        couponGrantRuleDto.setIsApplicableHotel(request.getIsApplicableHotel());
        couponGrantRuleDto.setCustomizeHotelInfos(
                convertApplicableHotelInfo(request.getCustomizedHotelInfos(), userInfoDto)
        );
        return couponGrantRuleDto;
    }

    default CouponUseRuleDto convertCouponUseRuleDto(SaveCouponTemplateRequest request, UserInfoDto userInfoDto) {
        CouponUseRuleDto couponUseRuleDto = new CouponUseRuleDto();
        //提前预定天数
        couponUseRuleDto.setAheadReserveDay(Objects.isNull(request.getUseRuleInfo().getAheadReserveDay())
                ? Constant.ZERO : request.getUseRuleInfo().getAheadReserveDay());
        //是否允许叠加
        couponUseRuleDto.setIsStacking(request.getUseRuleInfo().getIsStacking());
        //最低订单价格
        couponUseRuleDto.setOrderMinPrice(Objects.isNull(request.getUseRuleInfo().getOrderMinPrice())
                ? new BigDecimal(Constant.ZERO) : request.getUseRuleInfo().getOrderMinPrice());
        //最低房价
        couponUseRuleDto.setRoomMinPrice(Objects.isNull(request.getUseRuleInfo().getRoomMinPrice())
                ? new BigDecimal(Constant.ZERO) : request.getUseRuleInfo().getRoomMinPrice());
        List<ApplicableRuleInfoDto<String>> commonApplicableRuleInfoDtos = new ArrayList<>();
        //适用渠道
        commonApplicableRuleInfoDtos.add(convertApplicableChannelInfo(request.getUseRuleInfo()));
        //适用会员等级
        commonApplicableRuleInfoDtos.add(convertApplicableMemberLevel(request.getUseRuleInfo()));
        //适用时间周
        commonApplicableRuleInfoDtos.add(convertApplicableWeek(request.getUseRuleInfo()));
        //适用时间段
        commonApplicableRuleInfoDtos.add(convertApplicableTimeBucket(request.getUseRuleInfo()));
        couponUseRuleDto.setCommonApplicableRuleInfos(commonApplicableRuleInfoDtos);

        List<ApplicableRuleInfoDto<ApplicableRuleDataInfoDto>> applicableRuleInfos = new ArrayList<>();
        //适用酒店
        ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> applicableHotelRuleInfoDto =
                convertApplicableHotelInfo(request.getUseRuleInfo().getHotelInfos(), userInfoDto);
        if (!Objects.isNull(applicableHotelRuleInfoDto)) {
            applicableRuleInfos.add(applicableHotelRuleInfoDto);
        }
        //适用价格方案
        ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> applicableRatePlanRuleInfoDto =
                convertApplicableRatePlan(request.getUseRuleInfo(), userInfoDto);
        if (!Objects.isNull(applicableRatePlanRuleInfoDto)) {
            applicableRuleInfos.add(applicableRatePlanRuleInfoDto);
        }
        //适用房型
        ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> applicableRoomTypeRuleInfoDto =
                convertApplicableRoomType(request.getUseRuleInfo(), userInfoDto);
        if (!Objects.isNull(applicableRoomTypeRuleInfoDto)) {
            applicableRuleInfos.add(applicableRoomTypeRuleInfoDto);
        }
        couponUseRuleDto.setApplicableRuleInfos(applicableRuleInfos);
        return couponUseRuleDto;
    }

    default ApplicableRuleInfoDto<String> convertApplicableChannelInfo(CouponUseRuleRequest request) {
        if (!CollectionUtils.isEmpty(request.getChannelInfos())) {
            ApplicableRuleInfoDto<String> channelApplicableRuleInfoDto = new ApplicableRuleInfoDto<>();
            channelApplicableRuleInfoDto.setApplicableParentRuleType(ApplicableRuleTypeEnum.CHANNEL.getParentType());
            channelApplicableRuleInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
            channelApplicableRuleInfoDto.setApplicableRuleDataInfo(request.getChannelInfos());
            return channelApplicableRuleInfoDto;
        }
        return null;
    }

    default ApplicableRuleInfoDto<String> convertApplicableMemberLevel(CouponUseRuleRequest request) {
        if (!Objects.isNull(request.getMemberLevelInfos())) {
            ApplicableRuleInfoDto<String> memberLevelApplicableRuleInfoDto = new ApplicableRuleInfoDto<>();
            memberLevelApplicableRuleInfoDto.setApplicableParentRuleType(ApplicableRuleTypeEnum.MEMBER_LEVEL.getParentType());
            memberLevelApplicableRuleInfoDto.setApplicableScopeType(request.getMemberLevelInfos().getApplicableScopeType());
            memberLevelApplicableRuleInfoDto.setApplicableRuleDataInfo(request.getMemberLevelInfos().getMemberLevelCodes());
            return memberLevelApplicableRuleInfoDto;
        }
        return null;
    }

    default ApplicableRuleInfoDto<String> convertApplicableWeek(CouponUseRuleRequest request) {
        if (!CollectionUtils.isEmpty(request.getUnApplicableWeekInfos())) {
            ApplicableRuleInfoDto<String> weekApplicableRuleInfoDto = new ApplicableRuleInfoDto<>();
            weekApplicableRuleInfoDto.setApplicableParentRuleType(ApplicableRuleTypeEnum.WEEK.getParentType());
            weekApplicableRuleInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_UNAVAILABLE.getScopeType());
            weekApplicableRuleInfoDto.setApplicableRuleDataInfo(request.getUnApplicableWeekInfos());
            return weekApplicableRuleInfoDto;
        }
        return null;
    }

    default ApplicableRuleInfoDto<String> convertApplicableTimeBucket(CouponUseRuleRequest request) {
        if (!CollectionUtils.isEmpty(request.getUnApplicableTimeBucketInfos())) {
            ApplicableRuleInfoDto<String> timeBucketApplicableRuleInfoDto = new ApplicableRuleInfoDto<>();
            timeBucketApplicableRuleInfoDto.setApplicableParentRuleType(ApplicableRuleTypeEnum.TIME_BUCKET.getParentType());
            timeBucketApplicableRuleInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_UNAVAILABLE.getScopeType());
            timeBucketApplicableRuleInfoDto.setApplicableRuleDataInfo(
                    request.getUnApplicableTimeBucketInfos().stream().map(
                            item -> item.getStartTime() + Constant.STRING_COMMA + item.getEndTime()
                    ).collect(Collectors.toList())
            );
            return timeBucketApplicableRuleInfoDto;
        }
        return null;
    }

    default ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> convertApplicableHotelInfo(ApplicableHotelInfoRequest couponApplicableHotelInfoRequest,
                                                                                        UserInfoDto userInfoDto) {
        if (!Objects.isNull(couponApplicableHotelInfoRequest)) {
            ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> hotelApplicableInfo = new ApplicableRuleInfoDto<>();
            hotelApplicableInfo.setApplicableParentRuleType(ApplicableRuleTypeEnum.HOTEL.getParentType());
            hotelApplicableInfo.setApplicableScopeType(couponApplicableHotelInfoRequest.getApplicableScopeType());
            if (Objects.equals(couponApplicableHotelInfoRequest.getApplicableScopeType(), ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType())) {
                List<ApplicableRuleDataInfoDto> applicableRuleDataInfoDtos = new ArrayList<>();
                if (!CollectionUtils.isEmpty(couponApplicableHotelInfoRequest.getBrandInfos())) {
                    ApplicableRuleDataInfoDto brandApplicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
                    brandApplicableRuleDataInfoDto.setMasterType(MasterTypeEnum.BLOC.getCode());
                    brandApplicableRuleDataInfoDto.setMasterCode(userInfoDto.getBlocCode());
                    brandApplicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
                    brandApplicableRuleDataInfoDto.setApplicableRuleType(ApplicableRuleTypeEnum.BRAND.getRuleType());
                    brandApplicableRuleDataInfoDto.setBizData(
                            couponApplicableHotelInfoRequest.getBrandInfos().stream().map(brandInfo -> {
                                CommonInfoDto commonInfoDto = new CommonInfoDto();
                                commonInfoDto.setName(brandInfo.getName());
                                commonInfoDto.setCode(brandInfo.getCode());
                                return commonInfoDto;
                            }).collect(Collectors.toList())
                    );
                    applicableRuleDataInfoDtos.add(brandApplicableRuleDataInfoDto);
                }
                if (!CollectionUtils.isEmpty(couponApplicableHotelInfoRequest.getAreaInfos())) {
                    ApplicableRuleDataInfoDto areaApplicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
                    areaApplicableRuleDataInfoDto.setMasterType(MasterTypeEnum.BLOC.getCode());
                    areaApplicableRuleDataInfoDto.setMasterCode(userInfoDto.getBlocCode());
                    areaApplicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
                    areaApplicableRuleDataInfoDto.setApplicableRuleType(ApplicableRuleTypeEnum.AREA.getRuleType());
                    areaApplicableRuleDataInfoDto.setBizData(
                            couponApplicableHotelInfoRequest.getAreaInfos().stream().map(areaInfo -> {
                                CommonInfoDto commonInfoDto = new CommonInfoDto();
                                commonInfoDto.setName(areaInfo.getName());
                                commonInfoDto.setCode(areaInfo.getCode());
                                return commonInfoDto;
                            }).collect(Collectors.toList())
                    );
                    applicableRuleDataInfoDtos.add(areaApplicableRuleDataInfoDto);
                }
                if (!CollectionUtils.isEmpty(couponApplicableHotelInfoRequest.getRegionInfos())) {
                    ApplicableRuleDataInfoDto regionApplicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
                    regionApplicableRuleDataInfoDto.setMasterType(MasterTypeEnum.BLOC.getCode());
                    regionApplicableRuleDataInfoDto.setMasterCode(userInfoDto.getBlocCode());
                    regionApplicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
                    regionApplicableRuleDataInfoDto.setApplicableRuleType(ApplicableRuleTypeEnum.REGION.getRuleType());
                    regionApplicableRuleDataInfoDto.setBizData(
                            couponApplicableHotelInfoRequest.getRegionInfos().stream().map(brandInfo -> {
                                CommonInfoDto commonInfoDto = new CommonInfoDto();
                                commonInfoDto.setName(brandInfo.getName());
                                commonInfoDto.setCode(brandInfo.getCode());
                                return commonInfoDto;
                            }).collect(Collectors.toList())
                    );
                    applicableRuleDataInfoDtos.add(regionApplicableRuleDataInfoDto);
                }
                if (!CollectionUtils.isEmpty(couponApplicableHotelInfoRequest.getHotelInfos())) {
                    ApplicableRuleDataInfoDto hotelApplicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
                    hotelApplicableRuleDataInfoDto.setMasterType(MasterTypeEnum.BLOC.getCode());
                    hotelApplicableRuleDataInfoDto.setMasterCode(userInfoDto.getBlocCode());
                    hotelApplicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
                    hotelApplicableRuleDataInfoDto.setApplicableRuleType(ApplicableRuleTypeEnum.HOTEL.getRuleType());
                    hotelApplicableRuleDataInfoDto.setBizData(
                            couponApplicableHotelInfoRequest.getHotelInfos().stream().map(brandInfo -> {
                                CommonInfoDto commonInfoDto = new CommonInfoDto();
                                commonInfoDto.setName(brandInfo.getName());
                                commonInfoDto.setCode(brandInfo.getCode());
                                return commonInfoDto;
                            }).collect(Collectors.toList())
                    );
                    applicableRuleDataInfoDtos.add(hotelApplicableRuleDataInfoDto);
                }
                hotelApplicableInfo.setApplicableRuleDataInfo(applicableRuleDataInfoDtos);
            }
            return hotelApplicableInfo;
        }
        return null;
    }

    default ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> convertApplicableRatePlan(CouponUseRuleRequest request,
                                                                                       UserInfoDto userInfoDto) {
        if (!CollectionUtils.isEmpty(request.getBlocApplicableRatePlanInfos())) {
            //集团价格方案
            ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> ratePlanApplicableInfo = new ApplicableRuleInfoDto<>();
            ratePlanApplicableInfo.setApplicableParentRuleType(ApplicableRuleTypeEnum.RATE_PLAN.getParentType());
            ratePlanApplicableInfo.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
            ApplicableRuleDataInfoDto applicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
            applicableRuleDataInfoDto.setMasterType(MasterTypeEnum.BLOC.getCode());
            applicableRuleDataInfoDto.setMasterCode(userInfoDto.getBlocCode());
            applicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
            applicableRuleDataInfoDto.setBizData(
                    request.getBlocApplicableRatePlanInfos().stream().map(blocRatePlanCode -> {
                        CommonInfoDto commonInfoDto = new CommonInfoDto();
                        commonInfoDto.setCode(blocRatePlanCode);
                        return commonInfoDto;
                    }).collect(Collectors.toList())
            );
            ratePlanApplicableInfo.setApplicableRuleDataInfo(Collections.singletonList(applicableRuleDataInfoDto));
            return ratePlanApplicableInfo;
        }
        if (!Objects.isNull(request.getHotelApplicableRatePlanInfos())) {
            //酒店价格方案
            ApplicableHotelRatePlanInfoRequest hotelApplicableRatePlanInfos = request.getHotelApplicableRatePlanInfos();
            ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> ratePlanApplicableInfo = new ApplicableRuleInfoDto<>();
            ratePlanApplicableInfo.setApplicableParentRuleType(ApplicableRuleTypeEnum.RATE_PLAN.getParentType());
            ratePlanApplicableInfo.setApplicableScopeType(hotelApplicableRatePlanInfos.getApplicableScopeType());
            if (!CollectionUtils.isEmpty(hotelApplicableRatePlanInfos.getHotelRatePlanInfos())) {
                List<ApplicableRuleDataInfoDto> applicableRuleDataInfoDtos =
                        hotelApplicableRatePlanInfos.getHotelRatePlanInfos().stream().map(hotelRatePlan -> {
                            ApplicableRuleDataInfoDto applicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
                            applicableRuleDataInfoDto.setMasterType(MasterTypeEnum.HOTEL.getCode());
                            applicableRuleDataInfoDto.setMasterCode(hotelRatePlan.getHotelCode());
                            if (!CollectionUtils.isEmpty(hotelRatePlan.getRatePlanInfos())) {
                                applicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
                                applicableRuleDataInfoDto.setBizData(
                                        hotelRatePlan.getRatePlanInfos().stream().map(ratePlanInfo -> {
                                            CommonInfoDto commonInfoDto = new CommonInfoDto();
                                            commonInfoDto.setCode(ratePlanInfo.getCode());
                                            commonInfoDto.setName(ratePlanInfo.getName());
                                            return commonInfoDto;
                                        }).collect(Collectors.toList())
                                );
                            } else {
                                applicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.ALL.getScopeType());
                            }

                            return applicableRuleDataInfoDto;
                        }).collect(Collectors.toList());
                ratePlanApplicableInfo.setApplicableRuleDataInfo(applicableRuleDataInfoDtos);
            }
            return ratePlanApplicableInfo;
        }
        return null;
    }

    default ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> convertApplicableRoomType(CouponUseRuleRequest request,
                                                                                       UserInfoDto userInfoDto) {
        //集团房型
        if (!CollectionUtils.isEmpty(request.getBlocApplicableRoomTypeInfos())) {
            ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> roomTypeApplicableInfo = new ApplicableRuleInfoDto<>();
            roomTypeApplicableInfo.setApplicableParentRuleType(ApplicableRuleTypeEnum.ROOM_TYPE.getParentType());
            roomTypeApplicableInfo.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
            ApplicableRuleDataInfoDto applicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
            applicableRuleDataInfoDto.setMasterType(MasterTypeEnum.BLOC.getCode());
            applicableRuleDataInfoDto.setMasterCode(userInfoDto.getBlocCode());
            applicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
            applicableRuleDataInfoDto.setBizData(
                    request.getBlocApplicableRoomTypeInfos().stream().map(blocRatePlanCode -> {
                        CommonInfoDto commonInfoDto = new CommonInfoDto();
                        commonInfoDto.setCode(blocRatePlanCode);
                        return commonInfoDto;
                    }).collect(Collectors.toList())
            );
            roomTypeApplicableInfo.setApplicableRuleDataInfo(Collections.singletonList(applicableRuleDataInfoDto));
            return roomTypeApplicableInfo;
        }
        //酒店房型
        if (!Objects.isNull(request.getHotelApplicableRoomTypeInfos())) {
            ApplicableHotelRoomTypeInfoRequest hotelApplicableRoomTypeInfos = request.getHotelApplicableRoomTypeInfos();
            ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> roomTypeApplicableInfo = new ApplicableRuleInfoDto<>();
            roomTypeApplicableInfo.setApplicableParentRuleType(ApplicableRuleTypeEnum.ROOM_TYPE.getParentType());
            roomTypeApplicableInfo.setApplicableScopeType(hotelApplicableRoomTypeInfos.getApplicableScopeType());
            if (!CollectionUtils.isEmpty(hotelApplicableRoomTypeInfos.getHotelRoomTypeInfos())) {
                List<ApplicableRuleDataInfoDto> applicableRuleDataInfoDtos =
                        hotelApplicableRoomTypeInfos.getHotelRoomTypeInfos().stream().map(hotelRoomType -> {
                            ApplicableRuleDataInfoDto applicableRuleDataInfoDto = new ApplicableRuleDataInfoDto();
                            applicableRuleDataInfoDto.setMasterType(MasterTypeEnum.HOTEL.getCode());
                            applicableRuleDataInfoDto.setMasterCode(hotelRoomType.getHotelCode());
                            if (!CollectionUtils.isEmpty(hotelRoomType.getRoomTypeInfos())) {
                                applicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType());
                                applicableRuleDataInfoDto.setBizData(
                                        hotelRoomType.getRoomTypeInfos().stream().map(ratePlanInfo -> {
                                            CommonInfoDto commonInfoDto = new CommonInfoDto();
                                            commonInfoDto.setCode(ratePlanInfo.getCode());
                                            commonInfoDto.setName(ratePlanInfo.getName());
                                            return commonInfoDto;
                                        }).collect(Collectors.toList())
                                );
                            } else {
                                applicableRuleDataInfoDto.setApplicableScopeType(ApplicableScopeTypeEnum.ALL.getScopeType());
                            }

                            return applicableRuleDataInfoDto;
                        }).collect(Collectors.toList());
                roomTypeApplicableInfo.setApplicableRuleDataInfo(applicableRuleDataInfoDtos);
            }
            return roomTypeApplicableInfo;
        }
        return null;
    }

    @Mapping(target = "operator", ignore = true)
    @Mapping(target = "masterType", ignore = true)
    @Mapping(target = "masterCode", ignore = true)
    DeleteCouponTemplateReq convertDeleteCouponTemplateReq(DeleteCouponTemplateRequest request);

    @Mapping(target = "operator", ignore = true)
    @Mapping(target = "masterType", ignore = true)
    @Mapping(target = "masterCode", ignore = true)
    ChangeCouponTemplateStateReq convertRepealCouponTemplateReq(RepealCouponTemplateRequest request);

    SaveCouponTemplateInfoResponse convertSaveCouponTemplateInfoResponse(SaveCouponTemplateInfoResp resp);

    GetCouponTemplateDetailReq convertGetCouponTemplateDetailReqByApplicableInfo(CouponApplicableInfoRequest request);

    CouponApplicableInfoRequest convertCouponApplicableInfoRequestByRoomType(PageCouponApplicableRoomTypeRequest request);

    CouponApplicableInfoRequest convertCouponApplicableInfoRequestByRatePlan(PageCouponApplicableRatePlanRequest request);

    GetCouponTemplateApplicableHotelListReq convertGetCouponTemplateApplicableHotelListReq(PageCouponApplicableHotelRequest request);

    CouponApplicableRoomTypeResponse convertCouponApplicableRoomTypeResponse(RoomTypeBaseInfoResp resp);

    List<CouponApplicableRoomTypeResponse> convertCouponApplicableRoomTypeResponses(List<RoomTypeBaseInfoResp> roomTypeInfos);


    default List<CouponApplicableRoomTypeResponse> convertCouponApplicableRoomTypeResponses(List<RoomTypeBaseInfoResp> roomTypeInfos,
                                                                                            Set<String> havingPermissionHotelCodes,
                                                                                            ApplicableHotelRoomTypeInfoResponse hotelRoomTypeInfos) {
        Map<String, RoomTypeBaseInfoResp> RoomTypeBaseInfoMap = roomTypeInfos.stream().collect(
                Collectors.toMap(RoomTypeBaseInfoResp::getRoomTypeCode, Function.identity(), (o1, o2) -> o2));
        return hotelRoomTypeInfos.getHotelRoomTypeInfos().stream()
                .filter(item -> havingPermissionHotelCodes.contains(item.getHotelCode()))
                .map(item -> item.getRoomTypeInfos().stream().map(roomTypeInfo -> {
                    RoomTypeBaseInfoResp roomTypeBaseInfoResp = RoomTypeBaseInfoMap.get(roomTypeInfo.getCode());
                    CouponApplicableRoomTypeResponse response = new CouponApplicableRoomTypeResponse();
                    response.setRoomTypeCode(roomTypeInfo.getCode());
                    response.setRoomTypeName(roomTypeBaseInfoResp.getRoomTypeName());
                    response.setHotelCode(roomTypeBaseInfoResp.getHotelCode());
                    response.setHotelName(roomTypeBaseInfoResp.getHotelName());
                    return response;
                }).collect(Collectors.toList())).flatMap(Collection::stream).collect(Collectors.toList());
    }

    default List<CouponApplicableRoomTypeResponse> convertCouponApplicableRatePlanResponses(List<HotelRatePlanListResp> hotelRatePlans,
                                                                                            Map<String, String> hotelCodeMap) {
        return hotelRatePlans.stream().map(item -> {
            CouponApplicableRoomTypeResponse response = new CouponApplicableRoomTypeResponse();
            response.setHotelCode(item.getHotelCode());
            response.setHotelName(hotelCodeMap.getOrDefault(item.getHotelCode(), ""));
            response.setRoomTypeCode(item.getRatePlanCode());
            response.setRoomTypeName(item.getRatePlanName());
            return response;
        }).collect(Collectors.toList());
    }

    default List<CouponApplicableRoomTypeResponse> convertCouponApplicableRatePlanResponses(List<HotelRatePlanListResp> hotelRatePlans,
                                                                                            Set<String> havingPermissionHotelCodes,
                                                                                            List<ApplicableHotelRatePlanDataInfoResponse> hotelRatePlanInfos,
                                                                                            Map<String, String> hotelCodeMap) {
        Map<String, HotelRatePlanListResp> hotelRatePlaMap = hotelRatePlans.stream()
                .collect(Collectors.toMap(HotelRatePlanListResp::getRatePlanCode, Function.identity(), (o1, o2) -> o2));
        return hotelRatePlanInfos.stream().filter(item -> havingPermissionHotelCodes.contains(item.getHotelCode()))
                .map(item -> item.getRatePlanInfos().stream().map(ratePlanInfo->{
                    CouponApplicableRoomTypeResponse response = new CouponApplicableRoomTypeResponse();
                    response.setHotelCode(item.getHotelCode());
                    response.setHotelName(hotelCodeMap.getOrDefault(item.getHotelCode(), ""));
                    response.setRoomTypeCode(ratePlanInfo.getCode());
                    response.setRoomTypeName(hotelRatePlaMap.get(ratePlanInfo.getCode()).getRatePlanName());
                    return response;
                }).collect(Collectors.toList())).flatMap(Collection::stream).collect(Collectors.toList());
    }


    default List<CouponTemplateTreeResponse> convertCouponTemplateTreeResponses(List<CouponTemplateListResp> responses) {
        if (CollectionUtils.isEmpty(responses)) {
            return Collections.emptyList();
        }
        Map<Integer, List<CouponTemplateListResp>> couponTypeMap =
                responses.stream().collect(Collectors.groupingBy(CouponTemplateListResp::getCouponType));
        List<CouponTemplateTreeResponse> returnList = new ArrayList<>();
        couponTypeMap.forEach((key, value) -> {
            CouponTemplateTreeResponse response = new CouponTemplateTreeResponse();
            response.setCouponType(key);
            response.setCouponTypeName(CouponTypeEnum.getEnum(key).getDesc());
            response.setTemplateInfos(this.convertCouponTemplateListResponses(value));
            returnList.add(response);
        });
        return returnList;
    }

    default void buildCouponCommonApplicableRuleInfo(List<ApplicableRuleInfoDto<String>> commonApplicableRuleInfos,
                                                     CouponUseRuleResponse couponUseRuleResponse) {
        if (CollectionUtils.isEmpty(commonApplicableRuleInfos)) {
            return;
        }
        commonApplicableRuleInfos.stream().parallel().forEach(item -> {
            List<String> dataInfo = CollectionUtils.isEmpty(item.getApplicableRuleDataInfo())
                    ? Collections.emptyList() : item.getApplicableRuleDataInfo();
            if (Objects.equals(item.getApplicableParentRuleType(), ApplicableRuleTypeEnum.WEEK.getParentType())) {
                couponUseRuleResponse.setUnApplicableWeekInfos(dataInfo);
            } else if (Objects.equals(item.getApplicableParentRuleType(), ApplicableRuleTypeEnum.TIME_BUCKET.getParentType())) {
                couponUseRuleResponse.setUnApplicableTimeBucketInfos(CollectionUtils.isEmpty(dataInfo)
                        ? Collections.emptyList() : this.convertCouponTimeBucketInfoResponse(dataInfo)
                );
            } else if (Objects.equals(item.getApplicableParentRuleType(), ApplicableRuleTypeEnum.CHANNEL.getParentType())) {
                couponUseRuleResponse.setChannelInfos(dataInfo);
            } else if (Objects.equals(item.getApplicableParentRuleType(), ApplicableRuleTypeEnum.MEMBER_LEVEL.getParentType())) {
                ApplicableMemberLevelResponse response = new ApplicableMemberLevelResponse();
                response.setApplicableScopeType(item.getApplicableScopeType());
                response.setMemberLevelCodes(dataInfo);
                couponUseRuleResponse.setMemberLevelInfos(response);
            }
        });
    }


    default void buildCouponOtherApplicableRuleInfo(List<ApplicableRuleInfoDto<ApplicableRuleDataInfoDto>> applicableRuleInfos,
                                                    List<HotelBaseInfoResp> hotelBaseInfoResp,
                                                    CouponUseRuleResponse couponUseRuleResponse) {
        Map<String, String> hotelCodeAndNameMap = hotelBaseInfoResp.stream()
                .collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, HotelBaseInfoResp::getHotelName, (o1, o2) -> o1));
        applicableRuleInfos.stream().parallel().forEach(item -> {
            if (Objects.equals(item.getApplicableParentRuleType(), ApplicableRuleTypeEnum.HOTEL.getParentType())) {
                couponUseRuleResponse.setHotelInfos(this.buildCouponApplicableHotelInfoResponse(item));
            } else if (Objects.equals(item.getApplicableParentRuleType(), ApplicableRuleTypeEnum.ROOM_TYPE.getParentType())) {
                this.buildCouponApplicableRoomTypeInfoResponse(item, hotelCodeAndNameMap, couponUseRuleResponse);
            } else if (Objects.equals(item.getApplicableParentRuleType(), ApplicableRuleTypeEnum.RATE_PLAN.getParentType())) {
                this.buildCouponApplicableRatePlanInfoResponse(item, hotelCodeAndNameMap, couponUseRuleResponse);
            }
        });
    }

    default ApplicableHotelInfoResponse buildCouponApplicableHotelInfoResponse(
            ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> applicableRuleInfoDto) {
        ApplicableHotelInfoResponse response = new ApplicableHotelInfoResponse();
        if (Objects.isNull(applicableRuleInfoDto)) {
            return response;
        }
        response.setApplicableScopeType(applicableRuleInfoDto.getApplicableScopeType());
        if (Objects.equals(applicableRuleInfoDto.getApplicableScopeType(), ApplicableScopeTypeEnum.ALL.getScopeType())) {
            return response;
        } else {
            List<ApplicableRuleDataInfoDto> applicableRuleDataInfo = applicableRuleInfoDto.getApplicableRuleDataInfo();
            applicableRuleDataInfo.stream().parallel().forEach(ruleDataInfo -> {
                List<CommonNameInfoResponse> commonNameInfoResponseList = this.convertCommonNameInfoResponses(ruleDataInfo.getBizData());
                if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.BRAND.getRuleType())) {
                    response.setBrandInfos(commonNameInfoResponseList);
                } else if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.AREA.getRuleType())) {
                    response.setAreaInfos(commonNameInfoResponseList);
                } else if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.REGION.getRuleType())) {
                    response.setRegionInfos(commonNameInfoResponseList);
                } else if (Objects.equals(ruleDataInfo.getApplicableRuleType(), ApplicableRuleTypeEnum.HOTEL.getRuleType())) {
                    response.setHotelInfos(commonNameInfoResponseList);
                }
            });
            return response;
        }
    }

    default void buildCouponApplicableRoomTypeInfoResponse(ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> applicableRuleInfoDto,
                                                           Map<String, String> hotelCodeAndNameMap,
                                                           CouponUseRuleResponse couponUseRuleResponse) {
        ApplicableHotelRoomTypeInfoResponse hotelRoomTypeInfo = new ApplicableHotelRoomTypeInfoResponse();
        hotelRoomTypeInfo.setApplicableScopeType(applicableRuleInfoDto.getApplicableScopeType());
        List<String> blocRoomTypeInfos = new ArrayList<>();
        List<ApplicableHotelRoomTypeDataInfoResponse> hotelRoomTypeInfos = new ArrayList<>();
        if (Objects.equals(applicableRuleInfoDto.getApplicableScopeType(), ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType())) {
            List<ApplicableRuleDataInfoDto> applicableRuleDataInfo = applicableRuleInfoDto.getApplicableRuleDataInfo();
            applicableRuleDataInfo.stream().parallel().forEach(dataInfo -> {
                if (Objects.equals(dataInfo.getMasterType(), MasterTypeEnum.BLOC.getCode())) {
                    blocRoomTypeInfos.addAll(dataInfo.getBizData().stream().map(CommonInfoDto::getCode).collect(Collectors.toList()));
                } else {
                    ApplicableHotelRoomTypeDataInfoResponse response = new ApplicableHotelRoomTypeDataInfoResponse();
                    response.setHotelCode(dataInfo.getMasterCode());
                    String hotelName = Objects.isNull(hotelCodeAndNameMap)
                            ? Strings.EMPTY : hotelCodeAndNameMap.getOrDefault(dataInfo.getMasterCode(), Strings.EMPTY);
                    response.setHotelName(hotelName);
                    response.setRoomTypeInfos(this.convertCommonNameInfoResponses(dataInfo.getBizData()));
                    hotelRoomTypeInfos.add(response);
                }
            });
        }
        hotelRoomTypeInfo.setHotelRoomTypeInfos(hotelRoomTypeInfos);
        couponUseRuleResponse.setHotelRoomTypeInfos(hotelRoomTypeInfo);
        couponUseRuleResponse.setBlocRoomTypeInfos(blocRoomTypeInfos);
    }

    default void buildCouponApplicableRatePlanInfoResponse(ApplicableRuleInfoDto<ApplicableRuleDataInfoDto> applicableRuleInfoDto,
                                                           Map<String, String> hotelCodeAndNameMap,
                                                           CouponUseRuleResponse couponUseRuleResponse) {
        ApplicableHotelRatePlanInfoResponse hotelRatePlanInfo = new ApplicableHotelRatePlanInfoResponse();
        hotelRatePlanInfo.setApplicableScopeType(applicableRuleInfoDto.getApplicableScopeType());
        List<String> blocRatePlanInfos = new ArrayList<>();
        List<ApplicableHotelRatePlanDataInfoResponse> hotelRatePlanInfos = new ArrayList<>();
        if (Objects.equals(applicableRuleInfoDto.getApplicableScopeType(), ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType())) {
            List<ApplicableRuleDataInfoDto> applicableRuleDataInfo = applicableRuleInfoDto.getApplicableRuleDataInfo();
            applicableRuleDataInfo.stream().parallel().forEach(dataInfo -> {
                if (Objects.equals(dataInfo.getMasterType(), MasterTypeEnum.BLOC.getCode())) {
                    blocRatePlanInfos.addAll(dataInfo.getBizData().stream().map(CommonInfoDto::getCode).collect(Collectors.toList()));
                } else {
                    ApplicableHotelRatePlanDataInfoResponse response = new ApplicableHotelRatePlanDataInfoResponse();
                    response.setHotelCode(dataInfo.getMasterCode());
                    String hotelName = Objects.isNull(hotelCodeAndNameMap)
                            ? Strings.EMPTY : hotelCodeAndNameMap.getOrDefault(dataInfo.getMasterCode(), Strings.EMPTY);
                    response.setHotelName(hotelName);
                    response.setRatePlanInfos(this.convertCommonNameInfoResponses(dataInfo.getBizData()));
                    hotelRatePlanInfos.add(response);
                }
            });
        }
        hotelRatePlanInfo.setHotelRatePlanInfos(hotelRatePlanInfos);
        couponUseRuleResponse.setHotelRatePlanInfos(hotelRatePlanInfo);
        couponUseRuleResponse.setBlocRatePlanInfos(blocRatePlanInfos);
    }

    default List<String> getApplicableHotelCodes(List<ApplicableRuleInfoDto<ApplicableRuleDataInfoDto>> applicableRuleInfos) {
        return applicableRuleInfos.stream().filter(item ->
                        Objects.equals(item.getApplicableScopeType(), ApplicableScopeTypeEnum.APPOINT_AVAILABLE.getScopeType())
                                || Objects.equals(item.getApplicableScopeType(), ApplicableScopeTypeEnum.APPOINT_UNAVAILABLE.getScopeType()))
                .map(item -> item.getApplicableRuleDataInfo().stream()
                        .filter(dataInfo ->
                                Objects.equals(dataInfo.getMasterType(), MasterTypeEnum.HOTEL.getCode()) && !StringUtils.isEmpty(dataInfo.getMasterCode()))
                        .map(ApplicableRuleDataInfoDto::getMasterCode)
                        .collect(Collectors.toList()))
                .flatMap(Collection::stream).distinct().collect(Collectors.toList());
    }

    CommonNameInfoResponse convertCommonNameInfoResponse(CommonInfoDto commonInfoDto);

    List<CommonNameInfoResponse> convertCommonNameInfoResponses(List<CommonInfoDto> commonInfoDto);
}
