package com.ly.titc.pms.member.gateway.entity.request.spm.activity;

import com.ly.titc.pms.spm.dubbo.entity.dto.activity.common.MemberActivityCommonDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.common.MemberRechargeActivityGearsDto;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.Valid;
import java.util.List;

/**
 * 保存储值充值活动入参
 *
 * <AUTHOR>
 * @date 2024/12/28 14:18
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class SaveRechargeActivityRequest extends MemberActivityCommonRequest {

    /**
     * 充值活动时必填
     */
    @Valid
    private List<MemberRechargeActivityGearsDto> carriers;

    /**
     * 礼金有效期
     */
    private Integer storeLimit;

    /**
     * 礼金有效期单位 年 YEAR 月:MONTH 日; DAY
     */
    private String storeLimitUnit;

    /**
     * 是否长期有效
     */
    private boolean isPerpetualEffect;

}
