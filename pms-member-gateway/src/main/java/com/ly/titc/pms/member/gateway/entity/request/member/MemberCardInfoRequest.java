package com.ly.titc.pms.member.gateway.entity.request.member;

import lombok.Data;

/**
 * 会员卡信息
 *
 * <AUTHOR>
 * @date 2024/11/22 10:42
 */
@Data
public class MemberCardInfoRequest {

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 实体卡号
     */
    private String physicalCardNo;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 会员卡类型
     */
    private Integer cardType;

    /**
     * 会员卡ID
     */
    private Long cardId;

    /**
     * 会员卡名称
     */
    private String cardName;

    /**
     * 会员卡等级
     */
    private Integer cardLevel;

    /**
     * 会员卡等级名称
     */
    private String cardLevelName;

    /**
     * 会员卡生效时间
     */
    private String effectBeginDate;

    /**
     * 会员卡失效时间
     */
    private String effectEndDate;

    /**
     * 状态 0 无效 1 有效
     */
    private Integer state;

    /**
     * 排序
     */
    private Integer sort;


}
