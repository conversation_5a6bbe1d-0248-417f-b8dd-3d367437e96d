
package com.ly.titc.pms.member.gateway.entity.response.spm.coupon;

import lombok.Data;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-5
 */
@Data
public class CouponTemplateTreeResponse {
    /**
     * 券类型 1.折扣券,2.代金券,3.免房券,4.餐饮券,5.抵扣券,6.小时券,7.延时券,8.升房券,9.体验券,10.通用券
     */
    private Integer couponType;

    /**
     * 券类型名称
     */
    private String couponTypeName;

    /**
     * 优惠券模版信息
     */
    private List<CouponTemplateListResponse> templateInfos;
}
