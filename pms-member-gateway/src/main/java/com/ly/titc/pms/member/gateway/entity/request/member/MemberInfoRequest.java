package com.ly.titc.pms.member.gateway.entity.request.member;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

/**
 * @Author：rui
 * @name：MemberInfoRequest
 * @Date：2024-11-18 20:30
 * @Filename：MemberInfoRequest
 */
@Data
public class MemberInfoRequest {

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 姓名
     */
    private String realName;

    /**
     * 昵称
     */
    private String nickName;

    /**
     * 英文名
     */
    private String enName;

    /**
     * 手机号
     */
    private String mobile;

    /**
     * 0:身份证;1:护照;2:学生证;3:军官证;4:回乡证;5:台胞证;6:港澳通行证;7:国际海员;8:外国人永久居留（身份）证;9:其他证件;10:警官证;11:士兵证;12:台湾通行证;13:入台证;14:户口薄;15:出生证明;16:中国驾照;17:港澳居民居住证;18:台湾居民居住证
     */
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 性别 1:男;2:女;3:保密
     */
    private Integer gender;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 自定义会员号
     */
    private String customizeMemberNo;

    /**
     * 登录密码
     */
    private String password;

    /**
     * 状态
     */
    private Integer state;
}
