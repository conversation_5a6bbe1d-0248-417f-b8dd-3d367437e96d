package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：SaveCommonInvoiceHeader
 * @Date：2024-11-18 14:18
 * @Filename：SaveCommonInvoiceHeader
 */
@Data
public class SaveCommonInvoiceHeaderRequest extends BaseRequest {

    /**
     * 发票抬头编号,新增时不传，编辑时必传
     */
    private String invoiceHeaderNo;

    /**
     * 会员号
     */
    @NotBlank(message = "会员号不可为空")
    private String memberNo;

    /**
     * 发票类型；1普通发票 2 专用发票
     */
    private Integer invoiceType;

    /**
     * 发票抬头
     */
    private String headerName;

    /**
     * 税号
     */
    private String taxCode;

    /**
     * 公司地址
     */
    private String companyAddress;

    /**
     * 公司电话
     */
    private String companyTel;

    /**
     * 银行名称
     */
    private String bankName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 修改人
     */
    private String modifyUser;
}
