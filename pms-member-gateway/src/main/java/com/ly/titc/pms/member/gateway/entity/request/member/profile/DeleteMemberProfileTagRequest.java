package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：DeleteMemberProfileTagRequest
 * @Date：2024-11-18 16:58
 * @Filename：DeleteMemberProfileTagRequest
 */
@Data
public class DeleteMemberProfileTagRequest extends BaseRequest {

    /**
     * 会员编号
     */
    @NotBlank(message = "会员编号不能为空")
    private String memberNo;

    /**
     * 标签编号
     */
    @NotBlank(message = "标签编号不能为空")
    private String tagNo;

    /**
     * 标签名称
     */
    @NotBlank(message = "标签名称不能为空")
    private String tagName;
}
