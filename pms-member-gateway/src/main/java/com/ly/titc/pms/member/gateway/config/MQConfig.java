package com.ly.titc.pms.member.gateway.config;

import com.ly.titc.springboot.mq.consumer.callback.DefaultTurboMQConsumerCallback;
import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableAsync;


@Configuration
@EnableAsync
public class MQConfig {

    /**
     * 生产组
     */
    @Value("${turbomq.producer.pms-member.group}")
    private String producerGroup;
    /**
     * NameServer的地址
     */
    @Value("${turbomq.producer.pms-member.nameserver}")
    private String nameServer;

    /**
     * 生产者
     * @return
     */
    @Bean(name = "producer",initMethod = "start",destroyMethod = "shutdown")
    public TurboMQProducer getTurboMQProducerBean() {

        TurboMQProducer producer = new TurboMQProducer();
        producer.setGroup(producerGroup);
        producer.setNameserver(nameServer);
        return producer;
    }


}
