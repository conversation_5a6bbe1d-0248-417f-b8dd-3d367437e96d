package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：ListCommonOccupantRequest
 * @Date：2024-11-18 15:15
 * @Filename：ListCommonOccupantRequest
 */
@Data
public class ListCommonOccupantRequest extends BaseRequest {

    /**
     * 会员编号
     */
    @NotBlank(message = "会员号不可为空")
    private String memberNo;

    /**
     * 名称
     */
    private String name;
}
