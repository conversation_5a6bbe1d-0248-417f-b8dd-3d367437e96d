package com.ly.titc.pms.member.gateway.handler.log;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberPrivilegeConfigDetailResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberPrivilegeConfigResp;
import com.ly.titc.pms.member.com.annotation.LogRecordFunction;
import com.ly.titc.pms.member.com.enums.ModuleEnum;
import com.ly.titc.pms.member.com.enums.PrivilegeTypeEnum;
import com.ly.titc.pms.member.com.enums.PrivilegeClassificationEnum;
import com.ly.titc.pms.member.gateway.entity.request.member.card.ActionPrivilegeRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.card.DeletePrivilegeRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.privilege.MemberPrivilegeConfigInfoDetailRequest;
import com.ly.titc.pms.member.gateway.entity.request.member.privilege.SaveMemberPrivilegeConfigInfoRequest;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberPrivilegeDecorator;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * @Author：rui
 * @name：MemberPrivilegeLogFunctionHandler
 * @Date：2024-11-15 14:30
 * @Filename：MemberPrivilegeLogFunctionHandler
 */
@LogRecordFunction("memberPrivilege")
public class MemberPrivilegeLogFunctionHandler extends AbstractModuleServiceHandler implements ApplicationContextAware {

    private static MemberPrivilegeDecorator memberPrivilegeDecorator;

    private static HotelDecorator hotelDecorator;


    @Override
    public List<String> execute(String blocCode, String hotelCode, String name, String trackingId) {
        return null;
    }

    @Override
    public Integer getModuleId() {
        return ModuleEnum.MEMBER_PRIVILEGE_MANAGE.getCode();
    }

    @Override
    public void setApplicationContext(ApplicationContext applicationContext) throws BeansException {
        memberPrivilegeDecorator = applicationContext.getBean(MemberPrivilegeDecorator.class);
        hotelDecorator = applicationContext.getBean(HotelDecorator.class);
    }

    @LogRecordFunction("addMemberPrivilege")
    public static String addMemberPrivilege(SaveMemberPrivilegeConfigInfoRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(String.format("新增【%s】", name));
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("updateMemberPrivilege")
    public static String updateMemberPrivilege(SaveMemberPrivilegeConfigInfoRequest request) {
        MemberPrivilegeConfigResp origin =  memberPrivilegeDecorator.getMemberPrivilegeConfigDetail(Long.parseLong(request.getId()), request.getTrackingId());
        List<HotelBaseInfoResp> hotelBaseInfoRespList = hotelDecorator.listHotelBaseInfos(request.getBlocCode(), null, request.getTrackingId());
        Map<String, HotelBaseInfoResp> hotelMap = hotelBaseInfoRespList.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, item -> item));
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(String.format("修改【%s】", name));
        records.add(stringBuilder.toString());
        if (!origin.getName().equals(name)) {
            records.add(String.format("【名称】%s->%s", origin.getName(), name));
        }
        if (!origin.getType().equals(request.getType())) {
            records.add(String.format("【权益名称】%s->%s", PrivilegeClassificationEnum.getNameByType(origin.getType()), PrivilegeClassificationEnum.getNameByType(request.getType())));
        }
        if (!origin.getInstruction().equals(request.getInstruction())) {
            records.add(String.format("【权益说明】%s->%s", origin.getInstruction(), request.getInstruction()));
        }
        if (!origin.getDescription().equals(request.getDescription())) {
            records.add(String.format("【权益描述】%s->%s", origin.getDescription(), request.getDescription()));
        }
        if (!origin.getSort().equals(request.getSort())) {
            records.add(String.format("【排序值】%s->%s", origin.getSort(), request.getSort()));
        }
        if (!origin.getPic().equals(request.getPic())) {
            records.add(String.format("【权益图标】%s->%s", origin.getPic(), request.getPic()));
        }
        if (!origin.getClassification().equals(request.getClassification())) {
            records.add(String.format("【权益值】%s->%s", PrivilegeTypeEnum.getNameByType(origin.getClassification()), PrivilegeTypeEnum.getNameByType(request.getClassification())));
        }
        List<MemberPrivilegeConfigDetailResp> originalValues = origin.getScopeValues();
        // 根据 scopeType 和 value 进行分组
        Map<String, MemberPrivilegeConfigDetailResp> originalMap = originalValues.stream().collect(Collectors.toMap(item -> String.format("%s-%s-%s", item.getClassification(), item.getScopeType(), item.getValue()) , item -> item));
        List<MemberPrivilegeConfigInfoDetailRequest> detailRequests = request.getScopeValues();
        Map<String, MemberPrivilegeConfigInfoDetailRequest> currentMap = detailRequests.stream().collect(Collectors.toMap(item -> String.format("%s-%s-%s", item.getClassification(), item.getScopeType(), item.getValue()), item -> item));
        List<String> delete = new ArrayList<>();
        List<String> update = new ArrayList<>();
        List<String> add = new ArrayList<>();
        originalMap.forEach((k, v) -> {
            if (currentMap.containsKey(k)) {
                if (!new HashSet<>(v.getScopeValues()).equals(new HashSet(currentMap.get(k).getScopeValues()))) {
                    update.add(String.format("%s，%s->%s,%s",
                            v.getClassification().equals(PrivilegeClassificationEnum.DISCOUNT.getType()) ?  v.getValue()  + "折" : v.getValue() + "",
                            v.getScopeType() == 1 ? "全部酒店" : String.join("/", v.getScopeValues().stream().map(item -> hotelMap.get(item).getHotelName()).collect(Collectors.toList())),
                            currentMap.get(k).getClassification().equals(PrivilegeClassificationEnum.DISCOUNT.getType()) ?  v.getValue()  + "折" : v.getValue()+ "",
                            currentMap.get(k).getScopeType() == 1 ? "全部酒店" : String.join("/", currentMap.get(k).getScopeValues().stream().map(item -> hotelMap.get(item).getHotelName()).collect(Collectors.toList()))));
                }
            } else {
                delete.add(String.format("%s, %s",
                        v.getClassification().equals(PrivilegeClassificationEnum.DISCOUNT.getType()) ?  v.getValue()  + "折" : v.getValue() + "",
                        v.getScopeType() == 1 ? "全部酒店" : String.join("/", v.getScopeValues().stream().map(item -> hotelMap.get(item).getHotelName()).collect(Collectors.toList()))));
            }
        });
        currentMap.forEach((k, v) -> {
            if (!originalMap.containsKey(k)) {
                add.add(String.format("%s, %s",
                        v.getClassification().equals(PrivilegeClassificationEnum.DISCOUNT.getType()) ?  v.getValue()  + "折" : v.getValue() + "",
                        v.getScopeType() == 1 ? "全部酒店" : String.join("/", v.getScopeValues().stream().map(item -> hotelMap.get(item).getHotelName()).collect(Collectors.toList()))));
            }
        });
        if (CollectionUtils.isNotEmpty(update)) {
            records.add(String.format("【编辑权益内容】%s", String.join(",", update)));
        }
        if (CollectionUtils.isNotEmpty(add)) {
            records.add(String.format("【新增权益内容】%s", String.join(",", add)));
        }
        if (CollectionUtils.isNotEmpty(delete)) {
            records.add(String.format("【删除权益内容】%s", String.join(",", delete)));
        }
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("deleteMemberPrivilege")
    public static String deleteMemberPrivilege(DeletePrivilegeRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(String.format("删除【%s】", name));
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("disableMemberPrivilege")
    public static String disableMemberPrivilege(ActionPrivilegeRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(String.format("停用【%s】", name));
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

    @LogRecordFunction("enableMemberPrivilege")
    public static String enableMemberPrivilege(ActionPrivilegeRequest request) {
        String name = request.getName();
        JSONObject json = new JSONObject();
        json.put("name", name);
        json.put("blocCode", request.getBlocCode());
        List<String> records = new ArrayList<>();
        StringBuilder stringBuilder = new StringBuilder("");
        stringBuilder.append(String.format("启用【%s】", name));
        records.add(stringBuilder.toString());
        json.put("records", records);
        return json.toJSONString();
    }

}
