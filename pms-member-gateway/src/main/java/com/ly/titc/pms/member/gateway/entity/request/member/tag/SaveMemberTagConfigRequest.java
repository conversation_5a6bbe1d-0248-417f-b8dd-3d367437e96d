package com.ly.titc.pms.member.gateway.entity.request.member.tag;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：SaveMemberCardSaleRuleRequest
 * @Date：2024-11-15 10:56
 * @Filename：SaveMemberCardSaleRuleRequest
 */
@Data
public class SaveMemberTagConfigRequest extends BaseRequest {

    /**
     * 自增id
     */
    private Long id;

    /**
     * 标签名称
     */
    private String name;

    /**
     * 标签分类 0 客户标签、1客房偏好、2兴趣喜好、3餐饮喜好、4其它标签
     */
    private Integer type;

    /**
     * 打标分类 1 自动打标 2 手动达标
     */
    private Integer markType;

    /**
     * 自动删除 0 否 1 是
     */
    private Integer autoDelete;

    /**
     * 满足条件 ALL - 全部  ANY-满足任何一个条件
     */
    private String satisfyPerformType;

    /**
     * 标签描述
     */
    private String remark;

    /**
     * 统计周期 1 会员注册起 2 上次升降级起
     */
    private Integer cycleType;

    /**
     * 状态；0 无效 1 正常
     */
    private Integer state;

    /**
     * 排序
     */
    private Integer sort;

    /**
     * 规则列表
     */
    private List<MemberTagMarkRuleInfoRequest> markRuleList;
}
