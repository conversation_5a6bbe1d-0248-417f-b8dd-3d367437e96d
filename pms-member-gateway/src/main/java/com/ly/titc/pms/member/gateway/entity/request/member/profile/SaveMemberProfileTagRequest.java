package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * @Author：rui
 * @name：SaveMemberProfileTagRequest
 * @Date：2024-11-18 16:51
 * @Filename：SaveMemberProfileTagRequest
 */
@Data
public class SaveMemberProfileTagRequest extends BaseRequest {

    /**
     * 会员号
     */
    @NotBlank(message = "会员号不可为空")
    private String memberNo;

    /**
     * 标签列表
     */
    @Valid
    private List<MemberProfileTagRequest> memberProfileTagList;
}
