package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.cc.sdk.log.annotation.LogRecord;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.enums.StatusEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.QueryMemberPrivilegeConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.SaveCardLevelConfigInfoReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardPrivilegeConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberPrivilegeConfigResp;
import com.ly.titc.pms.member.com.annotation.OperationLog;
import com.ly.titc.pms.member.com.constant.ActionConstants;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.ModuleConstants;
import com.ly.titc.pms.member.com.enums.PrivilegeClassificationEnum;
import com.ly.titc.pms.member.com.enums.PrivilegeTypeEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.converter.MemberCardConverter;
import com.ly.titc.pms.member.gateway.entity.request.member.card.*;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberCardLevelConfigInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.member.MemberCardPrivilegeConfigResponse;
import com.ly.titc.pms.member.gateway.entity.response.SelectResponse;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberPrivilegeDecorator;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员卡等级
 *
 * @Author：rui
 * @name：MemberCardLevelController
 * @Date：2024-11-13 17:25
 * @Filename：MemberCardLevelController
 */
@Slf4j
@RestController
@RequestMapping("/member/card/level")
public class MemberCardLevelController {

    @Resource
    private MemberCardConverter converter;

    @Resource
    private MemberCardInfoDecorator decorator;

    @Resource
    private MemberPrivilegeDecorator privilegeDecorator;

    @Resource
    private MemberCardMedService memberCardMedService;

    /**
     * 会员等级分页查询
     *
     * @param request request
     * @return 分页结果
     */
    @PostMapping("/pageCardLevelConfig")
    public Response<Pageable<MemberCardLevelConfigInfoResponse>> pageCardLevelConfig(@RequestBody @Valid PageMemberCardLevelConfigInfoRequest request) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        Pageable<MemberCardLevelConfigInfoResp> pageable = decorator.pageCardLevelConfig(converter.convertPageMemberCardLevelConfigInfoReq(request, masterTuple.getFirst()));
        List<MemberCardLevelConfigInfoResp> list = pageable.getDatas();
        if (CollectionUtils.isEmpty(list)) {
            return Response.success(pageable);
        }
        List<MemberCardLevelConfigInfoResponse> records = converter.convertMemberCardLevelConfigInfoResponseList(list);
        return Response.success(PageableUtil.convert(pageable, records));
    }

    /**
     * 查询卡等级关联的权益--编辑等级时查询，考虑到新增权益时，不保存走不了接口查询，所以这里的两个条件过滤建议前端过滤
     *
     * @param request request
     * @return 权益列表
     */
    @PostMapping("/listCardRelatedPrivileges")
    public Response<List<MemberCardPrivilegeConfigResponse>> listCardRelatedPrivileges(@RequestBody @Valid ListCardRelatedPrivilegeRequest request) {
        List<MemberCardPrivilegeConfigResp> cardRelatedPrivileges = privilegeDecorator.listMemberCardPrivilegeLevel(Long.parseLong(request.getCardId()), request.getLevel(), request.getTrackingId());
        return Response.success(converter.convertMemberCardPrivilegeConfigResponse(cardRelatedPrivileges));
    }

    /**
     * 新增会员等级
     *
     * @param request 请求体
     * @return id
     */
    @PostMapping("/addCardLevelConfig")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_LEVEL_MANEGE_CODE, bizName = ModuleConstants.MEMBER_LEVEL_MANEGE_NAME,
            category = ActionConstants.ADD_CODE, categoryName = ActionConstants.ADD_NAME)
    @OperationLog(operateRecord = "#memberLevelConfig_addMemberCardLevel(#request)")
    public Response<Long> addCardLevelConfig(@RequestBody @Valid SaveCardLevelConfigInfoRequest request) {
        // 查询卡关联等级列表
        List<MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespList = decorator.listMemberCardLevel(Arrays.asList(request.getCardId()),
                request.getTrackingId());
        if (cardLevelConfigInfoRespList.stream().anyMatch(item -> item.getCardLevel().equals(request.getCardLevel()))) {
            throw new ServiceException(RespCodeEnum.MEMBER_10023);
        }
        // 校验是否存在价格折扣权益
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        checkPrivilege(request, masterTuple, request.getTrackingId());
        SaveCardLevelConfigInfoReq req = converter.convertSaveCardLevelConfigInfoReq(request, masterTuple.getFirst());
        return Response.success(decorator.saveCardLevelConfig(req));
    }


    public void checkPrivilege(SaveCardLevelConfigInfoRequest request, TwoTuple<Integer, String> masterTuple, String trackingId) {
        if (CollectionUtils.isEmpty(request.getRelationList())) {
            throw new ServiceException(RespCodeEnum.MEMBER_10048);
        }
        List<Long> privilegeIds = request.getRelationList().stream().map(MemberCardPrivilegeConfigInfoRequest::getPrivilegeId).collect(Collectors.toList());
        QueryMemberPrivilegeConfigReq privilegeConfigReq = new QueryMemberPrivilegeConfigReq();
        privilegeConfigReq.setMasterCode(masterTuple.getSecond());
        privilegeConfigReq.setMasterType(masterTuple.getFirst());
        privilegeConfigReq.setTrackingId(trackingId);
        List<MemberPrivilegeConfigResp> privilegeConfigRespList = privilegeDecorator.listByMasterTypeAndCode(privilegeConfigReq);
        if (CollectionUtils.isEmpty(privilegeConfigRespList)) {
            throw new ServiceException(RespCodeEnum.MEMBER_10048);
        }

        List<MemberPrivilegeConfigResp> priceRelated = privilegeConfigRespList.stream().filter(item -> privilegeIds.contains(item.getId()) &&
                item.getType().equals(PrivilegeTypeEnum.PRICE.getType())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(priceRelated)) {
            throw new ServiceException(RespCodeEnum.MEMBER_10048);
        }
        if (!priceRelated.stream().anyMatch(item -> item.getClassification().equals(PrivilegeClassificationEnum.DISCOUNT.getType()))) {
            throw new ServiceException(RespCodeEnum.MEMBER_10048);
        }
    }

    /**
     * 修改会员等级
     *
     * @param request 请求体
     * @return id
     */
    @PostMapping("/updateCardLevelConfig")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_LEVEL_MANEGE_CODE, bizName = ModuleConstants.MEMBER_LEVEL_MANEGE_NAME,
            category = ActionConstants.SAVE_CODE, categoryName = ActionConstants.SAVE_NAME)
    @OperationLog(operateRecord = "#memberLevelConfig_updateMemberCardLevel(#request)")
    public Response<Long> updateCardLevelConfig(@RequestBody @Valid SaveCardLevelConfigInfoRequest request) {
        // 查询卡关联等级列表
        List<MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespList = decorator.listMemberCardLevel(Arrays.asList(request.getCardId()),
                request.getTrackingId());
        if (cardLevelConfigInfoRespList.stream().filter(item -> !item.getId().equals(Long.parseLong(request.getId()))).anyMatch(item -> item.getCardLevel().equals(request.getCardLevel()))) {
            throw new ServiceException(RespCodeEnum.MEMBER_10023);
        }
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        checkPrivilege(request, masterTuple, request.getTrackingId());
        SaveCardLevelConfigInfoReq req = converter.convertSaveCardLevelConfigInfoReq(request, masterTuple.getFirst());
        return Response.success(decorator.saveCardLevelConfig(req));
    }


    /**
     * 删除会员卡等级
     *
     * @param request request
     * @return 结果
     */
    @PostMapping("/deleteCardLevelConfig")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_LEVEL_MANEGE_CODE, bizName = ModuleConstants.MEMBER_LEVEL_MANEGE_NAME,
            category = ActionConstants.DELETE_CODE, categoryName = ActionConstants.DELETE_NAME)
    @OperationLog(operateRecord = "#memberLevelConfig_deleteMemberCardLevel(#request)")
    public Response deleteCardLevelConfig(@RequestBody @Valid DeleteMemberCardLevelConfigRequest request) {
        decorator.deleteCardLevelConfig(converter.convertDeleteMemberCardLevelConfigReq(request));
        return Response.success();
    }

    /**
     * 启用会员卡等级
     *
     * @param request request
     */
    @PostMapping("/enableCardLevel")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_LEVEL_MANEGE_CODE, bizName = ModuleConstants.MEMBER_LEVEL_MANEGE_NAME,
            category = ActionConstants.START_CODE, categoryName = ActionConstants.START_NAME)
    @OperationLog(operateRecord = "#memberLevelConfig_enableMemberCardLevel(#request)")
    public Response enableCardLevel(@RequestBody @Valid ActionCardLevelRequest request) {
        decorator.actionCardLevel(converter.convertActionBaseReq(request, StatusEnum.VALID.getStatus()));
        return Response.success();
    }

    /**
     * 停用会员卡等级
     *
     * @param request request
     */
    @PostMapping("/stopCardLevel")
    @LogRecord(tenant = CommonConstant.PROJECT_CODE, bizCode = ModuleConstants.MEMBER_LEVEL_MANEGE_CODE, bizName = ModuleConstants.MEMBER_LEVEL_MANEGE_NAME,
            category = ActionConstants.STOP_CODE, categoryName = ActionConstants.STOP_NAME)
    @OperationLog(operateRecord = "#memberLevelConfig_stopMemberCardLevel(#request)")
    public Response stopCardLevel(@RequestBody @Valid ActionCardLevelRequest request) {
        // 获取卡code
        Long cardId = Long.parseLong(request.getCardId());
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        if (memberCardMedService.checkHasCard(masterTuple.getFirst(), request.getBlocCode(), cardId, request.getCardLevel())) {
            throw new ServiceException(RespCodeEnum.MEMBER_10021);
        }
        decorator.actionCardLevel(converter.convertActionBaseReq(request, StatusEnum.INVALID.getStatus()));
        return Response.success();
    }

    /**
     * 会员等级关联的等级下拉框
     *
     * @param request request
     */
    @PostMapping("/selectCardLevel")
    public Response<List<SelectResponse>> selectCardLevel(@RequestBody @Valid SelectCardLevelRequest request) {
        // 查询卡关联等级列表
        List<MemberCardLevelConfigInfoResp> cardLevelConfigInfoRespList = decorator.listMemberCardLevel(Arrays.asList(Long.parseLong(request.getCardId())), request.getTrackingId());
        return Response.success(converter.convertSelectResponse(cardLevelConfigInfoRespList));
    }
}
