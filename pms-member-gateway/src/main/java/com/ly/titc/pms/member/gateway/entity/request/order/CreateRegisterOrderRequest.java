package com.ly.titc.pms.member.gateway.entity.request.order;

import com.ly.titc.pms.member.gateway.entity.request.member.MemberRegisterRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.Valid;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 13:49
 */
@Data
@Accessors(chain = true)
public class CreateRegisterOrderRequest extends CreateOrderBaseRequest {

    /**
     * 会员注册信息
     */
    @Valid
    private MemberRegisterRequest registerRequest;

}
