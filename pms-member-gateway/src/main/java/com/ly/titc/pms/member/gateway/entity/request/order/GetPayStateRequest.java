package com.ly.titc.pms.member.gateway.entity.request.order;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-28 18:59
 */
@Data
@Accessors(chain = true)
public class GetPayStateRequest extends BaseRequest {
    /**
     * 会员订单号
     */
    @NotEmpty(message = "会员订单号不能为空")
    private String memberOrderNo;

    /**
     * 会员支付申请单号
     */
    @NotEmpty(message = "会员支付申请单号不能为空")
    private String memberOrderPayTradeNo;
}
