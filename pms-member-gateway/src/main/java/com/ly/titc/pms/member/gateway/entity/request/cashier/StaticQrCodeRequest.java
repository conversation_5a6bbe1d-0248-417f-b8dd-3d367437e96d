package com.ly.titc.pms.member.gateway.entity.request.cashier;

import com.ly.titc.cashier.dubbo.enums.QRCodeTypeEnum;
import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-9-10 11:49
 */
@Data
@Accessors(chain = true)
public class StaticQrCodeRequest extends BaseRequest {
    /**
     * 静态码 类型 pay 支付  refund 退款  prePayAuth 预授权操作
     */
    @NotEmpty(message = "静态码类型不能为空")
    @LegalEnum(methodName = "getCode",message = "静态码类型", target = QRCodeTypeEnum.class)

    private String qrCodeType;


    /**
     * 交易操作号
     */
    @NotEmpty(message = "交易操作号不能为空")
    private String tradeOperatorNo;



}
