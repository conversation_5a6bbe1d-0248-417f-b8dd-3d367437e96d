package com.ly.titc.pms.member.gateway.controller.spm.giftpack;

import cn.hutool.core.collection.CollUtil;
import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSON;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.dubbo.enums.IdTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.gateway.converter.GiftPackConverter;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.GetGrantReceiverRequest;
import com.ly.titc.pms.member.gateway.entity.request.spm.giftpack.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.CouponReceiverInfoResponse;
import com.ly.titc.pms.member.gateway.entity.response.spm.giftpack.*;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.giftpack.GiftPackDecorator;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.GrantGiftPackBatchReq;
import com.ly.titc.pms.spm.dubbo.entity.request.giftpack.*;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.*;
import com.ly.titc.pms.spm.dubbo.enums.SourceClientEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 礼包接口
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-28
 */
@Slf4j
@RestController
@RequestMapping("/giftPack/")
public class GiftPackController {
    @Resource(type = GiftPackDecorator.class)
    private GiftPackDecorator giftPackDecorator;
    @Resource(type = GiftPackConverter.class)
    private GiftPackConverter giftPackConverter;
    @Resource(type = MemberMedService.class)
    private MemberMedService memberMedService;


    /**
     * 查询某业务编号触发的礼包奖励详情列表
     */
    @PostMapping("/queryBizGrantDetails")
    public Response<List<GrantGiftPackDetailResponse>> queryBizGrantDetails(@Valid @RequestBody QueryGiftPackGrantDetailsRequest request){
        QueryGiftPackGrantDetailsReq req = giftPackConverter.convertQueryGrantDetailsReq(request);
        req.setGiftPackNos(request.getGiftPackNos());
        List<GiftPackBatchDetailListResp> grantGiftPackDetails = giftPackDecorator.queryBizGrantDetails(req);

        List<GrantGiftPackDetailResponse> grantGiftPackDetailResponses = giftPackConverter.convertGrantGiftPackDetailResponses(grantGiftPackDetails);
        //TODO 对于积分、
        return Response.success(grantGiftPackDetailResponses);
    }


    /**
     * 保存礼包信息
     */
    @PostMapping("/save")
    public Response<String> saveGiftPackInfo(@Valid @RequestBody SaveGiftPackInfoRequest request){
        SaveGiftPackInfoReq req = giftPackConverter.convertSaveGiftPackInfoReq(request,UserInfoThreadHolder.getUser());
        return Response.success(giftPackDecorator.saveGiftPackInfo(req));
    }

    /**
     * 礼包列表
     */
    @PostMapping("/page/list")
    public Response<Pageable<GiftPackListResponse>> pageGiftPackInfoList(@RequestBody GetGiftPackListRequest request){
        UserInfoDto user = UserInfoThreadHolder.getUser();
        GetGiftPackListReq req = giftPackConverter.convertGetGiftPackListReq(request, user.getBlocCode());
        Pageable<GiftPackListResp> pageable = giftPackDecorator.pageGiftPackInfoList(req);
        return Response.success(Pageable.convert(pageable,giftPackConverter.convertGiftPackListResponses(pageable.getDatas())));
    }
    /**
     * 根据 会员编号、手机号、身份证号 查询会员信息的接口
     * @param request
     * @return
     */
    @PostMapping("/getGrantUserInfo")
    public Response<CouponReceiverInfoResponse> getGrantUserInfo(@Valid @RequestBody GetGrantReceiverRequest request){
        String blocCode = UserThreadHolder.getUser().getBlocCode();
        CouponReceiverInfoResponse infoResponse = new CouponReceiverInfoResponse();
        String idCard = request.getIdCard();
        String mobile = request.getMobile();
        String memberCardNo = request.getMemberCardNo();
        if(StringUtils.isNotBlank(idCard)){
            List<MemberDetailDto> memberDetails = memberMedService.listByIdNos(
                    MasterTypeEnum.BLOC.getType(),blocCode,IdTypeEnum.IDENTITY_CARD.getType(),
                    Collections.singletonList(idCard),null);
            MemberDetailDto memberDetailDto = CollUtil.getFirst(memberDetails.listIterator());
            infoResponse.setCustomerName(memberDetailDto.getRealName());
            infoResponse.setMobile(memberDetailDto.getMobile());
            infoResponse.setMemberCardNo(memberDetailDto.getMemberNo());
        }else if(StringUtils.isNotBlank(memberCardNo)){
            MemberInfoDto memberInfoDto = memberMedService.getByMemberNo(null,null,memberCardNo);
            if(Objects.isNull(memberInfoDto)){
                throw new ServiceException(RespCodeEnum.CODE_500.getCode(),"会员信息不存在");
            }
            infoResponse.setCustomerName(memberInfoDto.getRealName());
            infoResponse.setMobile(memberInfoDto.getMobile());
            infoResponse.setMemberCardNo(memberInfoDto.getMemberNo());
        }else if(StringUtils.isNotBlank(mobile)){
            List<MemberDetailDto> memberDetails = memberMedService.listByMobiles(
                    MasterTypeEnum.BLOC.getType(),blocCode,Collections.singletonList(mobile),null);
            MemberDetailDto memberDetailDto = CollUtil.getFirst(memberDetails.listIterator());
            infoResponse.setCustomerName(memberDetailDto.getRealName());
            infoResponse.setMobile(memberDetailDto.getMobile());
            infoResponse.setMemberCardNo(memberDetailDto.getMemberNo());
        }
        return Response.success(infoResponse);
    }

    /**
     * 优惠礼包发放(单个发放)
     */
    @PostMapping("/grantGiftPackByBind")
    public Response<Void> grantGiftPackByBind(@Valid @RequestBody GrantGiftPackRequest req){
        UserInfoDto user = UserInfoThreadHolder.getUser();
        GrantGiftPackBatchReq request = giftPackConverter.convertGrantGiftPackBatchReq(req,user);
        giftPackDecorator.grantGiftPack(request);
        return Response.success();
    }


    /**
     * 优惠礼包发放(批量发放)
     * @param masterType    礼包所属主体类型
     * @param masterCode    礼包所属主体code
     * @param giftPackNo    礼包编号
     * @param giftPackName  礼包名
     * @param version       礼包版本
     * @param grantType     发放类型
     * @param grantNum      发放数量
     * @param grantEvent      发放事件
     * @param file           导入文件
     */
    @SneakyThrows
    @PostMapping("/grantGiftPackByImport")
    public Response<Void> grantGiftPackByImport(@RequestParam(value = "masterType") Integer masterType,
                                              @RequestParam(value = "masterCode")String masterCode,
                                              @RequestParam(value = "giftPackNo")String giftPackNo,
                                              @RequestParam(value = "giftPackName")String giftPackName,
                                              @RequestParam(value = "version")Integer version,
                                              @RequestParam(value = "grantType")Integer grantType,
                                              @RequestParam(value = "grantNum")Integer grantNum,
                                              @RequestParam(value = "grantEvent")Integer grantEvent,
                                              @RequestParam("file") MultipartFile file){
        final int BATCH_COUNT = 2000;
        GrantGiftPackRequest grantGiftPackRequest = new GrantGiftPackRequest()
                .setMasterCode(masterCode)
                .setMasterType(masterType)
                .setGiftPackName(giftPackName)
                .setVersion(version)
                .setSaleType(1)
                .setGiftPackNo(giftPackNo)
                .setGrantNum(grantNum)
                .setGrantEvent(grantEvent)
                .setGrantType(grantType);
        List<MembersForGrantCouponResponse> members = Lists.newArrayList();
        EasyExcel.read(file.getInputStream(), MembersForGrantCouponResponse.class, new AnalysisEventListener<MembersForGrantCouponResponse>() {
            @Override
            public void invoke(MembersForGrantCouponResponse data, AnalysisContext analysisContext) {
                log.info("解析到一条数据:{}", JSON.toJSONString(data));
                members.add(data);
                if (members.size() >= BATCH_COUNT) {
                    GrantGiftPackBatchReq grantGiftPackBatchReq =
                            giftPackConverter.convertGrantGiftPackBatchReq(grantGiftPackRequest, UserInfoThreadHolder.getUser());
                    grantGiftPackBatchReq.setReceiverCodes(getMemberNos(members));
                    giftPackDecorator.grantGiftPack(grantGiftPackBatchReq);
                }
            }
            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                // 这里也要保存数据，确保最后遗留的数据也存储到数据库
                log.info("所有数据解析完成！");
            }
        }).sheet().doRead();
        return Response.success();
    }

    private List<String> getMemberNos(List<MembersForGrantCouponResponse> members) {
        String blocCode = UserThreadHolder.getUser().getBlocCode();
        List<String> mobiles = members.stream()
                .filter(e -> StringUtils.isBlank(e.getMemberNo()) && StringUtils.isNotBlank(e.getMobile()))
                .map(MembersForGrantCouponResponse::getMobile).collect(Collectors.toList());
        List<String> memberNos = members.stream().filter(e -> StringUtils.isNotBlank(e.getMemberNo()))
                .map(MembersForGrantCouponResponse::getMemberNo).collect(Collectors.toList());
        List<MemberDetailDto> memberDetails = memberMedService.listByMobiles(MasterTypeEnum.BLOC.getType(),blocCode,mobiles,null);
        List<String> memberNosOfMobile = memberDetails.stream().map(MemberDetailDto::getMemberNo).collect(Collectors.toList());
        memberNos.addAll(memberNosOfMobile);
        return memberNos;
    }


    /**
     * 礼包发放批次分页列表查询
     */
    @PostMapping("/grantBatchPageInfos")
    public Response<Pageable<GiftPackBatchListResponse>> grantBatchPageInfos(@Valid @RequestBody PageGiftPackBatchRequest req){
        QueryGiftPackBatchListReq queryGiftPackBatchListReq = giftPackConverter.convertPageBatchRequest2ApiReq(req);
        String blocCode = UserThreadHolder.getUser().getBlocCode();
        queryGiftPackBatchListReq.setBlocCode(blocCode);
        queryGiftPackBatchListReq.setSourceClient(SourceClientEnum.CRM.getCode());
        if(StringUtils.isNotBlank(req.getMobile())){
            List<MemberDetailDto> memberDetails = memberMedService.listByMobiles(
                    MasterTypeEnum.BLOC.getType(),blocCode,Collections.singletonList(req.getMobile()),null);
            String memberNo = CollUtil.getFirst(memberDetails.listIterator()).getMemberNo();
            queryGiftPackBatchListReq.setMemberNo(memberNo);
        }
        Pageable<GiftPackBatchListResp> pageable = giftPackDecorator.batchPageInfos(queryGiftPackBatchListReq);
        return Response.success(PageableUtil.convert(pageable, d->giftPackConverter.convertGiftPackBatchListResp2Response(d)));
    }


    /**
     * 售卖详情
     */
    @PostMapping("/saleInfo")
    public Response<GiftPackGrantSaleInfoResponse> saleInfo(@Valid @RequestBody QueryGiftGrantBatchInfoRequest request){
        QueryGiftGrantBatchInfoReq req = giftPackConverter.convertGiftPackSaleInfoRequest2ApiReq(request);
        GiftPackSaleInfoResp saleInfo = giftPackDecorator.getSaleInfo(req);
        GiftPackGrantSaleInfoResponse response = giftPackConverter.convertGiftPackSaleRespToResponse(saleInfo);
        MemberInfoDto memberInfoDto = memberMedService.getByMemberNo(
                MasterTypeEnum.BLOC.getType(),UserThreadHolder.getUser().getBlocCode(),saleInfo.getReceiverCode());
        if(Objects.nonNull(memberInfoDto)){
            response.setMemberName(memberInfoDto.getRealName());
            response.setMemberNo(memberInfoDto.getMemberNo());
            response.setMobile(memberInfoDto.getMobile());
        }
        return Response.success(response);
    }


    /**
     * 礼包发放详情分页列表
     */
    @PostMapping("/batchDetailPageInfos")
    public Response<Pageable<GiftPackBatchDetailListResponse>> batchDetailPageInfos(@Valid @RequestBody PageGiftPackBatchDetailRequest request){
        UserInfoDto user = UserInfoThreadHolder.getUser();
        QueryGiftPackBatchDetailListReq req = giftPackConverter.convertPageBatchDetailRequest2ApiReq(request);
        req.setBlocCode(user.getBlocCode());
        Pageable<GiftPackBatchDetailListResp> pageable = giftPackDecorator.batchDetailPageInfos(req);
        return Response.success(buildGiftPackBatchDetailListResponse(pageable));
    }

    /**
     * 礼包发放详情列表导出
     * 导出手机号不脱敏
     */
    @SneakyThrows
    @PostMapping("/exportBatchDetails")
    public Response<Void> exportBatchDetails(@Valid @RequestBody PageGiftPackBatchDetailRequest req, HttpServletResponse response){
         response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
         response.setCharacterEncoding("utf-8");
        UserInfoDto user = UserInfoThreadHolder.getUser();
        String blocCode = user.getBlocCode();
        QueryGiftGrantBatchInfoRequest batchInfoRequest = new QueryGiftGrantBatchInfoRequest();
        batchInfoRequest.setGrantBatchNo(req.getGrantBatchNo());
        batchInfoRequest.setBlocCode(blocCode);
        QueryGiftGrantBatchInfoReq queryGiftGrantBatchInfoReq =
                giftPackConverter.convertGiftPackSaleInfoRequest2ApiReq(batchInfoRequest);
        GiftPackBatchInfoResp giftPackBatchInfo = giftPackDecorator.getGiftPackBatchInfo(queryGiftGrantBatchInfoReq);
        GiftPackBatchInfoResponse giftPackBatchInfoResponse =
                giftPackConverter.covertGiftPackBatchInfoResp2Response(giftPackBatchInfo);
        QueryGiftPackBatchDetailListReq queryGiftPackBatchDetailListReq =
                giftPackConverter.convertPageBatchDetailRequest2ApiReq(req);
        queryGiftPackBatchDetailListReq.setBlocCode(blocCode);
        List<GiftPackBatchDetailListResp> detailListResps = giftPackDecorator.batchDetails(queryGiftPackBatchDetailListReq);
        String giftPackName = giftPackBatchInfoResponse.getGiftPackName();
        String grantDate = giftPackBatchInfoResponse.getGrantDate();
        String fileName = URLEncoder.encode(giftPackName + grantDate +"发放详情", "UTF-8").replaceAll("\\+", "%20");
         response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
         EasyExcel.write(response.getOutputStream(), GiftPackBatchDetailExportResponse.class)
                 .sheet("礼包发放")
                 .doWrite(buildGiftPackBatchDetailExportResponse(detailListResps));
         return Response.success();
    }


    /**
     * 礼包批次作废
     */
    @PostMapping("/invalidBatch")
    public Response<Void> invalidBatch(@Valid @RequestBody QueryGiftGrantBatchInfoRequest request){
        QueryGiftGrantBatchInfoReq req = giftPackConverter.convertGiftPackSaleInfoRequest2ApiReq(request);
        req.setOperator(UserInfoThreadHolder.getUser().getUserName());
        giftPackDecorator.invalidBatch(req);
        return Response.success();
    }

    /**
     * 礼包批次删除
     */
    @PostMapping("/deleteBatch")
    public Response<Void> deleteBatch(@Valid @RequestBody QueryGiftGrantBatchInfoRequest request){
        QueryGiftGrantBatchInfoReq req = giftPackConverter.convertGiftPackSaleInfoRequest2ApiReq(request);
        req.setOperator(UserInfoThreadHolder.getUser().getUserName());
        giftPackDecorator.deleteBatch(req);
        return Response.success();
    }

    /**
     * 启用/停用
     */
    @PostMapping("/modify/state")
    public Response<String> enableAndDisableGiftPackInfo(@Valid @RequestBody EnableAndDisableGiftPackRequest request){
        UserInfoDto user = UserInfoThreadHolder.getUser();
        EnableAndDisableGiftPackReq req = giftPackConverter.convertEnableAndDisableGiftPackReq(request,user);
        giftPackDecorator.enableAndDisableGiftPackInfo(req);
        return Response.success();
    }

    /**
     * 礼包详情
     */
    @PostMapping("/info")
    public Response<GiftPackInfoResponse> getGiftPackInfo(@Valid @RequestBody GetGiftPackInfoRequest request){
        GetGiftPackInfoReq req = giftPackConverter.convertGetGiftPackInfoReq(request, UserInfoThreadHolder.getUser());
        GiftPackInfoResp resp = giftPackDecorator.getGiftPackInfo(req);
        return Response.success(giftPackConverter.buildGiftPackInfoResponse(resp));
    }

    /**
     * 礼包删除
     */
    @PostMapping("/delete")
    public Response<String> delete(@Valid @RequestBody DeleteGiftPackRequest request){
        DeleteGiftPackReq req = giftPackConverter.convertDeleteGiftPackReq(request,UserInfoThreadHolder.getUser());
        giftPackDecorator.delete(req);
        return Response.success();
    }

    private List<GiftPackBatchDetailExportResponse> buildGiftPackBatchDetailExportResponse(List<GiftPackBatchDetailListResp> detailListResps ){
        return detailListResps.stream().map(e->{
            GiftPackBatchDetailExportResponse exportData = giftPackConverter.convertGiftPackBatchDetailListResp2Export(e);
            exportData.setMemberNo(e.getReceiverCode());
            MemberInfoDto memberInfoDto = memberMedService.getByMemberNo(
                    MasterTypeEnum.BLOC.getType(),UserThreadHolder.getUser().getBlocCode(),e.getReceiverCode()
            );
            if(Objects.nonNull(memberInfoDto)){
                exportData.setMobile(memberInfoDto.getMobile());
                exportData.setMemberName(memberInfoDto.getRealName());
            }
            return exportData;
        }).collect(Collectors.toList());
    }

    private Pageable<GiftPackBatchDetailListResponse> buildGiftPackBatchDetailListResponse(Pageable<GiftPackBatchDetailListResp> pageable){
        return PageableUtil.convert(pageable, d->{
            GiftPackBatchDetailListResponse giftPackBatchDetailListResponse = giftPackConverter.convertGiftPackBatchDetailListResp2Response(d);
            MemberInfoDto memberInfoDto = memberMedService.getByMemberNo(
                    MasterTypeEnum.BLOC.getType(),UserThreadHolder.getUser().getBlocCode(),d.getReceiverCode()
            );
            if(Objects.nonNull(memberInfoDto)){
                giftPackBatchDetailListResponse.setMobile(memberInfoDto.getMobile());
                giftPackBatchDetailListResponse.setMemberNo(memberInfoDto.getMemberNo());
                giftPackBatchDetailListResponse.setMemberName(memberInfoDto.getRealName());
            }
            return giftPackBatchDetailListResponse;
        });
    }
}
