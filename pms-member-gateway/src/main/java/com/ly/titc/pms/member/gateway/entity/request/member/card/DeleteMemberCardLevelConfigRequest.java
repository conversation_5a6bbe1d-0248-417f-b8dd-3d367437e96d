package com.ly.titc.pms.member.gateway.entity.request.member.card;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：DeleteMemberCardLevelConfigRequest
 * @Date：2024-11-12 17:48
 * @Filename：DeleteMemberCardLevelConfigRequest
 */
@Data
public class DeleteMemberCardLevelConfigRequest extends BaseRequest {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private String id;

    /**
     * 卡等级名称
     */
    @NotBlank(message = "卡等级名称不能为空")
    private String cardLevelName;

    /**
     * 卡id
     */
    @NotBlank(message = "卡id不能为空")
    private String cardId;

    /**
     * 卡等级
     */
    @NotNull(message = "卡等级不能为空")
    private Integer cardLevel;
}
