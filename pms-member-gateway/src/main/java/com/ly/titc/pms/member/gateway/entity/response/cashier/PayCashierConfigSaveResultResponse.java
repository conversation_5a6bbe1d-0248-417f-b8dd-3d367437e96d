package com.ly.titc.pms.member.gateway.entity.response.cashier;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-29 14:54
 */
@Data
@Accessors(chain = true)
public class PayCashierConfigSaveResultResponse {
    /**
     * 配置主键ID
     */
    private Long configId;
    /**
     * 主体（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 主体code
     */
    private String masterCode;

    /**
     * 收银产品 PMSPAY(PMS相关收银业务),WXBOOKINGJSPAY(微订房公众号支付),WXBOOKINGAPPLETPAY(微订房小程序支付)
     */
    private String cashierProductCode;
    /**
     * 收银场景 MEMBER_REGISTER(会员注册),MEMBER_PURCHASE_CARD(会员购卡),MEMBER_RECHARGE(会员充值).....
     *
     */
    private String cashierScene;

    /**
     * 收银来源 PUB(集团组) BLOC (集团)，HOTEL(门店)
     */
    private String cashierSource;

    /**
     * 收银门店范围对应门店
     */
    private String hotelCode;

}
