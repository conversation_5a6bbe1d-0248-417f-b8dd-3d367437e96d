package com.ly.titc.pms.member.gateway.entity.request.spm.coupon;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;

/**
 * @author: shawn
 * @email: <EMAIL>
 * @date: 2024/11/21 09:27
 * @description:
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class GetCouponRequest extends BaseRequest {


    /**
     * 券码
     */
    @NotBlank(message = "券码不能为空")
    private String couponCode;


}
