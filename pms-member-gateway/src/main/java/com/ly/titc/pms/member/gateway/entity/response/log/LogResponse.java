package com.ly.titc.pms.member.gateway.entity.response.log;

import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * @Author：rui
 * @name：LogResponse
 * @Date：2023-11-21 17:28
 * @Filename：LogResponse
 */
@Data
@Accessors(chain = true)
public class LogResponse {

    /**
     * 操作模块名称
     */
    private String moduleName;

    /**
     * 操作对象
     */
    private String name;

    /**
     * 行为名称
     */
    private String actionName;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private String gmtCreate;

    /**
     * ip地址
     */
    private String ip;

    /**
     * 记录列表
     */
    private List<String> records;

    private String trackingId;
}
