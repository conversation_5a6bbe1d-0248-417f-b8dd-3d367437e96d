package com.ly.titc.pms.member.gateway.entity.request.member.card;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * @Author：rui
 * @name：ActionBaselRequest
 * @Date：2024-11-12 17:48
 * @Filename：ActionBaselRequest
 */
@Data
public class ActionBaseRequest extends BaseRequest {

    /**
     * id
     */
    @NotNull(message = "id不能为空")
    private String id;
}
