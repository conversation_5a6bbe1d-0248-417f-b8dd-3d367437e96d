package com.ly.titc.pms.member.gateway.entity.request.member.profile;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Author：rui
 * @name：ListCommonAddressRequest
 * @Date：2024-11-18 14:54
 * @Filename：ListCommonAddressRequest
 */
@Data
public class ListCommonAddressRequest extends BaseRequest {

    /**
     * 会员编号
     */
    @NotBlank(message = "会员号不可为空")
    private String memberNo;

    /**
     * 姓名
     */
    private String name;
}
