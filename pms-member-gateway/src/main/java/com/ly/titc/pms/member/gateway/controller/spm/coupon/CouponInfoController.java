package com.ly.titc.pms.member.gateway.controller.spm.coupon;


import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson2.JSON;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.request.hotel.PageHotelsReq;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.oauth.client.entity.dto.UserDto;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.member.gateway.converter.CouponConverter;
import com.ly.titc.pms.member.gateway.entity.request.spm.coupon.*;
import com.ly.titc.pms.member.gateway.entity.response.spm.coupon.*;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.coupon.CouponDecorator;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.*;
import com.ly.titc.pms.spm.dubbo.enums.GrantEventEnum;
import com.ly.titc.pms.spm.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.SourceClientEnum;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.net.URLEncoder;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

/**
 * 优惠券发放及发放记录管理
 * @Description 优惠券发放及发放记录管理
 * <AUTHOR>
 * @Date 2024-11-4 14:25
 */
@Slf4j
@RestController
@RequestMapping("/coupon/info")
public class CouponInfoController {
    @Resource
    private HotelDecorator hotelDecorator;
    @Resource
    private CouponDecorator couponDecorator;
    @Resource
    private CouponConverter couponConverter;

    /**
     * 券适用门店分页列表
     * @param request
     * @return
     */
    @PostMapping("/couponApplicableHotels")
    public Response<Pageable<CouponApplicableHotelResponse>> couponApplicableHotels(@Valid @RequestBody PageCouponApplicableHotelsRequest request){
        String couponCode = request.getCouponCode();
        String blocCode = UserInfoThreadHolder.getUser().getBlocCode();
        List<String> hotelCodes = couponDecorator.couponApplicableHotels(couponCode, blocCode);
        PageHotelsReq pageHotelsReq = couponConverter.convertPageHotelsReq(request,hotelCodes,blocCode);
        Pageable<PageHotelsResp> pageHotelsRespPageable = hotelDecorator.pageHotels(pageHotelsReq);
        return Response.success(couponConverter.convertPageableCouponApplicableHotelResponse(pageHotelsRespPageable));
    }


    /**
     * 根据 会员卡号、客户编号、手机号、身份证号 查询身份信息的接口
     * @param request
     * @return
     */
    @PostMapping("/getGrantUserInfo")
    public Response<List<CouponReceiverInfoResponse>> getGrantUserInfo(@Valid @RequestBody GetGrantReceiverRequest request){
        request.getCustomerNo();
        request.getHotelCode();
        request.getIdCard();
        request.getMobile();
        request.getMemberCardNo();
        //TODO
        return Response.success();
    }

    /**
     * 优惠券发放明细分页列表查询
     * @param req
     * @return
     */
    @PostMapping("/pageInfos")
    public Response<Pageable<CouponListResponse>> pageInfos(@Valid @RequestBody PageCouponRequest req){
        String blocCode = UserInfoThreadHolder.getUser().getBlocCode();
        QueryCouponListReq queryCouponListReq = couponConverter.convertPageCouponRequest2ApiReq(req);
        queryCouponListReq.setBlocCode(blocCode);
        Pageable<CouponListResp> pageable = couponDecorator.pageInfosForBloc(queryCouponListReq);
        return Response.success(couponConverter.convertPageableCouponListResponse(pageable));
    }

    /**
     * 获取某人各状态优惠券统计信息
     */
    @PostMapping("/getCouponStatistics")
    public Response<CouponStatisticsResponse> getCouponStatistics(@Valid @RequestBody CouponStatisticsRequest req){
        String blocCode = UserInfoThreadHolder.getUser().getBlocCode();
        QueryCouponStatisticsReq apiReq = couponConverter.convertCouponStatisticsRequest2ApiReq(req);
        apiReq.setBlocCode(blocCode);
        CouponStatisticsResp apiResp = couponDecorator.getCouponStatistics(apiReq);
       return Response.success(couponConverter.convertCouponStatisticsResponse(apiResp));
    }



    /**
     * 获取券的核销信息
     * @param request
     * @return
     */
    @PostMapping("/getRedeemInfo")
    public Response<CouponRedeemInfoResponse> getCouponRedeemInfo(@Valid @RequestBody GetCouponRequest request){
        request.setBlocCode(request.getBlocCode());
        //一般只有券是在这个酒店核销的才能看到核销信息
        GetCouponInfoReq apiReq = couponConverter.convertGetCouponRequest2GetRedeemReq(request);
        CouponRedeemInfoResp apiResp = couponDecorator.getCouponRedeemInfo(apiReq);
        if (apiResp == null) {
            return Response.success();
        }
        return Response.success(couponConverter.convertCouponRedeemInfoResp2Response(apiResp));
    }

    /**
     * 优惠券分页列表导出
     * @param req
     * @return
     */
    @SneakyThrows
    @PostMapping("/export")
    public Response<Void> export(@Valid @RequestBody PageCouponRequest req, HttpServletResponse response){
        String blocCode = UserInfoThreadHolder.getUser().getBlocCode();
        QueryCouponListReq queryCouponListReq = couponConverter.convertPageCouponRequest2ApiReq(req);
        queryCouponListReq.setBlocCode(blocCode);
        List<CouponListResp> couponListRespList = couponDecorator.queryBlocCouponList(queryCouponListReq);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("优惠券查询", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), CouponExportResponse.class)
                .sheet("优惠券")
                .doWrite(couponConverter.convertCouponExportResponse(couponListRespList));
        return Response.success();
    }



    /**
     * 发放优惠券(批量发放)
     * @param templateCode 发放券模板code
     * @param version       模板version
     * @param grantNum      每人发放数
     * @param crossShop     是否跨店
     * @param hotelCode     门店生成时门店code
     * @param toSmsNotify    是否短信通知
     * @param file           导入文件
     */
    @SneakyThrows
    @PostMapping("/grantCouponByImport")
    public Response<Void> grantCouponByImport(@RequestParam(value = "templateCode") String templateCode,
                                              @RequestParam(value = "version")Integer version,
                                              @RequestParam(value = "grantNum")Integer grantNum,
                                              @RequestParam(value = "crossShop")Integer crossShop,
                                              @RequestParam(value = "hotelCode",required = false)String hotelCode,
                                              @RequestParam(value = "toSmsNotify", required = false) Boolean toSmsNotify,
                                              @RequestParam("file") MultipartFile file){
        final int BATCH_COUNT = 2000;

        List<MembersForGrantCoupon> members = Lists.newArrayList();
        EasyExcel.read(file.getInputStream(), MembersForGrantCoupon.class, new AnalysisEventListener<MembersForGrantCoupon>() {
            @Override
            public void invoke(MembersForGrantCoupon data, AnalysisContext analysisContext) {
                log.info("解析到一条数据:{}", JSON.toJSONString(data));
                members.add(data);
                if (members.size() >= BATCH_COUNT) {
                    //TODO invoke spm 发放优惠券
                    //TODO 根据手机号查询会员卡号
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                // 这里也要保存数据，确保最后遗留的数据也存储到数据库
                //TODO invoke spm 发放优惠券
                log.info("所有数据解析完成！");
            }
        }).sheet().doRead();

        return Response.success();
    }

    /**
     * 发放优惠券(单个发放)
     */
    @PostMapping("/grantCouponByBind")
    public Response<Void> grantCouponByBind(@Valid @RequestBody GrantCouponRequest req){
        String blocCode = UserThreadHolder.getUser().getBlocCode();
        Boolean toSmsNotify = req.getToSmsNotify();

        GrantCouponBatchReq grantCouponBatchReq = couponConverter.convertGrantCouponRequest2ApiReq(req);
        grantCouponBatchReq.setBlocCode(blocCode);
        grantCouponBatchReq.setHotelCode(StringUtils.isBlank(req.getHotelCode())?"":req.getHotelCode());
        grantCouponBatchReq.setGrantEvent(GrantEventEnum.MANUAL_GRANT.getEvent());
        grantCouponBatchReq.setSourceClient(SourceClientEnum.CRM.getCode());
        grantCouponBatchReq.setIsAllowCrossStore(0);
        if(Objects.nonNull(toSmsNotify)){
            grantCouponBatchReq.setIsSmsNotification(toSmsNotify?1:0);
        }else{
            grantCouponBatchReq.setIsSmsNotification(0);
        }
        grantCouponBatchReq.setReceiverCodes(Arrays.asList(req.getReceiverCode()));
        couponDecorator.grantCouponsByBind(grantCouponBatchReq);
        return Response.success();
    }


    /**
     * 发放优惠券(导出券码)
     */
    @SneakyThrows
    @PostMapping("/exportCouponForGrant")
    public Response<Void> exportCouponForGrant(@Valid @RequestBody GrantCouponRequest req, HttpServletResponse response){
        Boolean toSmsNotify = req.getToSmsNotify();
        GrantCouponBatchReq grantCouponBatchReq = couponConverter.convertGrantCouponRequest2ApiReq(req);
        grantCouponBatchReq.setBlocCode(UserThreadHolder.getUser().getBlocCode());
        grantCouponBatchReq.setHotelCode(StringUtils.isBlank(req.getHotelCode())?"":req.getHotelCode());
        grantCouponBatchReq.setGrantEvent(GrantEventEnum.MANUAL_GRANT.getEvent());
        grantCouponBatchReq.setSourceClient(SourceClientEnum.CRM.getCode());
        grantCouponBatchReq.setIsAllowCrossStore(0);
        if(Objects.nonNull(toSmsNotify)){
            grantCouponBatchReq.setIsSmsNotification(toSmsNotify?1:0);
        }else{
            grantCouponBatchReq.setIsSmsNotification(0);
        }
        List<String> couponCodes = couponDecorator.grantCouponsByExport(grantCouponBatchReq);
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        String fileName = URLEncoder.encode("优惠券码", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        EasyExcel.write(response.getOutputStream(), String.class)
                .sheet("优惠券码")
                .doWrite(couponCodes);
        return Response.success();
    }

    /**
     * 券发放详情
     */
    @PostMapping("/couponInfoDetail")
    public Response<CouponDetailResponse> couponInfoDetail(@Valid @RequestBody CouponInfoDetailRequest req){
        CouponIssueResp couponGrantInfo = couponDecorator.getCouponGrantInfo(req.getCouponCode());
        return Response.success(couponConverter.convertCouponDetailResponse(couponGrantInfo));
    }

    /**
     * 具体优惠券作废
     */
    @PostMapping("/invalidCoupon")
    public Response<Void> invalidCoupon(@Valid @RequestBody CouponInfoDetailRequest req){
        String userName = UserThreadHolder.getUser().getUserName();
        couponDecorator.invalidCoupon(req.getCouponCode(),userName);
        return Response.success();
    }

    /**
     * 具体优惠券删除
     */
    @PostMapping("/deleteCoupon")
    public Response<Void> deleteCoupon(@Valid @RequestBody CouponInfoDetailRequest req){
        String userName = UserThreadHolder.getUser().getUserName();
        couponDecorator.deleteCoupon(req.getCouponCode(),userName);
        return Response.success();
    }


    /**
     * 优惠券发放批次分页列表查询
     */
    @PostMapping("/grantBatchPageInfos")
    public Response<Pageable<CouponBatchListResponse>> grantBatchPageInfos(@Valid @RequestBody PageCouponBatchRequest req){
        QueryCouponBatchListReq queryCouponBatchListReq = couponConverter.convertPageBatchRequest2ApiReq(req);
        queryCouponBatchListReq.setBlocCode(UserThreadHolder.getUser().getBlocCode());
        Pageable<CouponBatchListResp> pageable = couponDecorator.batchPageInfos(queryCouponBatchListReq);
        return Response.success(PageableUtil.convert(pageable,d-> couponConverter.convertCouponBatchListResp2Response(d)));
    }

    /**
     * 优惠券某发放批次详细列表分页查询
     */
    @PostMapping("/grantBatchDetailPageInfos")
    public Response<Pageable<CouponBatchDetailResponse>> grantBatchDetailPageInfos(@Valid @RequestBody PageCouponBatchDetailsRequest req){
        QueryCouponBatchDetailsReq queryCouponBatchDetailsReq = couponConverter.convertPageBatchDetailsRequest2ApiReq(req);
        Pageable<CouponBatchDetailResp> pageable = couponDecorator.pageCouponBatchDetails(queryCouponBatchDetailsReq);
        return Response.success(couponConverter.convertPageableCouponBatchDetailResponse(pageable));
    }

    /**
     * 优惠券某发放批次详细列表导出
     */
    @SneakyThrows
    @PostMapping("/exportBatchDetail")
    public Response<Void> exportBatchDetail(@Valid @RequestBody PageCouponBatchDetailsRequest req, HttpServletResponse response){

        String batchCode = req.getBatchCode();
        GetByCouponBatchCodeReq getBatchInfoReq = new GetByCouponBatchCodeReq();
        getBatchInfoReq.setBatchCode(batchCode);
        getBatchInfoReq.setBlocCode(UserInfoThreadHolder.getUser().getBlocCode());
        CouponBatchInfoResp couponBatchInfo = couponDecorator.getCouponBatchInfo(getBatchInfoReq);
        String couponName = couponBatchInfo.getCouponName();
        String grantDate = couponBatchInfo.getGrantDate();

        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");

        String fileName = URLEncoder.encode(couponName+grantDate+"发放详情", "UTF-8").replaceAll("\\+", "%20");
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
        List<CouponBatchDetailResp> couponBatchDetailResps = couponDecorator.queryCouponBatchDetails(
                couponConverter.convertPageBatchDetailsRequest2ApiReq(req)
        );
        EasyExcel.write(response.getOutputStream(), CouponExportSimpleResponse.class)
                .sheet("发放详情")
                .doWrite(couponConverter.convertCouponExportSimpleResponse(couponBatchDetailResps));
        return Response.success();
    }

    /**
     * 券批次作废
     */
    @PostMapping("/invalidBatch")
    public Response<Void> invalidBatch(@Valid @RequestBody CouponBatchInfoRequest req){
        UserDto user = UserThreadHolder.getUser();
        String userName = user.getUserName();
        String blocCode = user.getBlocCode();
        couponDecorator.invalidBatch(req.getBatchCode(),blocCode, userName);
        return Response.success();
    }


    /**
     * 券批次删除
     */
    @PostMapping("/deleteBatch")
    public Response<Void> deleteBatch(@Valid @RequestBody CouponBatchInfoRequest req){
        UserDto user = UserThreadHolder.getUser();
        String userName = user.getUserName();
        String blocCode = user.getBlocCode();
        couponDecorator.deleteBatch(req.getBatchCode(),blocCode,userName);
        return Response.success();
    }
}
