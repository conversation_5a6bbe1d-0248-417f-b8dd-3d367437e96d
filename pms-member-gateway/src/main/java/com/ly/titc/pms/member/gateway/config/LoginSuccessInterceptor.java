package com.ly.titc.pms.member.gateway.config;


import com.ly.titc.oauth.client.entity.dto.UserDto;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.member.gateway.thread.local.UserInfoThreadHolder;
import com.ly.titc.pms.member.gateway.converter.UserInfoConverter;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.ehr.UserDataDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.servlet.HandlerInterceptor;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * @Author: liye
 * @Description: 权限验证通过后的拦截器
 * @Date: 2021/6/7 9:59 上午
 */
@Slf4j
@Component
public class LoginSuccessInterceptor implements HandlerInterceptor {

    @Resource(type = UserInfoConverter.class)
    private UserInfoConverter userInfoConverter;

    @Resource
    private UserDataDecorator userDataDecorator;

    /**
     * 拦截器前置处理
     * 设置登陆人具有权限的酒店Ids
     *
     * @param request
     * @param response
     * @param handler
     * @return
     */
    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler) {
        String trackingId = UserThreadHolder.getTrackingId();
        UserInfoDto userInfoDto = null;
        UserDto user = UserThreadHolder.getUser();
        if (null != user){
            userInfoDto = userInfoConverter.convertUserDto2UserInfoDto(user);
            if (!user.getIsAdmin()) {
                userInfoDto.setHotelCodes(userDataDecorator.listHotelIdsByUserId(user.getBlocCode(), user.getTenantCode(), user.getUserId(), trackingId));
            }
        }
        UserInfoThreadHolder.setUser(userInfoDto);
        return true;
    }

    /**
     * 拦截器后置处理
     *
     * @param request
     * @param response
     * @param handler
     * @param ex
     */
    @Override
    public void afterCompletion(HttpServletRequest request, HttpServletResponse response, Object handler, Exception ex) {
        // 不管接口成功与否，都需要将线程变量清空
        log.info("请求结束，清除线程变量");
        UserInfoThreadHolder.clear();
    }
}
