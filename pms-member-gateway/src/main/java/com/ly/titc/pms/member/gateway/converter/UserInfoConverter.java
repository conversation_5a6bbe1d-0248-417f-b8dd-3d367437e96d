package com.ly.titc.pms.member.gateway.converter;


import com.ly.titc.oauth.client.entity.dto.UserDto;
import com.ly.titc.pms.member.com.converter.BaseConverter;
import com.ly.titc.pms.member.gateway.entity.response.sso.UserInfoResponse;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import org.mapstruct.Mapper;


/**
 * @Description:
 * @Author: lixu
 * @Date: 2022/11/27
 */
@Mapper(componentModel = "spring")
public interface UserInfoConverter extends BaseConverter {



    UserInfoDto convertUserDto2UserInfoDto(UserDto user);

    UserInfoResponse convertUserInfoDto2UserInfoResponse(UserInfoDto userInfoDto);
}
