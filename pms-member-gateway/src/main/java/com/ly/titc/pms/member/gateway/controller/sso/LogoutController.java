package com.ly.titc.pms.member.gateway.controller.sso;

import com.alibaba.fastjson.JSON;
import com.ly.titc.common.entity.Response;
import com.ly.titc.oauth.client.service.LogoutService;
import com.ly.titc.pms.member.com.utils.TokenUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

/**
 * 登出
 * @Author：rui
 * @name：LogoutController
 * @Date：2024-12-6 14:17
 * @Filename：LogoutController
 */
@Slf4j
@RestController
public class LogoutController {

    @Resource(type = LogoutService.class)
    private LogoutService logoutService;


    /**
     * 登出
     * @param request
     * @param response
     * @return
     */
    @PostMapping(value = "logout")
    public Response<String> logout(HttpServletRequest request, HttpServletResponse response) {
        String newAccessToken = TokenUtils.getNewAccessToken(request);
        Response<String> result = logoutService.logout(newAccessToken);
        log.info("调用dsf登出，结果{}", JSON.toJSONString(result));
        return Response.success(null);
    }
}
