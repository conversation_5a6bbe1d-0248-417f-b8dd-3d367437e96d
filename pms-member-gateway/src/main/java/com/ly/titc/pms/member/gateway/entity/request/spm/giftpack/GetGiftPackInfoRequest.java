package com.ly.titc.pms.member.gateway.entity.request.spm.giftpack;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-22
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class GetGiftPackInfoRequest extends BaseRequest {

    /**
     * 礼包编码
     */
    @NotBlank(message = "礼包编码不能为空")
    private String giftPackNo;

    /**
     * 版本号
     */
    @NotNull(message = "版本号不能为空")
    private Integer version;

}
