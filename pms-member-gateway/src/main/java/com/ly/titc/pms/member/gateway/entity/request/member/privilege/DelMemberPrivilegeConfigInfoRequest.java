package com.ly.titc.pms.member.gateway.entity.request.member.privilege;

import com.ly.titc.pms.member.gateway.entity.request.BaseRequest;
import lombok.Data;

import java.util.List;

/**
 * @Author：rui
 * @name：DelMemberPrivilegeConfigInfoRequest
 * @Date：2024-11-12 17:35
 * @Filename：DelMemberPrivilegeConfigInfoRequest
 */
@Data
public class DelMemberPrivilegeConfigInfoRequest extends BaseRequest {

    /**
     * 主键id，编辑时必传
     */
    private String id;

    /**
     * 主类型 1:ELONG 2:集团 3:门店
     */
    private Integer masterType;

    /**
     * 主类型编码 ELONG 集团编码 门店编码
     */
    private String masterCode;

    /**
     * 权益类型 1 价格权益 2 积分权益 3 线下权益 4 生态权益
     */
    private Integer type;

    /**
     * 权益名称
     */
    private String name;

    /**
     * 权益值
     */
    private String value;

    /**
     * 权益单位
     */
    private String unit;

    /**
     * 权益图标
     */
    private String pic;

    /**
     * 权益描述
     */
    private String description;

    /**
     * 适用类型 1 集团 2 品牌 3 门店
     */
    private Integer applicationType;

    /**
     * 适用范围 0 默认 1 全部
     */
    private Integer applicationScope;

    /**
     * 权益排序
     */
    private Integer sort;

    /**
     * 适用类型 值集合
     */
    private List<String> scopeValues;
}
