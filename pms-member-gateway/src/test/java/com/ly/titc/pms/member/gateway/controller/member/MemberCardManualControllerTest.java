package com.ly.titc.pms.member.gateway.controller.member;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.gateway.entity.request.member.IssueMemberCardManualRequest;
import com.ly.titc.pms.member.gateway.entity.response.member.IssueMemberCardManualResponse;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardRequestDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardResultDto;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * 会员卡手动发放控制器测试
 *
 * <AUTHOR>
 * @date 2024/12/25 10:00
 */
@ExtendWith(MockitoExtension.class)
class MemberCardManualControllerTest {

    @Mock
    private MemberMedService memberMedService;

    @InjectMocks
    private MemberCardManualController controller;

    @Test
    void testIssueMemberCard_Success() {
        // 准备测试数据
        IssueMemberCardManualRequest request = new IssueMemberCardManualRequest();
        request.setBlocCode("BLOC001");
        request.setCustomerNo("CUST001");
        request.setDefaultCard(0);
        request.setCardId(1L);
        request.setCardLevel(1);
        request.setHotelCode("HOTEL001");
        request.setBizId("BIZ001");
        request.setReason("测试发放");
        request.setOperator("TEST_USER");

        // Mock服务返回
        IssueMemberCardResultDto mockResult = IssueMemberCardResultDto.success(
                "MEM001", "CARD001", 1, "银卡", "REGISTER"
        );
        when(memberMedService.issueMemberCard(any(IssueMemberCardRequestDto.class)))
                .thenReturn(mockResult);

        // 执行测试
        Response<IssueMemberCardManualResponse> response = controller.issueMemberCard(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertTrue(response.getData().isSuccess());
        assertEquals("MEM001", response.getData().getMemberNo());
        assertEquals("CARD001", response.getData().getMemberCardNo());
        assertEquals("REGISTER", response.getData().getOperationType());
    }

    @Test
    void testIssueMemberCard_Failure() {
        // 准备测试数据
        IssueMemberCardManualRequest request = new IssueMemberCardManualRequest();
        request.setBlocCode("BLOC001");
        request.setMemberNo("MEM001");
        request.setDefaultCard(0);
        request.setCardId(1L);
        request.setCardLevel(1);
        request.setHotelCode("HOTEL001");
        request.setBizId("BIZ001");
        request.setOperator("TEST_USER");

        // Mock服务返回失败
        IssueMemberCardResultDto mockResult = IssueMemberCardResultDto.failure("等级不能降级");
        when(memberMedService.issueMemberCard(any(IssueMemberCardRequestDto.class)))
                .thenReturn(mockResult);

        // 执行测试
        Response<IssueMemberCardManualResponse> response = controller.issueMemberCard(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess()); // Controller层总是返回成功，错误信息在data中
        assertNotNull(response.getData());
        assertFalse(response.getData().isSuccess());
        assertEquals("等级不能降级", response.getData().getFailureReason());
    }

    @Test
    void testIssueMemberCard_DefaultCard() {
        // 准备测试数据 - 发放默认卡
        IssueMemberCardManualRequest request = new IssueMemberCardManualRequest();
        request.setBlocCode("BLOC001");
        request.setCustomerNo("CUST001");
        request.setDefaultCard(1); // 发放默认卡
        request.setHotelCode("HOTEL001");
        request.setBizId("BIZ001");
        request.setOperator("TEST_USER");

        // Mock服务返回
        IssueMemberCardResultDto mockResult = IssueMemberCardResultDto.success(
                "MEM001", "CARD001", 1, "默认等级", "REGISTER"
        );
        when(memberMedService.issueMemberCard(any(IssueMemberCardRequestDto.class)))
                .thenReturn(mockResult);

        // 执行测试
        Response<IssueMemberCardManualResponse> response = controller.issueMemberCard(request);

        // 验证结果
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertTrue(response.getData().isSuccess());
        assertEquals("默认等级", response.getData().getCardLevelName());
    }
}
