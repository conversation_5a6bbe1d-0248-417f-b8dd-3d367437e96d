package com.ly.titc.pms.member.dal.dao;

import com.ly.titc.pms.member.dal.entity.po.MemberProfileTagInfo;
import com.ly.titc.springboot.dcdb.dal.core.mapper.BaseMapperX;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 19:46
 */
public interface MemberProfileTagInfoDao extends BaseMapperX<MemberProfileTagInfo> {
    void batchAdd(@Param("list") List<MemberProfileTagInfo> addTagList);
}
