package com.ly.titc.pms.member.dal.entity.po;

import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Date;

/**
 * 会员入住记录表
 */
@TableName(value = "member_check_in_record")
@Data
public class MemberCheckInRecord {

    /**
     * 入住记录编号
     */
    @PrimaryKey(column = "record_no", value = 1)
    private String recordNo;

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 酒店编号
     */
    private String hotelCode;

    /**
     * 会员编号
     */
    private String memberNo;

    /**
     * 预订时会员号
     */
    private String bookMemberNo;

    /**
     * 订单类型
     */
    private Integer orderType;

    /**
     * 订单编号
     */
    private String orderNo;

    /**
     * 渠道编码
     */
    private String channelCode;

    /**
     * 渠道名称
     */
    private String channelName;

    /**
     * 房间号
     */
    private String roomNo;

    /**
     * 房型编号
     */
    private String roomTypeCode;

    /**
     * 房型名称
     */
    private String roomTypeName;

    /**
     * 入住日期
     */
    private Date checkInDate;

    /**
     * 离店日期
     */
    private Date checkOutDate;

    /**
     * 间夜数
     */
    private int roomNights;

    /**
     * 均价
     */
    private BigDecimal averagePrice;

    /**
     * 总房费
     */
    private BigDecimal roomRate;

    /**
     * 其他费用
     */
    private BigDecimal otherRate;

    /**
     * 是否删除；0:未删除；1：已删除
     */
    @TableLogic
    private int isDelete;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
