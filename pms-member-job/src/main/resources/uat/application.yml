server:
  name: titc.java.pms.member.job
  port: 8080
  shutdown: graceful
  servlet:
    context-path: /pms-member

######spring
spring:
  ###client名称：属性的值命名格式 是 ${一站式应用标识(appUk)}
  application:
    name: titc.java.pms.member.job
  servlet:
    multipart:
      max-file-size: 200MB
      max-request-size: 200MB
  http:
    encoding:
      charset: UTF-8
      force: true
      enabled: true

####dal 自定义配置
dal:
  #dbName
  dbName: TETitcPmsMemberDCDB
  #初始化连接
  initialSize: 5
  #最大连接数量
  maxActive: 300
  #最小空闲连接
  minIdle: 5
  #超时等待时间以毫秒为单位
  maxWait: 5000
  #指明是否在从池中取出连接前进行检验,如果检验失败
  testOnBorrow: false
  #指明是否在归还到池中前进行检验
  testOnReturn: false
  #指明连接是否被空闲连接回收器(如果有)进行检验.如果检测失败,则连接将被从池中去除.
  testWhileIdle: true
  #在空闲连接回收器线程运行期间休眠的时间值,以毫秒为单位
  #timeBetweenEvictionRunsMillis: 60000
  #连接在池中保持空闲而不被空闲连接回收器线程 1000 * 60 * 3
  #minEvictableIdleTimeMillis: 180000
  #连接的最大存活时间，如果连接的最大时间大于maxEvictableIdleTimeMillis，则无视最小连接数强制回收
  #maxEvictableIdleTimeMillis: 360000
  #当建立新连接时被发送给JDBC驱动的连接参数
  connectionProperties[allowMultiQueries]: true
  phyTimeoutMillis: 150000
  validationQuery: SELECT 1

###mybatis-plus 配置
mybatis-plus:
  global-config:
    db-config:
      id-type: auto
      field-strategy: not_empty
      table-underline: true
      db-type: mysql
      logic-delete-value: 1 # 逻辑已删除值(默认为 1)
      logic-not-delete-value: 0 # 逻辑未删除值(默认为 0)
  mapper-locations: classpath:mapper/*Mapper.xml
  type-aliases-package: com.ly.titc.pms.member.dal.entity

###旅智框架配置
titc:
  ###dal mapper scan
  dal-scan-package: com.ly.titc.pms.member.dal.dao*
  ###Redisson分布式锁配置
  cache:
    project-name: titc.java.pms.member.job
    cache-items:
      - cache-name: pms.member.job.redis.instance
        isDefault: true
  ###敏感信息加密算法
  encrypt:
    algorithm: BASE64
    ###AES需要
    key:
    iv:

###turbomq config
turbomq:
  name-srv-addr: mqnameserver.qa.17usoft.com:9876
  producer-group: titc_pms_member_bps_producer_group
  ##生产组
  producer:
    nameserver: mqnameserver.qa.17usoft.com:9876
    group: titc_pms_member_bps_producer_group
  ##消费组
  consumer:
    member:
      group: titc_pms_member_bps_consumer_group
      ##QA环境nameserver地址
      nameserver: mqnameserver.qa.17usoft.com:9876
      ##消费者订阅的topic，多个topic之间用英文分号隔开
      subscribeTopics: titc_pms_member_bps_topic

######es 配置
es:
  pms:
    member:
      host: http://es.dss.17usoft.com
      key:
        index: titc-member-qa
        token: 1a9bdc92-15f3-47c3-bb54-511c0e8f1a9f