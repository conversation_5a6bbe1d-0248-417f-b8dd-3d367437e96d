package com.ly.titc.pms.member.job.handler.tag;

import com.alibaba.fastjson.JSON;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.constant.TurboMqTopicTag;
import com.ly.titc.pms.member.mediator.entity.message.GenerateCardNoMsg;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTagHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 预生成会员卡号
 *
 * <AUTHOR>
 * @date 2024/11/11 11:07
 */
@Component
@Slf4j
public class MemberCardNoGenerateHandler extends AbstractTurboMQTagHandler {

    @Resource
    private MemberCardMedService memberCardMedService;

    @Override
    public boolean execute(String messageId, String msg) {
        GenerateCardNoMsg generateCardNoMsg = JSON.parseObject(msg, GenerateCardNoMsg.class);
        memberCardMedService.refreshCardNoCache(generateCardNoMsg.getMasterType(), generateCardNoMsg.getMasterCode(), generateCardNoMsg.getCardId());
        return true;
    }

    @Override
    public String getTag() {
        return TurboMqTopicTag.CARD_NO_GENERATE;
    }
}
