package com.ly.titc.pms.member.job.controller.init;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.job.entity.request.InitRequest;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.UUID;

/**
 * @Author：rui
 * @name：InitController
 * @Date：2024-11-22 13:54
 * @Filename：InitController
 */
@Slf4j
@RestController
@RequestMapping("/member/init")
public class InitController {

    @Resource
    private MemberCardInfoDecorator memberCardInfoDecorator;

    /**
     * 初始化配置
     * @param request
     * @return
     */
    @PostMapping("/init")
    public Response init(@RequestBody InitRequest request) {
        String trackingId = UUID.randomUUID().toString();
        memberCardInfoDecorator.init(request.getMasterType(), request.getMasterCode(), trackingId);
        return Response.success();
    }
}
