package com.ly.titc.pms.member.job.handler.tag;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.com.constant.TurboMqTopicTag;
import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MemberEventEnum;
import com.ly.titc.pms.member.job.converter.MemberConverter;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.MemberDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.MemberTagDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberCardInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileTagInfoDto;
import com.ly.titc.pms.member.mediator.handler.es.ElasticsearchHandler;
import com.ly.titc.pms.member.mediator.service.*;
import com.ly.titc.springboot.mq.handler.topic.AbstractTurboMQTagHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 会员事件
 *
 * <AUTHOR>
 * @date 2024/11/11 21:21
 */
@Component
@Slf4j
public class MemberEventHandler extends AbstractTurboMQTagHandler {

    @Resource
    private ElasticsearchHandler elasticsearchHandler;

    @Resource
    private MemberMedService memberMedService;

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private MemberProfileMedService memberProfileMedService;

    @Resource
    private MessageMedService messageMedService;

    @Resource
    private MemberConverter memberConverter;

    @Resource
    private MemberBlacklistMedService memberBlacklistMedService;

    @Override
    public boolean execute(String messageId, String msg) {
        log.error("收到会员事件消息，消息ID:{}, 消息:{}", messageId, msg);
        MemberEventMsg memberEventMsg = JSON.parseObject(msg, MemberEventMsg.class);
        MemberEventEnum eventType = memberEventMsg.getEventType();
        String memberNo = memberEventMsg.getMemberNo();
        Integer masterType = memberEventMsg.getMasterType();
        String masterCode = memberEventMsg.getMasterCode();
        if (eventType.equals(MemberEventEnum.CANCEL)) {
            // 删除事件
            elasticsearchHandler.delete(memberNo);
        } else {
            // 更新ES数据
            MemberDetailDto memberInfo = memberMedService.getDetailByMemberNo(masterType, masterCode, memberNo);
            if (memberInfo == null) {
                log.error("收到会员事件【{}】，会员不存在, 会员号:{}", eventType.getDesc(), memberNo);
                sendMemberEventFailOaContext(memberEventMsg, "会员不存在");
                return true;
            }
            MemberDocumentDto memberDocumentDto = memberConverter.convertDtoToDto(memberInfo);
            // 查询会员卡
            List<MemberCardInfoDto> memberCardInfos = memberCardMedService.listByMemberNo(masterType, masterCode, memberNo);
            memberDocumentDto.setMemberCardInfos(memberConverter.convertDtoToDto(memberCardInfos));
            // 查询会员标签
            List<MemberProfileTagInfoDto> memberProfileTagInfos = memberProfileMedService.listMemberTag(masterType, masterCode, memberNo);
            List<MemberTagDocumentDto> memberTagInfos = memberProfileTagInfos.stream().map(memberConverter::convertPoToDto).collect(Collectors.toList());
            memberDocumentDto.setMemberTagInfos(memberTagInfos);
            // 是否黑名单
            memberDocumentDto.setBlackFlag(memberBlacklistMedService.checkWhetherBlacklist(memberNo, null, null) ? Constant.ONE : Constant.ZERO);
            log.info("收到会员事件【{}】, 更新ES数据，memberDocumentDto:{}", eventType.getDesc(), memberDocumentDto);
            elasticsearchHandler.insert(JSONObject.toJSONString(Collections.singletonList(memberDocumentDto)));
        }
        return true;
    }

    @Override
    public String getTag() {
        return TurboMqTopicTag.MEMBER_EVENT;
    }


    /**
     * 会员变动消息处理失败通知
     *
     * @param memberEventMsg
     * @param message
     */
    private void sendMemberEventFailOaContext(MemberEventMsg memberEventMsg, String message) {
        try {
            String title = "【会员变动消息处理失败通知】";
            String content = title + Constant.HTML_RETURN_NEWLINE +
                    "trackingId:" + memberEventMsg.getTraceId() + Constant.HTML_RETURN_NEWLINE +
                    "事件名称:" + memberEventMsg.getEventType().getDesc() + Constant.HTML_RETURN_NEWLINE +
                    "会员归属:" + MasterTypeEnum.getDesc(memberEventMsg.getMasterType()) + Constant.HTML_RETURN_NEWLINE +
                    "会员归属值:" + memberEventMsg.getMasterCode() + Constant.HTML_RETURN_NEWLINE +
                    "会员号:" + memberEventMsg.getMemberNo() + Constant.HTML_RETURN_NEWLINE +
                    "失败原因" + message + Constant.HTML_RETURN_NEWLINE;
            messageMedService.sendOaAlertMsg(memberEventMsg.getTraceId(), title, content);
        } catch (Exception e) {
            log.error("告警消息发送失败：{}", memberEventMsg.getTraceId(), e);
        }
    }

}
