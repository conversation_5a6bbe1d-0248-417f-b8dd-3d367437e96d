package com.ly.titc.pms.member.biz;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ly.titc.common.enums.DeletedStatusEnum;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.dao.MemberProfileInvoiceHeaderInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileAddressInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileInvoiceHeaderInfo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 19:41
 */
@Slf4j
@Component
public class MemberProfileInvoiceHeaderInfoBiz extends AbstractBiz<MemberProfileInvoiceHeaderInfo>{

    @Resource
    private MemberProfileInvoiceHeaderInfoDao memberProfileInvoiceHeaderInfoDao;

    public void add(MemberProfileInvoiceHeaderInfo entity){
        entity.setInvoiceHeaderNo(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
        memberProfileInvoiceHeaderInfoDao.insert(entity);
    }

    public void update(MemberProfileInvoiceHeaderInfo info) {
        LambdaUpdateWrapper<MemberProfileInvoiceHeaderInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberProfileInvoiceHeaderInfo::getInvoiceHeaderNo, info.getInvoiceHeaderNo())
                .eq(MemberProfileInvoiceHeaderInfo::getMemberNo, info.getMemberNo());
        wrapper.set(MemberProfileInvoiceHeaderInfo::getInvoiceType, info.getInvoiceType())
                .set(MemberProfileInvoiceHeaderInfo::getHeaderName, info.getHeaderName())
                .set(MemberProfileInvoiceHeaderInfo::getTaxCode, info.getTaxCode())
                .set(MemberProfileInvoiceHeaderInfo::getNeedSpecialInvoice, info.getNeedSpecialInvoice())
                .set(MemberProfileInvoiceHeaderInfo::getCompanyName, info.getCompanyName())
                .set(MemberProfileInvoiceHeaderInfo::getCompanyTel, info.getCompanyTel())
                .set(MemberProfileInvoiceHeaderInfo::getBankName, info.getBankName())
                .set(MemberProfileInvoiceHeaderInfo::getBankAccount, info.getBankAccount())
                .set(MemberProfileInvoiceHeaderInfo::getSort, info.getSort())
                .set(MemberProfileInvoiceHeaderInfo::getModifyUser, info.getModifyUser())
                .set(MemberProfileInvoiceHeaderInfo::getGmtModified, LocalDateTime.now())
        ;
        memberProfileInvoiceHeaderInfoDao.update(null, wrapper);
    }

    public void delete(String memberNo, Long invoiceHeaderNo, String operator) {
        LambdaUpdateWrapper<MemberProfileInvoiceHeaderInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberProfileInvoiceHeaderInfo::getInvoiceHeaderNo, invoiceHeaderNo)
                .eq(MemberProfileInvoiceHeaderInfo::getMemberNo, memberNo);
        wrapper.set(MemberProfileInvoiceHeaderInfo::getIsDelete, DeletedStatusEnum.INVALID.getStatus())
                .set(MemberProfileInvoiceHeaderInfo::getModifyUser, operator)
                .set(MemberProfileInvoiceHeaderInfo::getGmtModified, LocalDateTime.now());
        memberProfileInvoiceHeaderInfoDao.update(null, wrapper);
    }

    public List<MemberProfileInvoiceHeaderInfo> listByMemberNo(String memberNo, String headerName) {
        LambdaQueryWrapper<MemberProfileInvoiceHeaderInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberProfileInvoiceHeaderInfo::getMemberNo, memberNo)
                .like(StringUtils.isNotEmpty(headerName), MemberProfileInvoiceHeaderInfo::getHeaderName, headerName)
                .orderByDesc(MemberProfileInvoiceHeaderInfo::getSort)
                .orderByDesc(MemberProfileInvoiceHeaderInfo::getGmtCreate);
        return memberProfileInvoiceHeaderInfoDao.selectList(wrapper);
    }

    public MemberProfileInvoiceHeaderInfo getByNo(String memberNo, Long invoiceHeaderNo) {
        LambdaQueryWrapper<MemberProfileInvoiceHeaderInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberProfileInvoiceHeaderInfo::getInvoiceHeaderNo, invoiceHeaderNo)
                .eq(MemberProfileInvoiceHeaderInfo::getMemberNo, memberNo);
        return memberProfileInvoiceHeaderInfoDao.selectOne(wrapper);
    }
}
