package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ly.titc.pms.member.dal.dao.MemberOrderDetailInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 15:13
 */
@Slf4j
@Component
public class MemberOrderDetailInfoBiz extends AbstractBiz<MemberOrderDetailInfo> {
    @Resource
    private MemberOrderDetailInfoDao dao;

    public void add(MemberOrderDetailInfo detailInfo) {
        dao.insert(detailInfo);
    }

    public MemberOrderDetailInfo getByOrderNo(String memberOrderNo) {
        return dao.selectOne(Wrappers.lambdaQuery(MemberOrderDetailInfo.class).
                eq(MemberOrderDetailInfo::getMemberOrderNo, memberOrderNo));
    }

    public List<MemberOrderDetailInfo> listByOrderNoList(List<String> memberOrderNo) {
        return dao.selectList(Wrappers.lambdaQuery(MemberOrderDetailInfo.class).
                eq(MemberOrderDetailInfo::getMemberOrderNo, memberOrderNo));
    }

}
