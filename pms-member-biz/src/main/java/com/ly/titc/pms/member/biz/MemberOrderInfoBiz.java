package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.com.enums.OrderStateEnum;
import com.ly.titc.pms.member.dal.dao.MemberOrderInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.springboot.dcdb.dal.core.query.LambdaQueryWrapperX;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 15:10
 */
@Slf4j
@Component
public class MemberOrderInfoBiz extends AbstractBiz<MemberOrderInfo> {
    @Resource
    private MemberOrderInfoDao dao;

    public void add(MemberOrderInfo orderInfo) {
        dao.insert(orderInfo);
    }

    public MemberOrderInfo getByOrderNo(String orderNo) {
        return dao.selectOne(new LambdaQueryWrapperX<MemberOrderInfo>()
                .eq(MemberOrderInfo::getMemberOrderNo, orderNo));
    }

    public void updateState(MemberOrderInfo orderInfo) {
        dao.update(null, new LambdaUpdateWrapper<MemberOrderInfo>()
                .set(MemberOrderInfo::getOrderState, orderInfo.getOrderState())
                .eq(MemberOrderInfo::getMemberOrderNo, orderInfo.getMemberOrderNo()));
    }

    public Pageable<MemberOrderInfo> pageMemberStoredRechargeRecord(Integer masterType, String masterCode, String memberNo, Integer pageIndex, Integer pageSize, Integer state, String beginTime, String endTime) {
        LambdaQueryWrapperX<MemberOrderInfo> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(Objects.nonNull(masterType), MemberOrderInfo::getMasterType, masterType)
                .eq(StringUtils.isNotEmpty(masterCode), MemberOrderInfo::getMasterCode, masterCode)
                .eq(StringUtils.isNotEmpty(masterCode), MemberOrderInfo::getMemberNo, memberNo)
                .eq(MemberOrderInfo::getMemberScene, MemberSceneEnum.RECHARGE.getScene())
                .between(StringUtils.isNotEmpty(beginTime) && StringUtils.isNotEmpty(endTime), MemberOrderInfo::getGmtCreate, beginTime, endTime)
                .orderByDesc(MemberOrderInfo::getGmtCreate);
        Page<MemberOrderInfo> page = dao.selectPage(new Page<>(pageIndex, pageSize), wrapperX);
        if (Objects.nonNull(state)) {
            if (state.equals(1)) {
                wrapperX.notIn(MemberOrderInfo::getOrderState, OrderStateEnum.PAID.getState(), OrderStateEnum.REFUND.getState().intValue());
            } else {
                wrapperX.eq(MemberOrderInfo::getOrderState, state);
            }
        }
        return PageableUtil.convert(page, page.getRecords());
    }

    public Pageable<MemberOrderInfo> pageMemberPurchaseCardRecord(Integer masterType, String masterCode, String memberNo, Integer pageIndex, Integer pageSize, String beginTime, String endTime, String platformChannel) {
        LambdaQueryWrapperX<MemberOrderInfo> wrapperX = new LambdaQueryWrapperX<>();
        wrapperX.eq(Objects.nonNull(masterType), MemberOrderInfo::getMasterType, masterType)
                .eq(StringUtils.isNotEmpty(masterCode), MemberOrderInfo::getMasterCode, masterCode)
                .eq(StringUtils.isNotEmpty(memberNo), MemberOrderInfo::getMemberNo, memberNo)
                .in(MemberOrderInfo::getMemberScene, MemberSceneEnum.PURCHASECARD.getScene(), MemberSceneEnum.UPGRADE.getScene())
                .between(StringUtils.isNotEmpty(beginTime) && StringUtils.isNotEmpty(endTime), MemberOrderInfo::getGmtCreate, beginTime, endTime)
                .eq(StringUtils.isNotEmpty(platformChannel), MemberOrderInfo::getPlatformChannel, platformChannel)
                .orderByDesc(MemberOrderInfo::getGmtCreate);
        Page<MemberOrderInfo> page = dao.selectPage(new Page<>(pageIndex, pageSize), wrapperX);
        return PageableUtil.convert(page, page.getRecords());
    }

    public void updateRefundState(String memberOrderNo, Integer orderState) {
        dao.update(null, new LambdaUpdateWrapper<MemberOrderInfo>()
                .set(MemberOrderInfo::getOrderState, orderState)
                .eq(MemberOrderInfo::getMemberOrderNo, memberOrderNo));
    }

    public void updateMemberNoAndCardNo(String memberOrderNo, String memberNo, String memberCardNo) {
        dao.update(null, new LambdaUpdateWrapper<MemberOrderInfo>()
                .set(MemberOrderInfo::getMemberNo, memberNo)
                .set(MemberOrderInfo::getMemberCardNo, memberCardNo)
                .eq(MemberOrderInfo::getMemberOrderNo, memberOrderNo));
    }
}
