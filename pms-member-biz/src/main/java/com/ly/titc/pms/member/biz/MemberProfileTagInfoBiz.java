package com.ly.titc.pms.member.biz;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.ly.titc.common.enums.DeletedStatusEnum;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.dao.MemberProfileTagInfoDao;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileTagInfo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/5 19:42
 */
@Slf4j
@Component
public class MemberProfileTagInfoBiz extends AbstractBiz<MemberProfileTagInfo>{

    @Resource(type = MemberProfileTagInfoDao.class)
    private MemberProfileTagInfoDao memberProfileTagInfoDao;

    public void add(MemberProfileTagInfo entity){
        entity.setTagNo(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
        memberProfileTagInfoDao.insert(entity);
    }

    public void delete(String memberNo, Long tagNo, String operator) {
        LambdaUpdateWrapper<MemberProfileTagInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberProfileTagInfo::getMemberNo, memberNo)
                .eq(MemberProfileTagInfo::getTagNo, tagNo);
        wrapper.set(MemberProfileTagInfo::getIsDelete, DeletedStatusEnum.INVALID.getStatus())
                .set(MemberProfileTagInfo::getModifyUser, operator);
        memberProfileTagInfoDao.update(null, wrapper);
    }

    public List<MemberProfileTagInfo> listMemberTag(String memberNo) {
        LambdaQueryWrapper<MemberProfileTagInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberProfileTagInfo::getMemberNo, memberNo)
                .orderByDesc(MemberProfileTagInfo::getSort)
                .orderByDesc(MemberProfileTagInfo::getGmtCreate);
        return memberProfileTagInfoDao.selectList(wrapper);
    }

    public List<MemberProfileTagInfo> listMemberTag(List<String> memberNos) {
        LambdaQueryWrapper<MemberProfileTagInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MemberProfileTagInfo::getMemberNo, memberNos)
                .orderByDesc(MemberProfileTagInfo::getSort)
                .orderByDesc(MemberProfileTagInfo::getGmtCreate);
        return memberProfileTagInfoDao.selectList(wrapper);
    }

    public List<MemberProfileTagInfo> listMemberTags(List<String> memberNos, List<Long> tagIds) {
        LambdaQueryWrapper<MemberProfileTagInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.in(MemberProfileTagInfo::getMemberNo, memberNos)
                .in(MemberProfileTagInfo::getTagId, tagIds);
        return memberProfileTagInfoDao.selectList(wrapper);
    }

    public boolean checkMemberTag(String memberNo, Long tagId) {
        LambdaQueryWrapper<MemberProfileTagInfo> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(MemberProfileTagInfo::getMemberNo, memberNo)
                .eq(MemberProfileTagInfo::getTagId, tagId);
        return memberProfileTagInfoDao.selectCount(wrapper) > 0;
    }

    public void batchDelete(List<String> memberNos, List<Long> tagIds, String operator) {
        LambdaUpdateWrapper<MemberProfileTagInfo> wrapper = new LambdaUpdateWrapper<>();
        wrapper.in(CollectionUtils.isNotEmpty(memberNos), MemberProfileTagInfo::getMemberNo, memberNos)
                .in(MemberProfileTagInfo::getTagId, tagIds);
        wrapper.set(MemberProfileTagInfo::getModifyUser, operator)
                .set(MemberProfileTagInfo::getIsDelete, DeletedStatusEnum.INVALID.getStatus());
        memberProfileTagInfoDao.update(null, wrapper);
    }

    public void batchAdd(List<MemberProfileTagInfo> addTagList) {
        addTagList.forEach(entity -> entity.setTagNo(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId()));
        memberProfileTagInfoDao.batchAdd(addTagList);
    }
}
