package com.ly.titc.pms.member.biz;

import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.enums.DeletedStatusEnum;
import com.ly.titc.pms.member.com.enums.ChangeTypeEnum;
import com.ly.titc.pms.member.com.utils.WorkerUtil;
import com.ly.titc.pms.member.dal.dao.MemberCardLevelChangeRecordDao;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @title: MemberCardLevelChangeLogBiz
 * @projectName pms-member
 * @description: 会员等级变更
 * @date 2023/10/11 11:48
 */
@Slf4j
@Component
public class MemberCardLevelChangeRecordBiz extends AbstractBiz<MemberCardLevelChangeRecord> {

    @Resource(type = MemberCardLevelChangeRecordDao.class)
    protected MemberCardLevelChangeRecordDao memberCardLevelChangeRecordDao;

    /**
     * 批量记录
     *
     * @param memberLevelChangeLogs
     */
    public void batchAdd(List<MemberCardLevelChangeRecord> memberLevelChangeLogs) {
        if (!CollectionUtils.isEmpty(memberLevelChangeLogs)) {
            memberCardLevelChangeRecordDao.insertBatch(memberLevelChangeLogs);
        }
    }

    /**
     * 记录会员卡等级变更日志
     *
     * @param memberLevelChangeLog
     */
    public void add(MemberCardLevelChangeRecord memberLevelChangeLog) {
        memberLevelChangeLog.setRecordNo(IdUtil.getSnowflake(WorkerUtil.getWorkId(), WorkerUtil.getDataCenterId()).nextId());
        memberCardLevelChangeRecordDao.insert(memberLevelChangeLog);
    }

    public void deleteByMemberNo(String memberNo, String operator) {
        LambdaUpdateWrapper<MemberCardLevelChangeRecord> wrapper = new LambdaUpdateWrapper<>();
        wrapper.eq(MemberCardLevelChangeRecord::getMemberNo, memberNo)
                .set(MemberCardLevelChangeRecord::getIsDelete, DeletedStatusEnum.INVALID.getStatus())
                .set(MemberCardLevelChangeRecord::getModifyUser, operator);
        memberCardLevelChangeRecordDao.update(null, wrapper);
    }

    public List<MemberCardLevelChangeRecord> getLevelChangeRecordByMaster(Integer masterType, String masterCode) {
        return memberCardLevelChangeRecordDao.getLatestLevelChangeRecordsByMemberAndType(masterType, masterCode, Arrays.asList(ChangeTypeEnum.REGISTER.getType(), ChangeTypeEnum.UPGRADE.getType(), ChangeTypeEnum.FAIL.getType()));
    }

    public Page<MemberCardLevelChangeRecord> page(Integer pageIndex, Integer pageSize, String memberNo, String beginTime, String endTime) {
        return memberCardLevelChangeRecordDao.selectPage(new Page<>(pageIndex, pageSize),
                Wrappers.lambdaQuery(MemberCardLevelChangeRecord.class)
                        .eq(StringUtils.isNotEmpty(memberNo), MemberCardLevelChangeRecord::getMemberNo, memberNo)
                        .between(StringUtils.isNotEmpty(beginTime) && StringUtils.isNotEmpty(endTime), MemberCardLevelChangeRecord::getGmtCreate, beginTime, endTime)
                        .orderByDesc(MemberCardLevelChangeRecord::getGmtCreate));
    }

    public MemberCardLevelChangeRecord getMemberLastedRecordByType(String memberNo, List<Integer> changeType) {
        return memberCardLevelChangeRecordDao.selectOne(Wrappers.lambdaQuery(MemberCardLevelChangeRecord.class)
                .eq(MemberCardLevelChangeRecord::getMemberNo, memberNo)
                .in(MemberCardLevelChangeRecord::getChangeType, changeType)
                .orderByDesc(MemberCardLevelChangeRecord::getGmtCreate)
                .last("limit 1"));
    }


}
