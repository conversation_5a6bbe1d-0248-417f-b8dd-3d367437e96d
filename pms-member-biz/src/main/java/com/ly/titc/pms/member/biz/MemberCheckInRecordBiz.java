package com.ly.titc.pms.member.biz;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.pms.member.dal.dao.MemberCheckInRecordDao;
import com.ly.titc.pms.member.dal.entity.po.MemberCheckInRecord;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * @Author：rui
 * @name：MemberCheckInRecordBiz
 * @Date：2024-12-11 22:00
 * @Filename：MemberCheckInRecordBiz
 */
@Slf4j
@Component
public class MemberCheckInRecordBiz extends AbstractBiz<MemberCheckInRecord> {

    @Resource
    protected MemberCheckInRecordDao dao;


    public Page<MemberCheckInRecord> page(String memberNo, String hotelCode, String checkInBeginTime, String checkInEndTime,
                                          String checkOutBeginTime, String checkOutEndTime, Integer pageIndex, Integer pageSize) {
        return dao.selectPage(new Page<>(pageIndex, pageSize),
                Wrappers.lambdaQuery(MemberCheckInRecord.class)
                        .eq(StringUtils.isNotEmpty(memberNo), MemberCheckInRecord::getMemberNo, memberNo)
                        .eq(StringUtils.isNotEmpty(hotelCode), MemberCheckInRecord::getHotelCode, hotelCode)
                        .between(StringUtils.isNotEmpty(checkInBeginTime) && StringUtils.isNotEmpty(checkInEndTime), MemberCheckInRecord::getCheckInDate, checkInBeginTime, checkInEndTime)
                        .between(StringUtils.isNotEmpty(checkOutBeginTime) && StringUtils.isNotEmpty(checkOutEndTime), MemberCheckInRecord::getCheckOutDate, checkOutBeginTime, checkOutEndTime));
    }
}
