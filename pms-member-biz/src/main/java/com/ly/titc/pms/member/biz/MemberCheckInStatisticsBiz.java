package com.ly.titc.pms.member.biz;

import com.ly.titc.pms.member.dal.dao.MemberCheckInStatisticsDao;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.dal.entity.po.MemberCheckInStatistics;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * @Author：rui
 * @name：MemberCheckInStatisticsBiz
 * @Date：2024-12-11 22:00
 * @Filename：MemberCheckInStatisticsBiz
 */
@Slf4j
@Component
public class MemberCheckInStatisticsBiz extends AbstractBiz<MemberCheckInStatistics> {

    @Resource
    private MemberCheckInStatisticsDao memberCheckInStatisticsDao;

    public MemberCheckInStatistics getByMemberNo(String memberNo) {
        return memberCheckInStatisticsDao.selectOne(MemberCheckInStatistics::getMemberNo, memberNo);
    }

    public List<MemberCheckInStatistics> listByMemberNo(List<String> memberNos) {
        return memberCheckInStatisticsDao.selectList(MemberCheckInStatistics::getMemberNo, memberNos);
    }


}
