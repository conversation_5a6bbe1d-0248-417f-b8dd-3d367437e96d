package com.ly.titc.pms.member.mediator.handler.pay;

import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import com.ly.titc.pms.member.mediator.manager.OrderPayManager;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-28 15:40
 */
public abstract class AbstractPayHandler {
    public AbstractPayHandler() {
        OrderPayManager.putInstance(getRoute(), this);
    }

    /**
     * 支付
     */
    public abstract PayOrderResultDto pay(PayOrderDto dto);

    /**
     * 查询支付结果
     */
    public abstract PayOrderResultDto getPayState(GetPayStateDto dto);

    /**
     * 获取支付路由
     */
    public abstract String getRoute();
}
