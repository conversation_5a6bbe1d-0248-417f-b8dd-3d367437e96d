package com.ly.titc.pms.member.mediator.entity.dto.member;

import com.ly.titc.pms.member.dubbo.enums.MemberStateEnum;
import lombok.Data;

/**
 * 会员注册DTO
 *
 * <AUTHOR>
 * @date 2024/11/19 17:13
 */
@Data
public class RegisterMemberDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值（艺龙会、集团）
     */
    private String masterCode;

    /**
     * 自定义会员号
     * <p>支持传入外部会员编号</p>
     */
    private String customizeMemberNo;

    /**
     * 验证码
     */
    private String verifyCode;

    /**
     * 会员手机号
     */
    private String mobile;

    /**
     * 证件类型;
     */
    private Integer idType;

    /**
     * 证件号
     */
    private String idNo;

    /**
     * 头像
     */
    private String avatar;

    /**
     * 会员昵称
     */
    private String nickName;

    /**
     * 会员真实姓名
     */
    private String realName;

    /**
     * 会员英文名
     */
    private String enName;

    /**
     * 性别1：男；2：女
     */
    private Integer gender;

    /**
     * 生日
     */
    private String birthday;

    /**
     * 国籍
     */
    private String nationality;

    /**
     * 籍贯
     */
    private String nativePlace;

    /**
     * 民族
     */
    private String nation;

    /**
     * 语言
     */
    private String language;

    /**
     * 车牌号
     */
    private String numberPlate;

    /**
     * 会员来源
     */
    private String source;

    /**
     * 注册门店类型 集团:BLOC;门店:HOTEL
     */
    private String registerHotelType;

    /**
     * 注册门店(集团编号; 酒店编号)
     */
    private String registerHotel;

    /**
     * 销售员
     */
    private String salesman;

    /**
     * 会员状态
     */
    private Integer state = MemberStateEnum.VALID.getState();

    /**
     * 备注
     */
    private String remark;

    /**
     * 联系方式
     */
    private MemberContactInfoDto memberContactInfo;

    /**
     * 会员卡
     */
    private IssueMemberCardDto memberCardInfo;

    /**
     * 注册时间，不传则默认时间
     */
    private String registerDate;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 密码
     */
    private String password;

    /**
     * 确认密码
     */
    private String confirmPassword;

    /**
     * 客户信息
     */
    private CustomerInfoDto customerInfo;
}
