package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.customer.dubbo.entity.request.customer.BindMemberReq;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.GetByCustomerNoReq;
import com.ly.titc.pms.member.dal.entity.bo.PageMemberParamBo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberContactInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberExtendInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberInfo;
import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;
import com.ly.titc.pms.member.dubbo.enums.MemberEventEnum;
import com.ly.titc.pms.member.entity.wrapper.MemberRegisterWrapper;
import com.ly.titc.pms.member.entity.wrapper.MemberSaveWrapper;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员medConverter
 *
 * <AUTHOR>
 * @date 2024/10/29 10:54
 */
@Mapper(componentModel = "spring")
public interface MemberMedConverter {

    @Mappings({
            @Mapping(target = "memberNo", source = "memberInfo.memberNo"),
            @Mapping(target = "createUser", source = "memberInfo.createUser"),
            @Mapping(target = "modifyUser", source = "memberInfo.modifyUser"),
            @Mapping(target = "gmtCreate", source = "memberInfo.gmtCreate"),
            @Mapping(target = "gmtModified", source = "memberInfo.gmtModified"),
            @Mapping(target = "tagNames", source = "tagNames"),
            @Mapping(target = "memberCardInfos", source = "memberCardInfos"),
    })
    MemberDetailDto convertPoToDto(MemberInfo memberInfo, MemberExtendInfo memberExtendInfo, MemberContactInfo memberContactInfo, List<String> tagNames, List<MemberCardInfoDto> memberCardInfos);

//    @Mappings({
//            @Mapping(target = "memberNo", source = "memberInfo.memberNo"),
//            @Mapping(target = "createUser", source = "memberInfo.createUser"),
//            @Mapping(target = "modifyUser", source = "memberInfo.modifyUser"),
//            @Mapping(target = "gmtCreate", source = "memberInfo.gmtCreate"),
//            @Mapping(target = "gmtModified", source = "memberInfo.gmtModified"),
//    })
//    MemberDetailDto convertPoToDto(MemberInfo memberInfo, MemberExtendInfo memberExtendInfo, MemberContactInfo memberContactInfo, List<MemberCardInfo> memberCardInfos);

    default MemberRegisterWrapper convertDtoToWrapper(RegisterMemberDto dto, String memberNo){
        MemberInfo memberInfo = convertDtoToPo(dto, memberNo);
        MemberExtendInfo memberExtendInfo = convertDtoToExtendPo(dto, memberNo);
        MemberContactInfo memberContactInfo = convertDtoToContactPo(dto.getMemberContactInfo(), memberNo);
        return convertPoToWrapper(memberInfo, memberExtendInfo, memberContactInfo);
    }

    MemberRegisterWrapper convertPoToWrapper(MemberInfo memberInfo, MemberExtendInfo memberExtendInfo,  MemberContactInfo memberContactInfo);

    @Mappings({
            @Mapping(target = "createUser", source = "dto.operator"),
            @Mapping(target = "modifyUser", source = "dto.operator"),
            @Mapping(target = "gmtCreate", source = "dto.registerDate"),
            @Mapping(target = "memberNo", source = "memberNo")
    })
    MemberInfo convertDtoToPo(RegisterMemberDto dto, String memberNo);

    @Mappings({
            @Mapping(target = "memberNo", source = "memberNo")
    })
    MemberExtendInfo convertDtoToExtendPo(RegisterMemberDto dto, String memberNo);

    @Mappings({
            @Mapping(target = "memberNo", source = "memberNo")
    })
    MemberContactInfo convertDtoToContactPo(MemberContactInfoDto dto, String memberNo);

    MemberSaveWrapper convertDtoToWrapper(UpdateMemberInfoDto dto);

    MemberEventMsg convertPoToEventMsg(MemberInfoDto memberInfo);

    MemberEventMsg convertPoToEventMsg(MemberInfo memberInfo, MemberEventEnum eventType);

    MemberEventMsg convertPoToEventMsg(IssueMemberCardDto dto);

    MemberEventMsg convertPoToEventMsg(UpdateCardLevelDto dto);

    MemberInfoDto convertPoToDto(MemberInfo memberInfo);

    PageMemberParamBo convertDtoToBo(PageMemberParamDto pageMemberParamDto);

    GetByCustomerNoReq convertDtoToReq(CustomerInfoDto customerInfo);

    BindMemberReq convertDtoToReq(CustomerInfoDto customerInfo, String memberNo);

}
