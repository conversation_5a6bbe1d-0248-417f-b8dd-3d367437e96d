package com.ly.titc.pms.member.mediator.rpc.dubbo.customer;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.BlacklistCustomerReq;
import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.CancelBlacklistCustomerReq;
import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.CheckWhetherBlacklistReq;
import com.ly.titc.pms.customer.dubbo.entity.request.blacklist.PageBlacklistParamReq;
import com.ly.titc.pms.customer.dubbo.entity.response.blacklist.BlacklistInfoResp;
import com.ly.titc.pms.customer.dubbo.interfaces.CustomerBlacklistDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 客户黑名单
 *
 * <AUTHOR>
 * @date 2024/12/11 20:30
 */
@Slf4j
@Component
public class CustomerBlacklistDecorator {

    @DubboReference(group = "${customer-dsf-dubbo-group}",timeout = 30000)
    private CustomerBlacklistDubboService customerBlacklistDubboService;

    /**
     * 校验是否是被拉黑
     *
     * @param req
     * @return
     */
    public boolean checkWhetherBlacklist(CheckWhetherBlacklistReq req){
        return Response.getValidateData(customerBlacklistDubboService.checkWhetherBlacklist(req));
    }

    /**
     * 拉黑客户
     *
     * @param req
     * @return 黑名单编号
     */
    public String blacklist(BlacklistCustomerReq req){
        return Response.getValidateData(customerBlacklistDubboService.blacklist(req));
    }

    /**
     * 取消拉黑客户
     *
     * @param req
     * @return 黑名单编号
     */
    public String cancelBlacklist(CancelBlacklistCustomerReq req){
        return Response.getValidateData(customerBlacklistDubboService.cancelBlacklist(req));
    }

    /**
     * 分页查询拉黑信息
     *
     * @param req
     * @return
     */
    public Pageable<BlacklistInfoResp> pageBlacklist(PageBlacklistParamReq req){
        return Response.getValidateData(customerBlacklistDubboService.pageBlacklist(req));
    }

}
