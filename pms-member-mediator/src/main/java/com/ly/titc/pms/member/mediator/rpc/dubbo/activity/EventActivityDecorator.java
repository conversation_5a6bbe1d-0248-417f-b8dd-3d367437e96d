package com.ly.titc.pms.member.mediator.rpc.dubbo.activity;

import com.alibaba.fastjson.JSON;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.*;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityListResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityResp;
import com.ly.titc.pms.spm.dubbo.interfaces.EventActivityDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-17 20:17
 */
@Slf4j
@Component
public class EventActivityDecorator {

    @DubboReference(group = "${spm-dsf-dubbo-group}")
    private EventActivityDubboService activityDubboService;


    /**
     * 创建/更新事件触发活动
     */
    public Void saveEventActivity(SaveEventActivityReq req) {
        log.info("创建活动请求参数：{}", JSON.toJSONString(req));
        Response<Void>  response=  activityDubboService.saveEventActivity(req);
        return Response.getValidateData(response);
    }


    /**
     * 事件活动分页列表
     */
    public Pageable<EventActivityListResp> pageEventActivities(@Valid PageEventActivitiesReq req){
        Response<Pageable<EventActivityListResp>> response = activityDubboService.pageEventActivities(req);
        return Response.getValidateData(response);
    }

    /**
     * 查看事件活动详情
     */
    public EventActivityDetailResp getEventActivityDetail(@Valid QueryEventActivityReq req){
        Response<EventActivityDetailResp> response = activityDubboService.getEventActivityDetail(req);
        return Response.getValidateData(response);
    }




    /**
     * 启用
     */
    public Void enableEventActivity(@Valid ModifyEventActivityReq req){
    Response<Void> response = activityDubboService.enableEventActivity(req);
        return Response.getValidateData(response);
    }


    /**
     * 停用
     */
    public Void disableEventActivity(@Valid ModifyEventActivityReq req){
    Response<Void> response = activityDubboService.disableEventActivity(req);
        return Response.getValidateData(response);
    }


    /**
     * 查询命中的活动
     */
    public EventActivityResp getHighestPriorityEventActivity(@Valid QueryEventActivitiesReq req){
        Response<EventActivityResp> response = activityDubboService.getHighestPriorityEventActivity(req);
        return Response.getValidateData(response);
    }

    /**
     * 判断某个活动是否符合指定参与条件
     *
     * @param req
     * @return
     */
    public boolean isEventActivityApplicable(CheckEventActivityApplicableReq req){
        Response<Boolean> response = activityDubboService.isEventActivityApplicable(req);
        return Response.getValidateData(response);
    }
}
