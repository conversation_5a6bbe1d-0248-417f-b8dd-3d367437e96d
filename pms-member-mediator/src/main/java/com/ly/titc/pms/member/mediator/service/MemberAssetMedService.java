package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberOrderDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberOrderDto;

/**
 * @Author：rui
 * @name：MemberAssetMedService
 * @Date：2024-12-5 15:08
 * @Filename：MemberAssetMedService
 */
public interface MemberAssetMedService {

    Pageable<MemberOrderDto> pageMemberStoredRechargeRecord(Integer masterType, String masterCode, String memberNo, Integer pageIndex,
                                                            Integer pageSize, Integer state, String beginTime, String endTime, String trackingId);

    MemberOrderDetailDto getMemberStoredRechargeRecord(String memberOrderNo);

}
