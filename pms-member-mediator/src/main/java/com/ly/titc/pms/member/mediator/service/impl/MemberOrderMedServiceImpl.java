package com.ly.titc.pms.member.mediator.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.cashier.dubbo.entity.request.CashierRefundReq;
import com.ly.titc.cashier.dubbo.entity.response.CashierRefundDetailResp;
import com.ly.titc.cashier.dubbo.entity.response.CashierRefundResp;
import com.ly.titc.cashier.dubbo.enums.*;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.biz.MemberOrderInfoBiz;
import com.ly.titc.pms.member.biz.MemberOrderPayInfoBiz;
import com.ly.titc.pms.member.biz.MemberOrderRefundInfoBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.OrderStateEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.enums.RouteEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.com.utils.GenerateUtils;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.entity.wrapper.MemberOrderPayWrapper;
import com.ly.titc.pms.member.entity.wrapper.MemberOrderWrapper;
import com.ly.titc.pms.member.mediator.converter.MemberOrderMedConverter;
import com.ly.titc.pms.member.mediator.converter.MemberRefundOrderMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import com.ly.titc.pms.member.mediator.entity.dto.recharge.MemberStoreRechargeDto;
import com.ly.titc.pms.member.mediator.handler.order.AbstractOrderSceneHandler;
import com.ly.titc.pms.member.mediator.handler.pay.AbstractPayHandler;
import com.ly.titc.pms.member.mediator.manager.OrderPayManager;
import com.ly.titc.pms.member.mediator.manager.OrderSceneManager;
import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.EventActivityDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cashier.OnlinePayDecorator;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.member.mediator.service.MemberOrderMedService;
import com.ly.titc.pms.member.service.MemberOrderService;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.DiscountBenefitDto;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.gear.BaseGearDto;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.CheckEventActivityApplicableReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.QueryEventActivitiesReq;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.QueryEventActivityReq;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityDetailResp;
import com.ly.titc.springboot.redisson.client.RedissonLockClient;
import lombok.extern.slf4j.Slf4j;
import org.redisson.api.RLock;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ly.titc.pms.member.com.constant.CommonConstant.CREATE_REFUND_ORDER_LOCK_KEY_PREFIX;
import static com.ly.titc.pms.member.com.enums.RespCodeEnum.MEMBER_10011;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-26 11:49
 */
@Service
@Slf4j
public class MemberOrderMedServiceImpl implements MemberOrderMedService {
    @Resource(type = RedissonLockClient.class)
    private RedissonLockClient redissonLockClient;
    @Resource
    private MemberOrderMedConverter converter;
    @Resource
    private MemberOrderService orderService;
    @Resource
    private MemberOrderPayInfoBiz payInfoBiz;

    @Resource(type = RedisFactory.class)
    private RedisFactory redisFactory;

    @Resource
    private MemberOrderRefundInfoBiz memberOrderRefundInfoBiz;

    @Resource
    private MemberRefundOrderMedConverter memberRefundOrderMedConverter;

    @Resource
    private OnlinePayDecorator onlinePayDecorator;

    @Resource
    private MemberOrderInfoBiz memberOrderInfoBiz;

    @Resource
    private EventActivityDecorator eventActivityDecorator;

    @Resource
    private MemberMedService memberMedService;

    @Override
    public <T> CreateOrderResultDto createOrder(CreateOrderDto<T> dto) {
        //获取创建订单的业务场景
        String scene = dto.getMemberScene();
        //获取场景对应的scene处理器
        AbstractOrderSceneHandler handler = OrderSceneManager.getInstance(scene);
        //1.创建订单的前置check
        check(dto, handler);
        //2.活动处理
        fillActivity(dto);
        //加锁
        String lockKey = getLockKey(dto, handler);
        RLock rLock = redissonLockClient.lock(lockKey, 3L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", lockKey);
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00002);
        }
        try {
            //创建订单
            return saveOrder(dto, handler);
        } finally {
            rLock.unlock();
        }
    }

    @Override
//    @Transactional(rollbackFor = Exception.class)
    public PayOrderResultDto payOrder(PayOrderDto dto) {
        String lockKey = CommonConstant.PAY_LOCK_KEY_PREFIX + dto.getMemberOrderNo();
        RLock rLock = redissonLockClient.lock(lockKey, 3L);
        if (!rLock.isLocked()) {
            log.error("获取锁失败{}", lockKey);
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00002);
        }
        try {
            //前置校验 校验金额和是否有支付中或者支付成功的的支付申请单
            MemberOrderInfo orderInfo = orderService.getByOrderNo(dto.getMemberOrderNo());
            payCheck(orderInfo, dto);
            List<MemberOrderPayInfo> payInfos = orderService.listPayByOrderNo(dto.getMemberOrderNo());
            //判断是否支付状态是否包含支付中和支付成功的单子
            payInfos.forEach(payInfo -> {
                if (CashierPayStateEnum.PROCESSING.getState().equals(payInfo.getOrderPayState()) || CashierPayStateEnum.PAID.getState().equals(payInfo.getOrderPayState())) {
                    log.info("订单{}存在支付中或者支付成功的支付单{}", dto.getMemberOrderNo(), payInfo.getMemberOrderPayNo());
                    throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00003);
                }
            });
            //生成支付单号
            String payNo = GenerateUtils.generate("MP");
            dto.setMemberOrderPayNo(payNo);
            //保存支付申请单
            MemberOrderPayInfo payInfo = buildMemberOrderPayInfo(dto, orderInfo);
            orderService.savePay(payInfo);
            AbstractPayHandler payHandler = OrderPayManager.getInstance(OrderPayManager.getRoutByProduct(dto.getPayProduct()));
            PayOrderResultDto resultDto = payHandler.pay(dto);
            return dealPayState(resultDto, payInfo, orderInfo);
        } finally {
            rLock.unlock();
        }
    }

    @Override
    public PayOrderResultDto getPayState(GetPayStateDto dto) {
        MemberOrderPayInfo payInfo = payInfoBiz.getByPayNo(dto.getMemberOrderNo(), dto.getMemberOrderPayNo());
        if (payInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00004);
        }
        MemberOrderInfo orderInfo = orderService.getByOrderNo(dto.getMemberOrderNo());
        if (orderInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00004);
        }
        AbstractPayHandler payHandler = OrderPayManager.getInstance(OrderPayManager.getRoutByProduct(payInfo.getPayProduct()));
        PayOrderResultDto resultDto = payHandler.getPayState(dto);
        return dealPayState(resultDto, payInfo, orderInfo);

    }

    @Override
    public RefundResultDto refundOrder(RefundOrderDto dto) {
        String memberOrderNo = dto.getMemberOrderNo();
        MemberOrderInfo memberOrderInfo = orderService.getByOrderNo(memberOrderNo);
        if (memberOrderInfo == null || !memberOrderInfo.getMemberNo().equals(dto.getMemberNo())) {
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00004);
        }
        //加锁
        String lockKey = CREATE_REFUND_ORDER_LOCK_KEY_PREFIX + memberOrderNo;
        Boolean result = redisFactory.setNx(lockKey, Constant.SIX, memberOrderNo);
        //处理中...
        if (!result) {
            log.warn("order refund is processing...;memberNo:{}, memberOrderNo:{}", dto.getMemberNo(), memberOrderNo);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            List<MemberOrderPayInfo> memberOrderPayInfos = payInfoBiz.listByOrderNo(memberOrderNo);
            // 默认只会有一个支付单，单支付方式
            Optional<MemberOrderPayInfo> optional = memberOrderPayInfos.stream().filter(pay -> pay.getOrderPayState().equals(CashierPayStateEnum.PAID.getState())).findFirst();
            if (!optional.isPresent()) {
                log.info("not find pay success record, memberOrderNo:{}", memberOrderNo);
                throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00012);
            }
            MemberOrderPayInfo memberOrderPayInfo = optional.get();
            // 查询退款单
            MemberOrderRefundInfo processRefundOrder = getMemberOrderRefundInfo(memberOrderInfo, memberOrderPayInfo, dto);
            return doRefund(processRefundOrder, memberOrderPayInfo);
        } finally {
            redisFactory.del(lockKey);
        }
    }

    @Override
    public RefundResultDto getRefundState(GetRefundStateDto dto) {
        String refundPayNo = dto.getRefundPayNo();
        MemberOrderRefundInfo memberOrderRefundInfo = memberOrderRefundInfoBiz.getByRefundPayNo(refundPayNo);
        if (memberOrderRefundInfo == null) {
            log.info("refund order is not exist, memberRefundNo:{}", refundPayNo);
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00013);
        }
        if (memberOrderRefundInfo.getRefundState().equals(CashierRefundStateEnum.PROCESSING.getState())) {
            CashierRefundDetailResp refundResult = onlinePayDecorator.getRefundInfo(memberRefundOrderMedConverter.convertRefundDetailReq(memberOrderRefundInfo, dto.getOperator()));
            CashierRefundResp refundResp = memberRefundOrderMedConverter.convertRefundResultResp(refundResult);
            // 处理退款状态
            MemberOrderPayInfo memberOrderPayInfo = payInfoBiz.getByPayNo(memberOrderRefundInfo.getMemberOrderNo(), memberOrderRefundInfo.getMemberOrderPayNo());
            if (memberOrderPayInfo == null) {
                log.info("order pay no is not exist, memberOrderPayNo:{}", memberOrderRefundInfo.getMemberOrderPayNo());
                throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00014);
            }
            // 更新退款单状态
            saveRefundOrder(memberOrderRefundInfo, refundResp);
            // 终态处理
            refundFinalStateProcess(memberOrderRefundInfo, memberOrderPayInfo, refundResp);
        }
        // 组装返回数据
        return memberRefundOrderMedConverter.convertRefundResultDto(memberOrderRefundInfo);
    }

    private void payCheck(MemberOrderInfo orderInfo, PayOrderDto dto) {
        if (orderInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00004);
        }
        if (orderInfo.getAmount().compareTo(dto.getAmount()) != 0) {
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00005);
        }
        if (!orderInfo.getAmountType().equals(dto.getAmountType())) {
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00006);
        }
        dto.setGoodsDes(orderInfo.getMemberSceneDesc());
        dto.setOperator(orderInfo.getCreateUser());
    }

    /**
     * 创建订单前的check
     */
    public <T> CreateOrderCheckResultDto check(CreateOrderDto<T> dto, AbstractOrderSceneHandler<T> handler) {
        //公共check
        CreateOrderCheckResultDto resultDto = new CreateOrderCheckResultDto();
        //校验活动的有效性
        ActivityOrderDto activityOrderDto = dto.getActivityOrderDto();
        if (activityOrderDto != null) {
            CheckEventActivityApplicableReq req = buildCheckEventActivityApplicableReq(dto, activityOrderDto);
            boolean isEventActivityFlag = eventActivityDecorator.isEventActivityApplicable(req);
            if (!isEventActivityFlag) {
                throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00017);
            }
        }

        //todo 传入活动code 查询活动信息 校验卡费 活动有效性
//        ActivityOrderDto activityOrderDto = new ActivityOrderDto();
//        //组装活动信息
//        dto.setActivityOrderDto(activityOrderDto);
//        resultDto.setActivityOrderDto(activityOrderDto);
        //业务的前置校验
        //场景check
        handler.doPreCheck(dto);
        return resultDto;
    }

    private PayOrderResultDto dealPayState(PayOrderResultDto resultDto, MemberOrderPayInfo payInfo, MemberOrderInfo orderInfo) {
        if (!payInfo.getOrderPayState().equals(CashierPayStateEnum.PROCESSING.getState())) {
            log.info("本单已是终态，不处理 payNo:{}", payInfo.getMemberOrderPayNo());
            return resultDto;
        }
        if(orderInfo.getOrderState().equals(OrderStateEnum.REFUND.getState())){
            log.info("订单已退款，不处理 payNo:{}", payInfo.getMemberOrderPayNo());
            return resultDto;
        }
        MemberOrderPayWrapper wrapper = new MemberOrderPayWrapper();
        payInfo = converter.convert(resultDto,payInfo);
        orderInfo.setOrderState(resultDto.getPayState());
        wrapper.setPayInfo(payInfo);
        wrapper.setOrderInfo(orderInfo);
        orderService.updatePayState(wrapper);
        resultDto.setSceneState(TradeStateEnum.SUCCESS.getState());
        //如果支付成功 处理成功后置业务处理
        if (resultDto.getPayState().equals(CashierPayStateEnum.PAID.getState())) {
            try {
                //支付成功后置处理
                AbstractOrderSceneHandler handler = OrderSceneManager.getInstance(orderInfo.getMemberScene());
                OrderPostResultDto postResultDto=   handler.postHandle(orderInfo);
                resultDto.setMemberNo(postResultDto.getMemberNo());
            } catch (Exception e) {
                log.error("支付成功后置处理异常", e);
                resultDto.setSceneState(TradeStateEnum.FAIL.getState());
                orderInfo.setOrderState(OrderStateEnum.PAID_BUSINESS_FAIL.getState());
            }

        }
        orderService.updatePayState(wrapper);
        return resultDto;
    }


    /**
     * 创建订单获取锁
     */
    public <T> String getLockKey(CreateOrderDto<T> dto, AbstractOrderSceneHandler<T> handler) {
        return handler.doGetLockKey(dto.getMemberSceneNoteDto());
    }

    /**
     * 保存订单
     */
    @Transactional(rollbackFor = Exception.class)
    public <T> CreateOrderResultDto saveOrder(CreateOrderDto<T> dto, AbstractOrderSceneHandler<T> handler) {
        //保存会员订单主表和明细表
        MemberOrderWrapper wrapper = converter.convert(dto);
        orderService.save(wrapper);
        dto.setMemberOrderNo(wrapper.getOrderInfo().getMemberOrderNo());
        //保存订单场景表
        handler.saveSceneOrder(dto);
        //支付成功后置处理
        CreateOrderResultDto resultDto= converter.convert(wrapper.getOrderInfo());
        if(dto.getAmount().equals(BigDecimal.ZERO)){
            //如果支付金额为0 直接支付成功
            MemberOrderInfo orderInfo = wrapper.getOrderInfo();
            orderInfo.setOrderState(OrderStateEnum.PAID.getState());
            memberOrderInfoBiz.updateState(orderInfo);
            OrderPostResultDto postResultDto = handler.postHandle(wrapper.getOrderInfo());
            resultDto.setMemberNo(postResultDto.getMemberNo());
        }
        return resultDto;
    }

    /**
     * 获取获取退款单信息
     *
     * @param memberOrderInfo
     * @param memberOrderPayInfo
     * @param dto
     * @return
     */
    private MemberOrderRefundInfo getMemberOrderRefundInfo(MemberOrderInfo memberOrderInfo, MemberOrderPayInfo memberOrderPayInfo, RefundOrderDto dto) {
        String memberOrderPayNo = memberOrderPayInfo.getMemberOrderPayNo();
        String memberOrderNo = memberOrderInfo.getMemberOrderNo();
        List<MemberOrderRefundInfo> refundOrders = memberOrderRefundInfoBiz.listRefundOrder(memberOrderNo, memberOrderPayNo);
        // 查询剩余可退款的金额
        if (CollectionUtils.isNotEmpty(refundOrders)) {
            BigDecimal refundAmount = refundOrders.stream().filter(e -> e.getRefundState().equals(CashierRefundStateEnum.PROCESSING.getState())).map(MemberOrderRefundInfo::getRefundAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal refundableAmount = memberOrderInfo.getAmount().subtract(refundAmount);
            if (dto.getRefundAmount().compareTo(refundableAmount)  > 0) {
                throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00015);
            }
        }
        MemberOrderDetailInfo memberOrderDetailInfo = orderService.getDetailByOrderNo(memberOrderNo);
        MemberOrderRefundInfo processRefundOrder = memberRefundOrderMedConverter.convertRefundOrderInfo(memberOrderInfo, memberOrderDetailInfo, memberOrderPayInfo, dto);
        memberOrderRefundInfoBiz.add(processRefundOrder);
        return processRefundOrder;
    }

    /**
     * 执行退款
     *
     * @param processRefundOrder
     * @param memberOrderPayInfo
     * @return
     */
    private RefundResultDto doRefund(MemberOrderRefundInfo processRefundOrder, MemberOrderPayInfo memberOrderPayInfo) {
        if (!memberOrderPayInfo.getPayProduct().equals(PayProductEnum.CASH.getCode()) && processRefundOrder.getRefundAmount().compareTo(BigDecimal.ZERO) > 0) {
            CashierRefundReq req = memberRefundOrderMedConverter.convertRefundReq(processRefundOrder, memberOrderPayInfo);
            // 调用收银台退款
            CashierRefundResp refundResp = onlinePayDecorator.refund(req);
            // 更新退款单状态
            saveRefundOrder(processRefundOrder, refundResp);
            // 后置处理
            refundFinalStateProcess(processRefundOrder, memberOrderPayInfo, refundResp);
            // 组装返回数据
            return memberRefundOrderMedConverter.convertRefundResultDto(processRefundOrder);
        } else {
            // 退款业务处理
            AbstractOrderSceneHandler handler = OrderSceneManager.getInstance(memberOrderPayInfo.getMemberScene());
            handler.refundHandle(processRefundOrder);

            // 退款成功
            memberOrderInfoBiz.updateRefundState(memberOrderPayInfo.getMemberOrderNo(), OrderStateEnum.REFUND.getState());

            RefundResultDto refundResultDto = new RefundResultDto();
            refundResultDto.setBlocCode(memberOrderPayInfo.getBlocCode()).setHotelCode(memberOrderPayInfo.getHotelCode())
                    .setMemberRefundNo(processRefundOrder.getMemberRefundNo()).setPayProduct(PayProductEnum.CASH.getCode())
                    .setRefundState(CashierRefundStateEnum.REFUND.getState());
            return refundResultDto;
        }
    }

    /**
     * 保存退款单状态
     *
     * @param processRefundOrder
     * @param refundResp
     */
    private void saveRefundOrder(MemberOrderRefundInfo processRefundOrder, CashierRefundResp refundResp) {
        processRefundOrder.setOnlineRefundPayNo(refundResp.getRefundPayNo());
        processRefundOrder.setRefundState(refundResp.getRefundState());
        processRefundOrder.setFailReason(refundResp.getFailReason());
        processRefundOrder.setTransactionId(refundResp.getTransactionId());
        memberOrderRefundInfoBiz.update(processRefundOrder);
    }

    /**
     * 终态处理
     *
     * @param memberOrderPayInfo
     * @param refundResp
     */
    private void refundFinalStateProcess(MemberOrderRefundInfo processRefundOrder, MemberOrderPayInfo memberOrderPayInfo, CashierRefundResp refundResp) {
        if (!refundResp.getRefundState().equals(CashierRefundStateEnum.PROCESSING.getState())) {
            if (refundResp.getRefundState().equals(CashierRefundStateEnum.REFUND.getState())) {
                // 退款业务处理
                AbstractOrderSceneHandler handler = OrderSceneManager.getInstance(memberOrderPayInfo.getMemberScene());
                handler.refundHandle(processRefundOrder);
                // 退款成功
                memberOrderInfoBiz.updateRefundState(memberOrderPayInfo.getMemberOrderNo(), OrderStateEnum.REFUND.getState());
            }
        }
    }

    /**
     * 填充活动信息
     *
     * @param dto
     * @param <T>
     */
    private <T> void fillActivity(CreateOrderDto<T> dto) {
        ActivityOrderDto activityOrderDto = dto.getActivityOrderDto();
        if (activityOrderDto != null) {
            EventActivityDetailResp eventActivityDetail = getEventActivityDetail(activityOrderDto.getActivityCode());
            if (StringUtils.isNotBlank(activityOrderDto.getGearCode())) {
                List<BaseGearDto> baseGears = JSONArray.parseArray(eventActivityDetail.getGearDtoList(), BaseGearDto.class);
                Optional<List<DiscountBenefitDto>> benefitOptional = baseGears.stream().filter(e -> e.getGearCode().equals(activityOrderDto.getGearCode())).map(BaseGearDto::getBenefitDtoList).findFirst();
                benefitOptional.ifPresent(discountBenefits -> activityOrderDto.setGiftPack(JSONObject.toJSONString(discountBenefits)));
            }
        }
    }


    /**
     * 查询活动信息
     *
     * @param activityCode
     * @return
     */
    private EventActivityDetailResp getEventActivityDetail(String activityCode) {
        QueryEventActivityReq activityReq = new QueryEventActivityReq();
        activityReq.setActivityCode(activityCode);
        activityReq.setTrackingId(TraceNoUtil.getTraceNo());
        EventActivityDetailResp eventActivityDetail = eventActivityDecorator.getEventActivityDetail(activityReq);
        if (eventActivityDetail == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_ORDER_00016);
        }
        return eventActivityDetail;
    }
    private CheckEventActivityApplicableReq buildCheckEventActivityApplicableReq(CreateOrderDto dto,
                                                                                 ActivityOrderDto activityOrderDto) {
        CheckEventActivityApplicableReq req = new CheckEventActivityApplicableReq();
        req.setPlatformChannel(dto.getPlatformChannel());
        req.setBlocCode(dto.getBlocCode());
        req.setHotelCode(dto.getHotelCode());
        req.setActivityCode(activityOrderDto.getActivityCode());
        req.setScopeSource(ScopeSourceEnum.BLOC.getCode());
        CheckEventActivityApplicableReq.UserInfo userInfo = buildUserInfo(dto);
        req.setUserInfo(userInfo);
        req.setTrackingId(TraceNoUtil.getTraceNo());
        return req;
    }

    private CheckEventActivityApplicableReq.UserInfo buildUserInfo(CreateOrderDto dto) {
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(dto.getBlocCode());
        MemberDetailDto detailDto = memberMedService.getDetailByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), dto.getMemberNo());
        if (detailDto == null) {
            throw new ServiceException(MEMBER_10011);
        }
        CheckEventActivityApplicableReq.UserInfo userInfo = new CheckEventActivityApplicableReq.UserInfo();
        userInfo.setMemberPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
        userInfo.setIsMember(true);
        userInfo.setMemberTags(detailDto.getTagNames());
        List<CheckEventActivityApplicableReq.MemberCardInfo> memberCardInfos = detailDto.getMemberCardInfos().stream().map(cardInfo -> {
            CheckEventActivityApplicableReq.MemberCardInfo memberCardInfo = new CheckEventActivityApplicableReq.MemberCardInfo();
            memberCardInfo.setMemberCardCode(cardInfo.getCardId() != null ? cardInfo.getCardId().toString() : "");
            memberCardInfo.setMemberLevel(cardInfo.getCardLevel() != null ? cardInfo.getCardLevel().toString() : "");
            return memberCardInfo;
        }).collect(Collectors.toList());
        userInfo.setMemberCardInfos(memberCardInfos);
        return userInfo;
    }

    private MemberOrderPayInfo buildMemberOrderPayInfo(PayOrderDto dto, MemberOrderInfo orderInfo) {
        MemberOrderPayInfo payInfo = converter.convert(dto);
        payInfo.setMasterType(orderInfo.getMasterType());
        payInfo.setMasterCode(orderInfo.getMasterCode());
        payInfo.setHotelCode(orderInfo.getHotelCode());
        payInfo.setBlocCode(orderInfo.getBlocCode());
        payInfo.setClubCode(orderInfo.getClubCode());
        payInfo.setMemberScene(orderInfo.getMemberScene());
        payInfo.setPayeeMasterType(MasterTypeEnum.BLOC.getType());
        payInfo.setPayeeMasterCode(dto.getBlocCode());
        payInfo.setPayeeMasterName(MasterTypeEnum.BLOC.getDesc());
        payInfo.setOrderPayState(CashierPayStateEnum.PROCESSING.getState());
        payInfo.setCreateUser(dto.getOperator());
        payInfo.setModifyUser(dto.getOperator());
        return payInfo;
    }
}
