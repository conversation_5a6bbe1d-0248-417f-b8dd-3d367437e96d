package com.ly.titc.pms.member.mediator.rpc.dubbo.asset;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.BaseMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.PageMemberPointsFlowMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.ListRechargeConsumeRecordMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.PageMemberStoreConsumeMemberReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.MemberPointsFlowInfoResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberTotalPointResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeRecordResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTotalAmountResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTradeConsumeRecordResp;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberPointsDubboService;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberStoreDubboService;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberStoreRechargeConsumeDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * @Author：rui
 * @name：AssetDecoretor
 * @Date：2024-12-9 9:45
 * @Filename：AssetDecoretor
 */
@Slf4j
@Service
public class AssetDecorator {

    @DubboReference(group = "${asset-dsf-dubbo-group}",timeout = 30000)
    private MemberPointsDubboService memberPointsDubboService;

    @DubboReference(group = "${asset-dsf-dubbo-group}",timeout = 30000)
    private MemberStoreDubboService memberStoreDubboService;

    @DubboReference(group = "${asset-dsf-dubbo-group}",timeout = 30000)
    private MemberStoreRechargeConsumeDubboService memberStoreRechargeConsumeDubboService;

    /**
     * 分页查询会员消费、冻结记录
     */
    public Pageable<MemberStoreConsumeRecordResp> pageConsumeRecord(String memberNo, String masterCode, Integer masterType, String beginTime, String endTime,
                                                       Integer pageIndex, Integer pageSize, String consumeType, String trackId, String platformChannel) {
        PageMemberStoreConsumeMemberReq req = new PageMemberStoreConsumeMemberReq();
        req.setPageIndex(pageIndex);
        req.setPageSize(pageSize);
        req.setTrackingId(trackId);
        req.setMemberNo(memberNo);
        req.setMasterCode(masterCode);
        req.setMasterType(masterType);
        req.setBeginTime(beginTime);
        req.setEndTime(endTime);
        req.setTrackingId(trackId);
        req.setConsumeType(consumeType);
        req.setPlatformChannel(platformChannel);
        return Response.getValidateData(memberStoreRechargeConsumeDubboService.page(req));
    }

    /**
     * 翻页查询会员积分记录
     * @param pageIndex
     * @param pageSize
     * @param memberNo
     * @param masterCode
     * @param masterType
     * @param beginTime
     * @param endTime
     * @param trackingId
     * @return
     */
    public Pageable<MemberPointsFlowInfoResp> pagePointRecord(Integer pageIndex, Integer pageSize,String memberNo, String masterCode, Integer masterType, String beginTime, String endTime, String trackingId) {
        PageMemberPointsFlowMemberReq req = new PageMemberPointsFlowMemberReq();
        req.setTrackingId(trackingId);
        req.setMemberNo(memberNo);
        req.setMasterCode(masterCode);
        req.setMasterType(masterType);
        req.setBeginTime(beginTime);
        req.setEndTime(endTime);
        req.setPageIndex(pageIndex);
        req.setPageSize(pageSize);
        return Response.getValidateData(memberPointsDubboService.pageMemberPointsFlow(req));
    }

    /**
     * 查询充值单消费记录
     */
    public List<MemberTradeConsumeRecordResp> listRechargeConsumeRecord(String memberNo, List<String> tradeNoList, String trackingId){
        ListRechargeConsumeRecordMemberReq req = new ListRechargeConsumeRecordMemberReq();
        req.setTrackingId(trackingId);
        req.setTradeNoList(tradeNoList);
        req.setMemberNo(memberNo);
        return Response.getValidateData(memberStoreRechargeConsumeDubboService.listRechargeConsumeRecord(req));
    }

    /**
     * 查询单个会员积分统计
     * @param memberNo
     * @param trackingId
     * @return
     */
    public MemberTotalPointResp getTotalAccountPoints(String memberNo, String trackingId){
        BaseMemberReq req = new BaseMemberReq();
        req.setTrackingId(trackingId);
        req.setMemberNo(memberNo);
        return Response.getValidateData(memberPointsDubboService.getTotalAccountPoints(req));
    }

    /**
     * 查询单个会员储值统计
     * @param memberNo
     * @param trackingId
     * @return
     */
    public MemberTotalAmountResp getTotalAccountAmount(String memberNo, String trackingId){
        BaseMemberReq req = new BaseMemberReq();
        req.setTrackingId(trackingId);
        req.setMemberNo(memberNo);
        return Response.getValidateData(memberStoreDubboService.getTotalAccountAmount(req));
    }
}
