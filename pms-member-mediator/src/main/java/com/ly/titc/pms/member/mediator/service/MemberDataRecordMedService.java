package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.data.*;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberDataRecordMedServiceImpl
 * @Date：2024-12-11 21:42
 * @Filename：MemberDataRecordMedServiceImpl
 */
public interface MemberDataRecordMedService {

    /**
     * 分页查询入住记录
     * @param dto
     * @return
     */
    Pageable<CheckinRecordDto> pageCheckinRecord(PageCheckinRecordDto dto);

    /**
     * 查询入住统计
     * @param memberNo
     * @return
     */
    CheckInStatisticsDto queryMemberCheckInStatistics(String memberNo);

    /**
     * 根据会员查询入住统计
     *
     * @param memberNos
     * @return
     */
    List<CheckInStatisticsDto> listByMemberNos(List<String> memberNos);

    /**
     * 分页查询购卡记录
     * @param dto
     * @return
     */
    Pageable<PurchaseCardRecordDto> pagePurchaseCardRecordDto(PagePurchaseCardRecordDto dto);

    /**
     * 分页查询升降级记录
     *
     */
    Pageable<MemberLevelChangeRecordDto> pageMemberLevelChangeRecord(PageLevelChangeRecordDto dto);

}
