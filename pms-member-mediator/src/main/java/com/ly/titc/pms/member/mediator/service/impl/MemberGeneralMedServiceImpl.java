package com.ly.titc.pms.member.mediator.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.chm.entity.response.BlocChannelResp;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.mdm.entity.dto.dict.DictItemTreeDto;
import com.ly.titc.mdm.entity.response.dict.BlocDictItemResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.member.com.enums.OrderChannelEnum;
import com.ly.titc.pms.member.com.enums.PointsIssueNodeEnum;
import com.ly.titc.pms.member.dubbo.enums.MemberSourceEnum;
import com.ly.titc.pms.member.mediator.converter.MemberGeneralConverter;
import com.ly.titc.pms.member.mediator.entity.dto.general.DictDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.DictItemDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberGeneralCardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberSourceDto;
import com.ly.titc.pms.member.mediator.rpc.dsf.chm.ChmChannelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.DictDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardLevelDecorator;
import com.ly.titc.pms.member.mediator.service.MemberGeneralMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberGeneralMedService
 * @Date：2024-12-4 11:24
 * @Filename：MemberGeneralMedService
 */
@Slf4j
@Service
public class MemberGeneralMedServiceImpl implements MemberGeneralMedService {

    @Resource
    private MemberCardInfoDecorator memberCardInfoDecorator;

    @Resource
    private MemberCardLevelDecorator memberCardLevelDecorator;

    @Resource
    private DictDecorator dictDecorator;

    @Resource
    private MemberGeneralConverter converter;

    @Resource
    private ChmChannelDecorator chmChannelDecorator;

    private static final String dict_code = "ApplicableChannel";


    @Override
    public List<MemberGeneralCardConfigDto> queryMemberIdentity(Integer masterType, String masterCode, String name, String trackingId) {

        List<MemberCardConfigResp> list = memberCardInfoDecorator.listCardConfig(masterType, masterCode, trackingId);
        if (CollectionUtils.isNotEmpty(list)) {
            List<MemberCardConfigResp> firstFilter = new ArrayList<>();
            if (StringUtils.isNotEmpty(name)) {
                firstFilter  = list.stream().filter(item -> item.getCardName().contains(name)).collect(Collectors.toList());
            }
            if (CollectionUtils.isNotEmpty(firstFilter)) {
                List<MemberCardLevelConfigInfoResp> memberCardLevelConfigInfoRespList = memberCardInfoDecorator.
                        listMemberCardLevel(list.stream().map(MemberCardConfigResp::getId).collect(Collectors.toList()), trackingId);
                return converter.convert(firstFilter, memberCardLevelConfigInfoRespList);
            } else {
                List<MemberCardLevelConfigInfoResp> memberCardLevelConfigInfoRespList = memberCardLevelDecorator.queryMemberCardLevel(masterType, masterCode, name, trackingId);
                if (CollectionUtils.isNotEmpty(memberCardLevelConfigInfoRespList)) {
                    List<Long> cardIdList = memberCardLevelConfigInfoRespList.stream().map(MemberCardLevelConfigInfoResp::getCardId).collect(Collectors.toList());
                    list = list.stream().filter(item -> cardIdList.contains(item.getId())).collect(Collectors.toList());
                    return converter.convert(list, memberCardLevelConfigInfoRespList);
                }
            }
        }
        return Collections.emptyList();
    }

    @Override
    public List<MemberSourceDto> queryMemberSource(Integer masterType, String masterCode, String name) {

        List<MemberSourceDto> list = new ArrayList<>();
        for (PlatformChannelEnum value : PlatformChannelEnum.values()) {
            if (StringUtils.isNotEmpty(name)) {
                if (!value.getPlatformChannelDesc().contains(name)) {
                    continue;
                }
            }
            list.add(new MemberSourceDto().setSource(value.getPlatformChannel()).setDesc(value.getPlatformChannelDesc()));
        }
        return list;
    }

    @Override
    public List<DictDto> queryApplicableChannel(String trackingId) {

       Map<String, List<PlatformChannelEnum>> map =  Arrays.stream(PlatformChannelEnum.values()).collect(Collectors.groupingBy(PlatformChannelEnum::getPlatform));
        List<DictDto> resp = new ArrayList<>();
        map.forEach((k, v) -> {
            DictDto dictDto = new DictDto().setValue(k).setName(v.get(0).getPlatformDesc());
            dictDto.setItems(v.stream().map(item -> {
                return new DictItemDto().setValue(item.getPlatformChannel()).setName(item.getPlatformChannelDesc());
            }).collect(Collectors.toList()));
            resp.add(dictDto);
        });
        return resp;
    }

    @Override
    public List<DictDto> queryApplicableOrderChannel(String blocCode) {

        List<BlocChannelResp> channelRespList = chmChannelDecorator.listBlocChannel(blocCode);
        List<DictDto> resp = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(channelRespList)) {
            Map<String,List<BlocChannelResp>> blocChannelMap = channelRespList.stream().collect(Collectors.groupingBy(BlocChannelResp::getChannelTypeCode));
            blocChannelMap.forEach((k, v) -> {
                DictDto dictDto = new DictDto().setValue(k).setName(v.get(0).getChannelTypeName());
                dictDto.setItems(v.stream().map(item -> {
                    return new DictItemDto().setValue(item.getChannelCode()).setName(item.getChannelName());
                }).collect(Collectors.toList()));
                resp.add(dictDto);
            });
        }

        return resp;
    }

    @Override
    public List<DictDto> queryApplicableExpense(String blocCode) {
        return new ArrayList<>();
    }

    @Override
    public List<DictDto> queryPointsIssueNode(String blocCode) {
        return Arrays.stream(PointsIssueNodeEnum.values()).map(e -> new DictDto().setValue(e.getNode()).setName(e.getDesc())).collect(Collectors.toList());
    }
}
