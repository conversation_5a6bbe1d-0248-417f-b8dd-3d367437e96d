package com.ly.titc.pms.member.mediator.rpc.dubbo.giftpack;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.GrantGiftPackBatchReq;
import com.ly.titc.pms.spm.dubbo.entity.request.giftpack.*;
import com.ly.titc.pms.spm.dubbo.entity.response.giftpack.*;
import com.ly.titc.pms.spm.dubbo.interfaces.GiftPackDubboService;
import com.ly.titc.pms.spm.dubbo.interfaces.GiftPackGrantDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-28
 */
@Slf4j
@Component
public class GiftPackDecorator {

    @DubboReference(group = "${spm-dsf-dubbo-group}",check = false)
    private GiftPackDubboService giftPackDubboService;

    @DubboReference(group = "${spm-dsf-dubbo-group}",check = false)
    private GiftPackGrantDubboService giftPackGrantDubboService;

    /**
     * 保存礼包信息
     */
    public String saveGiftPackInfo(SaveGiftPackInfoReq req){
        Response<String> response = giftPackDubboService.saveGiftPackInfo(req);
        return Response.getValidateData(response);
    }

    /**
     * 礼包列表
     */
    public Pageable<GiftPackListResp> pageGiftPackInfoList(GetGiftPackListReq req){
        Response<Pageable<GiftPackListResp>> response = giftPackDubboService.pageGiftPackInfoList(req);
        return Response.getValidateData(response);
    }

    /**
     * 启用/停用
     */
    public void enableAndDisableGiftPackInfo(EnableAndDisableGiftPackReq req){
        Response<String> response = giftPackDubboService.enableAndDisableGiftPackInfo(req);
        Response.getValidateData(response);
    }

    /**
     * 礼包详情
     */
    public GiftPackInfoResp getGiftPackInfo(GetGiftPackInfoReq req){
        Response<GiftPackInfoResp> response = giftPackDubboService.getGiftPackInfo(req);
        return Response.getValidateData(response);
    }

    public void grantGiftPack(GrantGiftPackBatchReq grantGiftPackBatchReq){
        giftPackGrantDubboService.grantGiftPack(grantGiftPackBatchReq);
    }

    /**
     * 礼包删除
     */
    public void delete(DeleteGiftPackReq req){
        Response<String> response = giftPackDubboService.deleteGiftPack(req);
        Response.getValidateData(response);
    }

    public Pageable<GiftPackBatchListResp> batchPageInfos(QueryGiftPackBatchListReq req) {
        Response<Pageable<GiftPackBatchListResp>> pageableResponse = giftPackGrantDubboService.batchPageInfos(req);
        return Response.getValidateData(pageableResponse);
    }

    public void deleteBatch(QueryGiftGrantBatchInfoReq req){
        Response<Void> response = giftPackGrantDubboService.deleteGiftPackBatch(req);
        Response.getValidateData(response);
    }

    public void invalidBatch(QueryGiftGrantBatchInfoReq req){
        Response<Void> response = giftPackGrantDubboService.invalidGiftPackBatch(req);
        Response.getValidateData(response);
    }

    public GiftPackBatchInfoResp getGiftPackBatchInfo( QueryGiftGrantBatchInfoReq req){
        Response<GiftPackBatchInfoResp> response = giftPackGrantDubboService.getGiftPackBatchInfo(req);
        return Response.getValidateData(response);
    }

    public List<GiftPackBatchDetailListResp> batchDetails(QueryGiftPackBatchDetailListReq req) {
        Response<List<GiftPackBatchDetailListResp>> response = giftPackGrantDubboService.queryBatchDetails(req);
        return Response.getValidateData(response);
    }

    public Pageable<GiftPackBatchDetailListResp> batchDetailPageInfos(QueryGiftPackBatchDetailListReq req) {
        Response<Pageable<GiftPackBatchDetailListResp>> response = giftPackGrantDubboService.batchDetailPageInfos(req);
        return Response.getValidateData(response);
    }

    public GiftPackSaleInfoResp getSaleInfo(QueryGiftGrantBatchInfoReq req) {
        Response<GiftPackSaleInfoResp> saleInfo = giftPackGrantDubboService.getSaleInfo(req);
        return Response.getValidateData(saleInfo);
    }

    public List<GiftPackBatchDetailListResp> queryBizGrantDetails(QueryGiftPackGrantDetailsReq req) {
        Response<List<GiftPackBatchDetailListResp>> listResponse = giftPackGrantDubboService.queryBizGrantDetails(req);
        return Response.getValidateData(listResponse);
    }
}
