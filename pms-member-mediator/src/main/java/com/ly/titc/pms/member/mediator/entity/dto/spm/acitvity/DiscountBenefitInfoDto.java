package com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.spm.dubbo.enums.ActivityDiscountTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.BasePriceTypeEnum;
import lombok.Data;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-13 11:44
 */
@Data
public class DiscountBenefitInfoDto {

    /**
     * 优惠类型  coupon 优惠券   member_card  会员卡 gift_pack 礼包  point 积分
     */
    @LegalEnum(target = ActivityDiscountTypeEnum.class, message = "优惠类型不合法",methodName = "getCode")
    private String discountType;

    private String gearCode;
    /**
     * 优惠奖励code/数值
     */
    private String benefitValue;

    /**
     * 奖励名
     */
    private String benefitName;

    /**
     * 奖励描述
     */
    private String benefitDesc;

    /**
     * 基准价类型
     */
    @LegalEnum(target = BasePriceTypeEnum.class, message = "基准价类型不合法",methodName = "getCode")
    private Integer basePriceType;

    /**
     * 奖励数量
     */
    private Integer discountCount;

    /**
     * 是否默认
     * 1:默认 0:不是
     */
    private Integer isDefault;
}
