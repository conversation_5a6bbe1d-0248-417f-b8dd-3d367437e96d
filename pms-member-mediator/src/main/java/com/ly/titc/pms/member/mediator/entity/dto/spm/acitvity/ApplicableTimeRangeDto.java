package com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity;

import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 适用时段
 *
 * <AUTHOR>
 * @date 2024/12/28 11:21
 */
@Data
public class ApplicableTimeRangeDto {

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;

    /**
     * 是否永久有效 true-永久有效 false-非永久有效（固定日期区间） 2099-12-31
     */
    private Boolean isPerpetualEffect;

    /**
     * 不参加的星期
     * 1-7
     */
    private List<Integer> excludeWeeks;

    /**
     * 不参加的日期段
     * yyyy-MM-dd
     */
    private List<CommonTimeRangeDto> excludeTimeRanges;

}
