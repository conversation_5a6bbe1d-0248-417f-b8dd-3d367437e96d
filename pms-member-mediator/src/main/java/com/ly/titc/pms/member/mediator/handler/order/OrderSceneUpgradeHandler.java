package com.ly.titc.pms.member.mediator.handler.order;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.pms.member.biz.MemberOrderDetailInfoBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.converter.MemberCardMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpdateCardLevelDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.UpgradeMemberDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.CreateOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.OrderPostResultDto;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 会员升级
 *
 * <AUTHOR>
 * @date 2024/12/10 17:31
 */
@Slf4j
@Component
public class OrderSceneUpgradeHandler extends AbstractOrderSceneHandler<UpgradeMemberDto>{

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private MemberOrderDetailInfoBiz detailInfoBiz;

    @Resource
    private MemberCardMedConverter memberCardMedConverter;

    @Override
    public CreateOrderDto<UpgradeMemberDto> doPreCheck(CreateOrderDto<UpgradeMemberDto> dto) {
        UpgradeMemberDto upgradeMemberDto = dto.getMemberSceneNoteDto();
        UpdateCardLevelDto updateCardLevelDto = memberCardMedConverter.convertDtoToDto(upgradeMemberDto);
        updateCardLevelDto.setMasterType(dto.getMasterType());
        updateCardLevelDto.setMasterCode(dto.getMasterCode());
        memberCardMedService.checkUpdateCardLevel(updateCardLevelDto);
        return dto;
    }

    @Override
    public String doGetLockKey(UpgradeMemberDto dto) {
        return CommonConstant.CREATE_ORDER_LOCK_KEY_PREFIX + String.format("%s_%s_%s", getScene(), dto.getMemberNo(), dto.getCardId());
    }

    @Override
    public void saveSceneOrder(CreateOrderDto<UpgradeMemberDto> dto) {

    }

    @Override
    public OrderPostResultDto postHandle(MemberOrderInfo orderInfo) {
        MemberOrderDetailInfo detailInfo =  detailInfoBiz.getByOrderNo(orderInfo.getMemberOrderNo());
        UpgradeMemberDto upgradeMemberDto = JSONObject.parseObject(detailInfo.getMemberSceneNote(), UpgradeMemberDto.class);
        UpdateCardLevelDto updateCardLevelDto = memberCardMedConverter.convertDtoToDto(upgradeMemberDto);
        memberCardMedService.updateCardLevel(updateCardLevelDto);
        OrderPostResultDto resultDto = new OrderPostResultDto();
        resultDto.setMemberNo(updateCardLevelDto.getMemberNo());
        resultDto.setMemberOrderNo(orderInfo.getMemberOrderNo());
        return resultDto;
    }

    @Override
    public void refundHandle(MemberOrderRefundInfo orderInfo) {

    }

    @Override
    public String getScene() {
        return MemberSceneEnum.UPGRADE.getScene();
    }
}
