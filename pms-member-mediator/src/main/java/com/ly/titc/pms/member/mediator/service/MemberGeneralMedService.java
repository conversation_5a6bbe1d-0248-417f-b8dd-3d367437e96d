package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.mediator.entity.dto.general.DictDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberGeneralCardConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.general.MemberSourceDto;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberGeneralMedService
 * @Date：2024-12-3 20:31
 * @Filename：MemberGeneralMedService
 */

public interface MemberGeneralMedService {

    /**
     * 查询会员身份
     *
     * @param masterType
     * @param masterCode
     * @param name
     * @param trackingId
     * @return
     */
    List<MemberGeneralCardConfigDto> queryMemberIdentity(Integer masterType, String masterCode, String name, String trackingId);

    /**
     * 查询会员来源
     *
     * @param masterType
     * @param masterCode
     * @param name
     * @return
     */
    List<MemberSourceDto> queryMemberSource(Integer masterType, String masterCode, String name);

    /**
     * 查询适用渠道
     */
    List<DictDto> queryApplicableChannel(String trackingId);

    /**
     * 查询适用订单渠道
     */
    List<DictDto> queryApplicableOrderChannel(String trackingId);

    /**
     * 查询适用消费项
     *
     * @param blocCode
     * @return
     */
    List<DictDto> queryApplicableExpense(String blocCode);

    /**
     * 查询适用消费项
     *
     * @param blocCode
     * @return
     */
    List<DictDto> queryPointsIssueNode(String blocCode);

}
