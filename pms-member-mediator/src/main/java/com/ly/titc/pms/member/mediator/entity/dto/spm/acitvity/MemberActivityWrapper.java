package com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity;

import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityDetailResp;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-18 16:49
 */
@Data
@Accessors(chain = true)
public class MemberActivityWrapper {

    /**
     * 活动详情
     */
    private EventActivityDetailResp activityResp;

    /**
     * 卡模版详情
     */
    private MemberCardConfigResp configResp;

    /**
     * 会员等级详情
     */
    private Map<Integer, MemberCardLevelConfigInfoResp> levelMap = new HashMap<>();
}
