package com.ly.titc.pms.member.mediator.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.cashier.dubbo.entity.request.CashierTermListReq;
import com.ly.titc.cashier.dubbo.entity.request.config.PayCashierSourceGetReq;
import com.ly.titc.cashier.dubbo.entity.request.merchant.CashierHotelPaySettingReq;
import com.ly.titc.cashier.dubbo.entity.response.CashierTermListResp;
import com.ly.titc.cashier.dubbo.entity.response.config.PayCashierConfigResp;
import com.ly.titc.cashier.dubbo.entity.response.merchant.CashierPayOpenStateDto;
import com.ly.titc.cashier.dubbo.entity.response.merchant.CashierPayOpenStateResp;
import com.ly.titc.cashier.dubbo.enums.CashierBusinessTypeEnum;
import com.ly.titc.cashier.dubbo.enums.CashierSourceEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.converter.CashierMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.PayCashierConfigDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.PayCashierConfigQueryDto;
import com.ly.titc.pms.member.mediator.entity.dto.cashier.TermListDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cashier.OnlinePayDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cashier.OnlinePayMerchantDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cashier.PayCashierConfigDecorator;
import com.ly.titc.pms.member.mediator.service.MemberCashierMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-3 19:22
 */
@Service
@Slf4j
public class MemberCashierMedServiceImpl implements MemberCashierMedService {
    @Resource
    private PayCashierConfigDecorator configDecorator;
    @Resource
    private OnlinePayMerchantDecorator merchantDecorator;
    @Resource
    private CashierMedConverter converter;
    @Resource
    private OnlinePayDecorator onlinePayDecorator;

    @Override
    public PayCashierConfigDto getCashierConfig(PayCashierConfigQueryDto dto) {
        PayCashierSourceGetReq req = converter.convert(dto);
        req.setCashierProductCode(CashierBusinessTypeEnum.PMSPAY.getType());
        req.setCashierSource(CashierSourceEnum.BLOC.getCode());
        req.setCashierSourceValue(dto.getBlocCode());
        String cashierScene = MemberSceneEnum.getCashierScene(dto.getMemberScene());
        if(StringUtils.isEmpty(cashierScene)){
            throw new ServiceException("会员场景不存在", RespCodeEnum.CODE_400.getCode());
        }
        req.setCashierScene(MemberSceneEnum.getCashierScene(dto.getMemberScene()));
        PayCashierConfigResp resp =  configDecorator.getByBlocSourceCode(req);
        if(resp == null){
            return null;
        }
        CashierHotelPaySettingReq settingReq = converter.convertSetting(dto);
        settingReq.setBusinessType(CashierBusinessTypeEnum.PMSPAY.getType());
        CashierPayOpenStateResp payOpenStateResp=  merchantDecorator.hotelPaySetting(settingReq);
        List<CashierPayOpenStateDto> openStates =  payOpenStateResp.getOnlinePayOpenState();

        return converter.convert(resp,openStates);
    }

    @Override
    public List<TermListDto> getTermList(BaseDto dto) {
        CashierTermListReq req =  converter.convertTermListReq(dto);
        req.setBusinessType(CashierBusinessTypeEnum.PMSPAY.getType());
        List<CashierTermListResp> termList = onlinePayDecorator.termList(req);
        return converter.convertTemList(termList);
    }
}
