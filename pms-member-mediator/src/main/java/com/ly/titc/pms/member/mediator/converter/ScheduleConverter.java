package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelRelegationRuleResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelUpgradeRuleDetailResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelUpgradeRuleResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberTagConfigInfoResp;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberRelegationRuleDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberUpgradeRuleDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.schedule.MemberUpgradeRuleDto;
import org.mapstruct.Mapper;

import java.util.List;

/**
 * @Author：rui
 * @name：ScheduleConverter
 * @Date：2024-11-21 16:19
 * @Filename：ScheduleConverter
 */
@Mapper(componentModel = "spring")
public interface ScheduleConverter {

    List<MemberTagDto> convertTagDto(List<MemberTagConfigInfoResp> respList);

    List<MemberRelegationRuleDto> convertRelegationRuleDto(List<MemberCardLevelRelegationRuleResp> respList);

    MemberRelegationRuleDto convertRelegationRuleDto(MemberCardLevelRelegationRuleResp resp);

    List<MemberUpgradeRuleDto> convertUpgradeRuleDto(List<MemberCardLevelUpgradeRuleResp> respList);

    MemberUpgradeRuleDto convertUpgradeRuleDto(MemberCardLevelUpgradeRuleResp resp);

}
