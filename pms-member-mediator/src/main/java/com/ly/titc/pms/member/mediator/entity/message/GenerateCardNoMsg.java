package com.ly.titc.pms.member.mediator.entity.message;

import com.baomidou.mybatisplus.annotation.TableField;
import com.ly.titc.common.mq.msg.MsgExt;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.experimental.Accessors;

/**
 * 会员卡号预生成
 *
 * <AUTHOR>
 * @date 2024/11/8 17:49
 */
@EqualsAndHashCode(callSuper = true)
@Data
@Accessors(chain = true)
public class GenerateCardNoMsg extends MsgExt {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    @TableField(value = "master_type")
    private Integer masterType;

    /**
     * 归属值
     */
    @TableField(value = "master_code")
    private String masterCode;

    /**
     * 会员卡ID
     */
    @TableField(value = "card_code")
    private Long cardId;

}
