package com.ly.titc.pms.member.mediator.rpc.dubbo.coupon;

import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.enums.StateEnum;
import com.ly.titc.pms.member.mediator.entity.dto.user.UserInfoDto;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.template.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateDetailResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.CouponTemplateListResp;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.template.SaveCouponTemplateInfoResp;
import com.ly.titc.pms.spm.dubbo.interfaces.CouponTemplateDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.UUID;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Slf4j
@Component
public class CouponTemplateDecorator {
    @DubboReference(group = "${spm-dsf-dubbo-group}")
    private CouponTemplateDubboService couponTemplateDubboService;


    public Pageable<CouponTemplateListResp> pageCouponTemplateList(GetCouponTemplateListReq req){
        Response<Pageable<CouponTemplateListResp>> response = couponTemplateDubboService.pageCouponTemplateList(req);
        return Response.getValidateData(response);
    }

    public List<CouponTemplateListResp> listCouponTemplate(GetCouponTemplateListReq req){
        Pageable<CouponTemplateListResp> pageable = this.pageCouponTemplateList(req);
        return pageable.getDatas();
    }

    public List<CouponTemplateListResp> defaultListCouponTemplate(UserInfoDto user){
        GetCouponTemplateListReq req = new GetCouponTemplateListReq();
        req.setState(StateEnum.VALID.getValue());
        req.setBlocCode(user.getBlocCode());
        req.setCurrPage(Constant.ONE);
        req.setPageSize(Integer.MAX_VALUE);
        req.setTrackingId(UUID.randomUUID().toString());
        Pageable<CouponTemplateListResp> pageable = this.pageCouponTemplateList(req);
        return pageable.getDatas();
    }


    /**
     * 查询优惠券模版明细
     */
    public CouponTemplateDetailResp getCouponTemplateDetail(GetCouponTemplateDetailReq req){
        Response<CouponTemplateDetailResp> couponTemplateDetail = couponTemplateDubboService.getCouponTemplateDetail(req);
        return Response.getValidateData(couponTemplateDetail);
    }


    /**
     * 保存优惠券模版(创建(复制)/更新)
     */
    public SaveCouponTemplateInfoResp saveCouponTemplate(SaveCouponTemplateReq req){
        Response<SaveCouponTemplateInfoResp> response = couponTemplateDubboService.saveCouponTemplate(req);
        return Response.getValidateData(response);
    }
    /**
     * 模版删除
     */
    public void deleteCouponTemplate(DeleteCouponTemplateReq req){
        Response<String> response = couponTemplateDubboService.deleteCouponTemplate(req);
        Response.getValidateData(response);
    }

    /**
     * 模版作废
     */
    public void repealCouponTemplate(ChangeCouponTemplateStateReq req){
        Response<String> response = couponTemplateDubboService.repealCouponTemplate(req);
        Response.getValidateData(response);
    }

    /**
     * 优惠券适用酒店列表
     */
    public Set<String> getCouponTemplateApplicableHotelList(GetCouponTemplateApplicableHotelListReq req){
        Response<Set<String>> response = couponTemplateDubboService.getCouponTemplateApplicableHotelList(req);
        return Response.getValidateData(response);
    }
}
