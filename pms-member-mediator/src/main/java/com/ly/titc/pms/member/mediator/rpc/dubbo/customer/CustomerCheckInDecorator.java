package com.ly.titc.pms.member.mediator.rpc.dubbo.customer;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.customer.dubbo.entity.request.checkIn.GetStatisticsByCustomerNoReq;
import com.ly.titc.pms.customer.dubbo.entity.request.checkIn.PageCheckInRecordParamReq;
import com.ly.titc.pms.customer.dubbo.entity.response.checkIn.CustomerCheckInRecordResp;
import com.ly.titc.pms.customer.dubbo.entity.response.checkIn.CustomerCheckInStatisticsResp;
import com.ly.titc.pms.customer.dubbo.interfaces.CustomerCheckInDubboService;
import com.ly.titc.pms.customer.dubbo.interfaces.CustomerDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 客户入住服务
 *
 * <AUTHOR>
 * @date 2024/12/13 14:42
 */
@Slf4j
@Component
public class CustomerCheckInDecorator {

    @DubboReference(group = "${customer-dsf-dubbo-group}",timeout = 30000)
    private CustomerCheckInDubboService customerCheckInDubboService;


    /**
     * 查询入住统计信息
     *
     * @param req
     * @return
     */
    public CustomerCheckInStatisticsResp getStatisticsByCustomerNo(GetStatisticsByCustomerNoReq req){
        return Response.getValidateData(customerCheckInDubboService.getStatisticsByCustomerNo(req));
    }

    /**
     * 分页查询客户入住记录
     *
     * @param req
     * @return
     */
    public Pageable<CustomerCheckInRecordResp> pageCheckInRecord(PageCheckInRecordParamReq req){
        return Response.getValidateData(customerCheckInDubboService.pageCheckInRecord(req));
    }


}

