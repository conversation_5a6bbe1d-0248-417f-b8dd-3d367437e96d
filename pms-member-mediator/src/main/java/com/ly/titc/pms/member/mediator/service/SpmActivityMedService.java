//package com.ly.titc.pms.member.mediator.service;
//
//import com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity.MemberActivityWrapper;
//import com.ly.titc.pms.spm.dubbo.entity.request.activity.QueryEventActivityReq;
//
///**
// * <AUTHOR>
// * @classname
// * @descrition
// * @since 2024-12-18 10:15
// */
//public interface SpmActivityMedService {
//
//    /**
//     * 会员购卡升级活动详情
//     */
////    MemberActivityWrapper memberCardActivityDetail(String activityCode, String blocCode);
//
//
//
//
//}
