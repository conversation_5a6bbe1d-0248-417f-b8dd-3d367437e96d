package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.pms.member.com.enums.BlacklistSceneEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberBlacklistApplicableDataMapping;
import com.ly.titc.pms.member.dal.entity.po.MemberBlacklistInfo;
import com.ly.titc.pms.member.mediator.entity.dto.blacklist.*;
import com.ly.titc.pms.member.mediator.entity.dto.general.DictDto;
import org.apache.commons.collections4.CollectionUtils;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;
import org.mapstruct.Named;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：CustomerBlacklistMedConverter
 * @Date：2024-12-10 16:05
 * @Filename：CustomerBlacklistMedConverter
 */
@Mapper(componentModel = "spring")
public interface MemberBlacklistMedConverter {

    MemberBlacklistInfo convert2CustomerBlacklistInfo(MemberBlacklistInfoDto customerBlacklistInfoDto);

    List<MemberBlacklistApplicableDataMapping> convert2CustomerBlacklistApplicableDataMapping(List<MemberBlacklistApplicableDataMappingDto> dtoList);

    MemberBlacklistApplicableDataMapping convert2CustomerBlacklistApplicableDataMapping(MemberBlacklistApplicableDataMappingDto dto);

    MemberBlacklistApplicableDataMappingDto convert2CustomerBlacklistApplicableDataMappingDto(MemberBlacklistApplicableDataMapping customerBlacklistApplicableDataMapping);

    default List<MemberBlacklistInfoDto> convert2CustomerBlacklistInfoDto(List<MemberBlacklistInfo> customerBlacklistInfoList, List<MemberBlacklistApplicableDataMapping> mappingList) {
        List<MemberBlacklistInfoDto> dtoList = convert2CustomerBlacklistInfoDto(customerBlacklistInfoList);
        Map<String, List<MemberBlacklistApplicableDataMappingDto>> mappingMap = mappingList.stream().map(this::convert2CustomerBlacklistApplicableDataMappingDto)
                .collect(Collectors.groupingBy(MemberBlacklistApplicableDataMappingDto::getBlacklistNo));
        dtoList.forEach(dto -> {
            List<MemberBlacklistApplicableDataMappingDto> mappingDtoList = mappingMap.get(dto.getBlacklistNo());
            if (CollectionUtils.isNotEmpty(mappingDtoList)) {
                for (MemberBlacklistApplicableDataMappingDto customerBlacklistApplicableDataMappingDto : mappingDtoList) {
                    if (customerBlacklistApplicableDataMappingDto.getApplicableType().equals(1)) {
                        customerBlacklistApplicableDataMappingDto.setScopeName(getPlatformChannelDesc(customerBlacklistApplicableDataMappingDto.getScopeValue()));
                    } else {
                        customerBlacklistApplicableDataMappingDto.setScopeName(BlacklistSceneEnum.getDescByType(customerBlacklistApplicableDataMappingDto.getScopeValue()));
                    }
                }
            }
        });
        return dtoList;
    }

    List<MemberBlacklistInfoDto> convert2CustomerBlacklistInfoDto(List<MemberBlacklistInfo> customerBlacklistInfoList);

    @Named("getPlatformChannelDesc")
    default String getPlatformChannelDesc(String platformChannel) {
        for (PlatformChannelEnum value : PlatformChannelEnum.values()) {
            if (value.getPlatformChannel().equals(platformChannel)) {
                return value.getPlatformChannelDesc();
            }
        }
        return null;
    }

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator"),
            @Mapping(target = "reason", source = "reason")
    })
    MemberBlacklistInfo convertDtoToPo(BlacklistMemberDto dto);

    @Mappings({
            @Mapping(target = "scenes", source = "scenes"),
            @Mapping(target = "platformChannels", source = "platformChannels")
    })
    BlacklistInfoDto convertPoToDto(MemberBlacklistInfo blacklistInfo, List<DictDto> scenes, List<DictDto> platformChannels);
}
