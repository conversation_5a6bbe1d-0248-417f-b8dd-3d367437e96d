package com.ly.titc.pms.member.mediator.rpc.dubbo.coupon;

import cn.hutool.core.util.IdUtil;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.spm.dubbo.entity.request.coupon.*;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.*;
import com.ly.titc.pms.spm.dubbo.interfaces.CouponDubboService;
import com.ly.titc.pms.spm.dubbo.interfaces.CouponGrantDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description 优惠券
 * <AUTHOR>
 * @Date 2024-11-4 14:28
 */
@Slf4j
@Component
public class CouponDecorator {

    @DubboReference(group = "${spm-dsf-dubbo-group}",check = false)
    private CouponDubboService couponDubboService;
    @DubboReference(group = "${spm-dsf-dubbo-group}",check = false)
    private CouponGrantDubboService couponGrantDubboService;

    /**
     * 查询券号的所有适用门店列表
     */
    public List<String> couponApplicableHotels(String couponCode, String blocCode) {
        GetCouponInfoReq getCouponInfoReq = new GetCouponInfoReq();
        getCouponInfoReq.setCouponCode(couponCode);
        getCouponInfoReq.setBlocCode(blocCode);
        getCouponInfoReq.setTrackingId(IdUtil.fastSimpleUUID());
        Response<List<String>> listResponse = couponDubboService.queryCouponApplicableHotelCodes(getCouponInfoReq);
        return Response.getValidateData(listResponse);
    }

    public Pageable<CouponListResp> pageInfosForBloc(QueryCouponListReq req) {
        req.setTrackingId(IdUtil.fastSimpleUUID());
        Response<Pageable<CouponListResp>> response = couponDubboService.blocPageInfos(req);
        return Response.getValidateData(response);
    }


    public List<CouponListResp> queryBlocCouponList(QueryCouponListReq req) {
        req.setTrackingId(IdUtil.fastSimpleUUID());
        Response<List<CouponListResp>> response = couponDubboService.queryBlocCouponList(req);
        return Response.getValidateData(response);
    }

    public void grantCouponsByBind(GrantCouponBatchReq req) {
        req.setTrackingId(IdUtil.fastSimpleUUID());
        req.setOperator(UserThreadHolder.getUser().getUserName());
        couponGrantDubboService.grantCouponBatchByBind(req);
    }

    public List<String> grantCouponsByExport(GrantCouponBatchReq req) {
        req.setTrackingId(IdUtil.fastSimpleUUID());
        req.setOperator(UserThreadHolder.getUser().getUserName());
        Response<List<String>> listResponse = couponGrantDubboService.grantCouponBatchByExport(req);
        return Response.getValidateData(listResponse);
    }

    public CouponIssueResp getCouponGrantInfo(String couponCode) {
        GetByCouponCodeReq couponCodeReq = new GetByCouponCodeReq();
        couponCodeReq.setCouponCode(couponCode);
        couponCodeReq.setTrackingId(IdUtil.fastSimpleUUID());
        Response<CouponIssueResp> couponIssueDetail = couponDubboService.getCouponIssueDetail(couponCodeReq);
        return Response.getValidateData(couponIssueDetail);
    }

    public void invalidCoupon(String couponCode, String operator) {
        GetByCouponCodeReq getByCouponCodeReq = new GetByCouponCodeReq();
        getByCouponCodeReq.setCouponCode(couponCode);
        getByCouponCodeReq.setOperator(operator);
        getByCouponCodeReq.setTrackingId(IdUtil.fastSimpleUUID());
        couponDubboService.invalidCoupon(getByCouponCodeReq);
    }

    public void invalidBatch(String batchCode,String blocCode, String operator) {
        GetByCouponBatchCodeReq getByCouponBatchCodeReq = new GetByCouponBatchCodeReq();
        getByCouponBatchCodeReq.setBatchCode(batchCode);
        getByCouponBatchCodeReq.setBlocCode(blocCode);
        getByCouponBatchCodeReq.setOperator(operator);
        couponGrantDubboService.invalidCouponBatch(getByCouponBatchCodeReq);
    }

    public void deleteBatch(String batchCode,String blocCode, String operator) {
        GetByCouponBatchCodeReq getByCouponBatchCodeReq = new GetByCouponBatchCodeReq();
        getByCouponBatchCodeReq.setBatchCode(batchCode);
        getByCouponBatchCodeReq.setBlocCode(blocCode);
        getByCouponBatchCodeReq.setOperator(operator);
        couponGrantDubboService.deleteCouponBatch(getByCouponBatchCodeReq);
    }

    public void deleteCoupon(String couponCode, String operator) {
        GetByCouponCodeReq getByCouponCodeReq = new GetByCouponCodeReq();
        getByCouponCodeReq.setCouponCode(couponCode);
        getByCouponCodeReq.setOperator(operator);
        getByCouponCodeReq.setTrackingId(IdUtil.fastSimpleUUID());
        couponDubboService.deleteCoupon(getByCouponCodeReq);
    }

    public Pageable<CouponBatchListResp> batchPageInfos(QueryCouponBatchListReq req) {
        req.setTrackingId(IdUtil.fastSimpleUUID());
        Response<Pageable<CouponBatchListResp>> response = couponGrantDubboService.batchPageInfos(req);
        return Response.getValidateData(response);
    }

    public Pageable<CouponBatchDetailResp> pageCouponBatchDetails(QueryCouponBatchDetailsReq req) {
        req.setTrackingId(IdUtil.fastSimpleUUID());
        Response<Pageable<CouponBatchDetailResp>> response = couponGrantDubboService.pageCouponBatchDetails(req);
        return Response.getValidateData(response);
    }


    public List<CouponBatchDetailResp> queryCouponBatchDetails(QueryCouponBatchDetailsReq req) {
        req.setTrackingId(IdUtil.fastSimpleUUID());
        Response<List<CouponBatchDetailResp>> response = couponGrantDubboService.queryCouponBatchDetails(req);
        return Response.getValidateData(response);
    }


    public CouponBatchInfoResp getCouponBatchInfo(GetByCouponBatchCodeReq req) {
        Response<CouponBatchInfoResp> response = couponGrantDubboService.getCouponBatchInfo(req);
        return Response.getValidateData(response);
    }

    public CouponRedeemInfoResp getCouponRedeemInfo(GetCouponInfoReq apiReq) {
        Response<CouponRedeemInfoResp> response = couponDubboService.getCouponRedeemInfo(apiReq);
        return Response.getValidateData(response);
    }

    public CouponStatisticsResp getCouponStatistics(QueryCouponStatisticsReq apiReq) {
        apiReq.setTrackingId(IdUtil.fastSimpleUUID());
        Response<CouponStatisticsResp> response = couponDubboService.queryCouponStatistics(apiReq);
        return Response.getValidateData(response);
    }
}
