//package com.ly.titc.pms.member.mediator.service.impl;
//
//import com.baomidou.mybatisplus.core.toolkit.StringUtils;
//import com.ly.titc.common.enums.MasterTypeEnum;
//import com.ly.titc.common.exceptions.ServiceException;
//import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardConfigResp;
//import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
//import com.ly.titc.pms.member.com.enums.RespCodeEnum;
//import com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity.MemberActivityWrapper;
//import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.EventActivityDecorator;
//import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
//import com.ly.titc.pms.member.mediator.service.SpmActivityMedService;
//import com.ly.titc.pms.spm.dubbo.entity.dto.activity.ApplicableCarrierDto;
//import com.ly.titc.pms.spm.dubbo.entity.request.activity.QueryEventActivitiesReq;
//import com.ly.titc.pms.spm.dubbo.entity.request.activity.QueryEventActivityReq;
//import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityDetailResp;
//import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityResp;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.stereotype.Service;
//
//import javax.annotation.Resource;
//import java.util.Collections;
//import java.util.List;
//import java.util.Map;
//import java.util.UUID;
//import java.util.function.Function;
//import java.util.stream.Collectors;
//
///**
// * <AUTHOR>
// * @classname
// * @descrition
// * @since 2024-12-18 10:16
// */
//@Service
//@Slf4j
//public class SpmActivityMedServiceImpl implements SpmActivityMedService {
//
//    @Resource
//    private EventActivityDecorator activityDecorator;
//    @Resource
//    private MemberCardInfoDecorator infoDecorator;
//
//    @Override
//    public MemberActivityWrapper memberCardActivityDetail(String activityCode ,String blocCode) {
//        MemberActivityWrapper wrapper = new MemberActivityWrapper();
//        QueryEventActivityReq req = new QueryEventActivityReq();
//        req.setActivityCode(activityCode);
//        req.setTrackingId(UUID.randomUUID().toString());
//        EventActivityDetailResp resp = activityDecorator.getEventActivityDetail(req);
//        ApplicableCarrierDto carrierDto =  resp.getApplicableCarrierDto();
//        if(carrierDto == null || StringUtils.isEmpty(carrierDto.getCarrierBizVal())){
//            throw new ServiceException("活动会员卡未配置", RespCodeEnum.CODE_400.getCode());
//        }
//        Long cardId = Long.valueOf(carrierDto.getCarrierBizVal());
//        //查询卡名称
//        MemberCardConfigResp configResp = infoDecorator.getMemberCardConfigInfo(MasterTypeEnum.BLOC.getType(), blocCode, cardId);
//        List<MemberCardLevelConfigInfoResp> resps =  infoDecorator.listMemberCardLevel(Collections.singletonList(cardId), UUID.randomUUID().toString());
//        Map<Integer,MemberCardLevelConfigInfoResp> levelMap = resps.stream().collect(Collectors.toMap(MemberCardLevelConfigInfoResp::getCardLevel, Function.identity()));
//        wrapper.setActivityResp(resp);
//        wrapper.setConfigResp(configResp);
//        wrapper.setLevelMap(levelMap);
//        return wrapper;
//    }
//
//
//}
