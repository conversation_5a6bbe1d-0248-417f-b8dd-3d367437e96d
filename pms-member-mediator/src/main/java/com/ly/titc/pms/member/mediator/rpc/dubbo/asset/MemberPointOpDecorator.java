package com.ly.titc.pms.member.mediator.rpc.dubbo.asset;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.ConsumeMemberPointReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.ConsumeRollBackMemberPointReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.ReceiveMemberPointReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.points.ReceiveRollBackMemberPointReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.point.MemberRecordOPResultResp;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberPointOpDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-28 16:25
 */
@Slf4j
@Service
public class MemberPointOpDecorator {

    @DubboReference(group = "${asset-dsf-dubbo-group}")
    private MemberPointOpDubboService opDubboService;


    /**
     * 获得积分 （调整加积分）
     * @param req
     */
    public MemberRecordOPResultResp receive(ReceiveMemberPointReq req) {
        Response<MemberRecordOPResultResp> response = opDubboService.receive(req);
       return Response.getValidateData(response);
    }

    /**
     * 获得后撤回积分
     */
    public MemberRecordOPResultResp receiveRollback(ReceiveRollBackMemberPointReq req) {
        Response<MemberRecordOPResultResp> response = opDubboService.receiveRollback(req);
        return Response.getValidateData(response);
    }

    /**
     * 消费积分
     */
    public MemberRecordOPResultResp consume(ConsumeMemberPointReq req) {
        Response<MemberRecordOPResultResp> response = opDubboService.consume(req);
        return Response.getValidateData(response);
    }


    /**
     *消费撤回(退款)
     */
    public MemberRecordOPResultResp consumeRollback(ConsumeRollBackMemberPointReq req) {
        Response<MemberRecordOPResultResp> response = opDubboService.consumeRollback(req);
        return Response.getValidateData(response);
    }




}
