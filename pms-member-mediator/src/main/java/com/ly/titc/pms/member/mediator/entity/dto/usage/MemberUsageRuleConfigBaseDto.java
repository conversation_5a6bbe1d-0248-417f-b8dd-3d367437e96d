package com.ly.titc.pms.member.mediator.entity.dto.usage;

import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import lombok.Data;
import lombok.experimental.Accessors;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-29 10:01
 */
@Data
@Accessors(chain = true)
public class MemberUsageRuleConfigBaseDto extends BaseDto {
    private Long id;


    /**
     * 酒馆组编码（冗余存储）
     */
    private String clubCode;

    /**
     * 集团编码（冗余存储）
     */
    private String blocCode;

    /**
     * 酒店编码（冗余存储）
     */
    private Integer hotelCode;

    /**
     * 规则名称
     */
    @NotEmpty(message = "规则名称不能为空")
    private String ruleName;

    /**
     * 规则描述
     */
    private String ruleDesc;

    /**
     * 适用渠道，逗号隔开 使用渠道 线下酒店：PMS、CRM  微订房：微订房公众号、微订房小程序
     */
    @NotNull(message = "适用渠道不能为空")
    private List<String> scopePlatformChannels;

    /**
     * 配置的适用来源 CLUB(集团组) BLOC (集团)，HOTEL(门店)
     */
    @NotNull(message = "适用来源不能为空")
    private List<String> scopeSources;

    /**
     * 配置的门店范围 1 全部门店 2 指定门店
     */
    @NotNull(message = "选择门店范围不能为空")
    private Integer scopeHotelRange;

    /**
     * 指定门店可用时必传
     */
    private ScopeHotelsDto applicableHotelsDto;

    /**
     * 是否可用 1可使用 0不可使用
     */
    @NotNull(message = "是否可用不能为空")
    private Integer isCanUse;



    /**
     * 使用模式 1.指定门店可用，2.仅充值门店可用，3.全部门店可用
     */
    @NotNull(message = "使用模式不能为空")
    private Integer usageMode;



    /**
     * 使用是否需要密码 1：需要 0 不需要
     */
    private Integer isUsePassword;


}
