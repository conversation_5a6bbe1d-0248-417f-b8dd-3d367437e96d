package com.ly.titc.pms.member.mediator.rpc.dsf.mdm;

import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.api.HotelRoomTypeService;
import com.ly.titc.mdm.entity.request.hotel.room.type.SelectRoomTypesByFuzzyReq;
import com.ly.titc.mdm.entity.response.hotel.room.type.RoomTypeBaseInfoResp;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-13
 */
@Component
public class RoomTypeDecorator {

    @DubboReference(protocol = "dsf", providedBy = "${mdm-dsf-group}", version = "${mdm-dsf-service-version}")
    private HotelRoomTypeService hotelRoomTypeService;

    public List<RoomTypeBaseInfoResp> selectRoomTypesByBlocCode(String blocCode,Integer state){
        return this.selectRoomTypesByFuzzy(blocCode,null,null,state);
    }


    public List<RoomTypeBaseInfoResp> selectRoomTypesByFuzzy(String blocCode,List<Long> hotelVids,String fuzzyNameAndCode,Integer state){
        SelectRoomTypesByFuzzyReq req = new SelectRoomTypesByFuzzyReq();
        req.setHotelVids(hotelVids);
        req.setFuzzyNameAndCode(fuzzyNameAndCode);
        req.setState(state);
        req.setBlocCode(blocCode);
        Response<List<RoomTypeBaseInfoResp>> listResponse = hotelRoomTypeService.selectRoomTypesByFuzzy(req);
        return Response.getValidateData(listResponse);
    }
}
