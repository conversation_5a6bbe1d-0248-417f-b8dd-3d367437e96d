package com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.usageRule.*;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberPointUsageRuleAssetResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberPointUsageRuleResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberUsageRuleSaveResultResp;
import com.ly.titc.pms.ecrm.dubbo.interfaces.MemberPointUsageDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition 积分设置
 * @since 2024-11-15 11:54
 */
@Slf4j
@Component
public class MemberPointUsageDecorator {
    @DubboReference(group = "${ecrm-dsf-group}", check = false)
    private MemberPointUsageDubboService pointUsageDubboService;

    /**
     * 根据规则适用主体查询使用规则
     * 资产服务调用
     */
   public  List<MemberPointUsageRuleAssetResp> listRuleForAsset(@Valid QueryMemberScopeUsageForAssetReq req){
       Response<List<MemberPointUsageRuleAssetResp>>  response = pointUsageDubboService.listRuleForAsset(req);
       return Response.getValidateData(response);
   }


    /**
     * 根据搜索条件分页查询配置
     * CRM调用
     */
    public Pageable<MemberPointUsageRuleResp> page(@Valid QueryMemberMasterUsageReq req){
        Response<Pageable<MemberPointUsageRuleResp>> response = pointUsageDubboService.page(req);
        return Response.getValidateData(response);
    }

    /**
     * 保存储值设置（新增和编辑）
     */
    public List<MemberUsageRuleSaveResultResp> save(@Valid MemberPointUsageRuleConfigSaveReq req){
        Response<List<MemberUsageRuleSaveResultResp>> response = pointUsageDubboService.save(req);
        return Response.getValidateData(response);
    }

    /**
     * 更新状态
     */
    public Boolean updateState(@Valid UpdateMemberUsageStateReq req){
        Response<Boolean> response = pointUsageDubboService.updateState(req);
        return Response.getValidateData(response);
    }

    /**
     * 删除
     */
    public  Boolean delete(@Valid DeleteMemberUsageReq req){
        Response<Boolean> response=  pointUsageDubboService.delete(req);
        return Response.getValidateData(response);
    }


    /**
     * 获取已设置的储值规则
     */
    public List<MemberUsageRuleSaveResultResp> listBlocScopeUsageRule(QueryMemberBlocScopeUsageReq req){
        Response<List<MemberUsageRuleSaveResultResp>>  response = pointUsageDubboService.listBlocScopeUsageRule(req);
        return Response.getValidateData(response);
    }


}
