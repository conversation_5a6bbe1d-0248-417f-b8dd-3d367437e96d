package com.ly.titc.pms.member.mediator.rpc.dubbo.cashier;

import com.alibaba.fastjson2.JSON;
import com.ly.titc.cashier.dubbo.entity.request.*;
import com.ly.titc.cashier.dubbo.entity.request.onlinePay.CashierOnlinePayReq;
import com.ly.titc.cashier.dubbo.entity.response.*;
import com.ly.titc.cashier.dubbo.entity.response.onlinePay.CashierOnlinePayResp;
import com.ly.titc.cashier.dubbo.interfaces.pay.OnlinePayDubboService;
import com.ly.titc.common.entity.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-8-21 13:54
 */
@Slf4j
@Service
public class OnlinePayDecorator {

    @DubboReference(group = "${cashier-dsf-dubbo-group}",timeout = 30000)
    private OnlinePayDubboService onlinePayDubboService;

    /**
     * 获取支付终端
     */
    public List<CashierTermListResp> termList(CashierTermListReq req) {
        return Response.getValidateData(onlinePayDubboService.termList(req));
    }


    /**
     * 线上支付
     */
    public CashierOnlinePayResp onlinePay(CashierOnlinePayReq req) {
        log.info("调用收银台线上支付请求参数：{}", JSON.toJSONString(req));
        Response<CashierOnlinePayResp> response = onlinePayDubboService.onlinePay(req);
        log.info("调用收银台线上支付响应参数：{}",JSON.toJSONString(response));
      return   Response.getValidateData(response);
    }

    /**
     * 查询支付订单详情
     * @param req
     * @return
     */
    public CashierPayDetailInfoResp getPayInfo(CashierPayGetOrderDetailReq req) {
        log.info("调用收银台查询支付订单详情请求参数：{}", JSON.toJSONString(req));
        Response<CashierPayDetailInfoResp> response = onlinePayDubboService.getPayInfo(req);
        log.info("调用收银台查询支付订单详情响应参数：{}",JSON.toJSONString(response));
        return Response.getValidateData(response);
    }

    /**
     * 退款
     */
    public CashierRefundResp refund(CashierRefundReq req){
        log.info("调用收银台退款请求参数：{}", JSON.toJSONString(req));
        Response<CashierRefundResp> response = onlinePayDubboService.refund(req);
        log.info("调用收银台退款响应参数：{}",JSON.toJSONString(response));
        return Response.getValidateData(response);
    }

    /**
     * 获取退款信息
     */
    public CashierRefundDetailResp getRefundInfo(CashierRefundDetailReq req){
        log.info("调用收银查询台退款请求参数：{}", JSON.toJSONString(req));
       Response<CashierRefundDetailResp>  response = onlinePayDubboService.getRefundInfo(req);
        log.info("调用收银查询台退款响应参数：{}",JSON.toJSONString(response));
        return Response.getValidateData(response);
    }


    /**
     * 退款补偿获取静态二维码
     */
    public CashierStaticQrCodeResp getStaticQrCode(CashierStaticQrCodeReq req) {
        return Response.getValidateData(onlinePayDubboService.getStaticQrCode(req));
    }



}
