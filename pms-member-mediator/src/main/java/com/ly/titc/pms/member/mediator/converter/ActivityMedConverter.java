package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity.QueryMemberActiveActivityDto;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.QueryEventActivitiesReq;
import org.mapstruct.Mapper;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-18 16:52
 */
@Mapper(componentModel = "spring")
public interface ActivityMedConverter {

    QueryEventActivitiesReq convert(QueryMemberActiveActivityDto dto);
}
