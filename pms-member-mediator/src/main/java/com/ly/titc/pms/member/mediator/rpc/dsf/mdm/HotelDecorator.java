package com.ly.titc.pms.member.mediator.rpc.dsf.mdm;

import com.google.common.collect.Lists;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.mdm.api.HotelService;
import com.ly.titc.mdm.entity.request.ListByFuzzyNameReq;
import com.ly.titc.mdm.entity.request.hotel.*;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.HotelInfoResp;
import com.ly.titc.mdm.entity.response.hotel.PageHotelsResp;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import com.ly.titc.mdm.enums.HotelOpenStateEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.rpc.dsf.AbstractDsfServiceProxy;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @ClassName: HotelDecorator
 * @Description:
 * @since 2022/11/28 13:47
 */
@Slf4j
@Component
public class HotelDecorator extends AbstractDsfServiceProxy {

    private static final String S_NAME = "hotel";

    @DubboReference(protocol = "dsf", providedBy = "${mdm-dsf-group}", version = "${mdm-dsf-service-version}")
    private HotelService hotelService;

    /**
     * 获取门店信息
     *
     * @param req
     * @return
     */
    public HotelInfoResp getHotelByVid(GetHotelByVidReq req) {
        return Response.getValidateData(hotelService.getHotelByVid(req));
    }

    /**
     * 获取酒店下拉列表
     *
     * @param req
     * @return
     */
    public List<SelectHotelResp> selectHotels(SelectHotelReq req) {
        return Response.getValidateData(hotelService.selectHotels(req));
    }

    public List<HotelInfoResp> listByFuzzyName(String blocCode, String fuzzyName, String brandCode,
                                               Integer state, String trackingId) {
        ListByFuzzyNameReq req = new ListByFuzzyNameReq();
        req.setFuzzyName(fuzzyName)
                .setState(state)
                .setBlocCode(blocCode)
                .setBrandCode(brandCode)
                .setTrackingId(trackingId);
        return Response.getValidateData(hotelService.listByFuzzyName(req));
    }

    public List<HotelBaseInfoResp> listHotelBaseInfos(String blocCode,List<String> hotelCodes, String trackingId) {
        return listHotelBaseInfos(blocCode, null,null,hotelCodes, trackingId);
    }

    public List<HotelBaseInfoResp> listHotelBaseInfos(String blocCode,String hotelName,List<String> hotelCodes, String trackingId) {
        return listHotelBaseInfos(blocCode, null,hotelName,hotelCodes, trackingId);
    }

    public List<HotelBaseInfoResp> listHotelBaseInfos(String blocCode,String brandCode,String hotelName,
                                                      List<String> hotelCodes, String trackingId) {
        List<Long> hotelVids = null;
        if (!CollectionUtils.isEmpty(hotelCodes)) {
            hotelVids = hotelCodes.stream().map(Long::valueOf).collect(Collectors.toList());
        }
        ListHotelBaseInfosReq req = new ListHotelBaseInfosReq();
        req.setHotelVids(hotelVids)
                .setHotelName(hotelName)
                .setBlocCode(blocCode)
                .setBrandCode(brandCode)
                .setTrackingId(trackingId);

        log.info("mdm查询酒店基础信息请求参数，blocCode:{},trackingId：{}", blocCode, trackingId);
        HotelService service = getProxy(HotelService.class, MDM_DSF_GS_NAME, S_NAME, mdmVersion);
        Response<List<HotelBaseInfoResp>> listResponse = service.listHotelBaseInfos(req);
        if (null == listResponse || !RespCodeEnum.CODE_200.getCode().equals(listResponse.getCode())) {
            log.info("mdm查询酒店基础信息请求返回错误，返回值：{}，blocCode:{},trackingId：{}", listResponse, blocCode, trackingId);
            return null;
        }
        return listResponse.getData();
    }

    /**
     * 分页酒店
     *
     * @param req
     * @return
     */
    public Pageable<PageHotelsResp> pageHotels(PageHotelsReq req) {
        //cms和eCms都按hotel_code排序
        req.setIsOrderByHotelCode(1);
        return Response.getValidateData(hotelService.pageHotels(req));
    }

    public List<PageHotelsResp> selectHotelsByFuzzy(SelectHotelsByFuzzyReq req) {
        Response<List<PageHotelsResp>> pageableResponse = hotelService.selectHotelsByFuzzy(req);
        return Response.getValidateData(pageableResponse);
    }

    public List<PageHotelsResp> selectHotelsByFuzzy(String blocCode, String hotelFuzzyNameOrCode, Integer state, String tracingId) {
        SelectHotelsByFuzzyReq req = new SelectHotelsByFuzzyReq();
        req.setTrackingId(tracingId);
        req.setBlocCode(blocCode);
        req.setOpenStates(
                Lists.newArrayList(HotelOpenStateEnum.BUILDING.getOpenState(),
                        HotelOpenStateEnum.SOFT_OPENING.getOpenState(), HotelOpenStateEnum.OPENING.getOpenState())
        );
        req.setFuzzyNameAndCode(hotelFuzzyNameOrCode);
        req.setState(state);
        Response<List<PageHotelsResp>> pageableResponse = hotelService.selectHotelsByFuzzy(req);
        return Response.getValidateData(pageableResponse);
    }

    /**
     * 获取酒店基础信息列表
     *
     * @param blocCode
     * @param brandCodes
     * @param hotelName
     * @param trackingId
     * @return
     */
    public List<SelectHotelResp> selectHotels(String blocCode, List<String> brandCodes, String hotelName, List<String> hotelCodeList, String trackingId) {
        SelectHotelReq hotelReq = new SelectHotelReq();
        hotelReq.setBlocCode(blocCode)
                .setBrandCodes(brandCodes)
                .setHotelName(hotelName)
                .setTrackingId(trackingId);
        if (!CollectionUtils.isEmpty(hotelCodeList)) {
            hotelReq.setHotelVids(hotelCodeList.stream().map(Long::valueOf).collect(Collectors.toList()));
        }
        Response<List<SelectHotelResp>> response = hotelService.selectHotels(hotelReq);
        return Response.getValidateData(response);
    }


}
