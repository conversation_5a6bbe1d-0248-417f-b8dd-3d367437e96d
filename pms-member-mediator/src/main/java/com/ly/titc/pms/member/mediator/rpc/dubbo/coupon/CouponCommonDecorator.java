package com.ly.titc.pms.member.mediator.rpc.dubbo.coupon;


import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.spm.dubbo.entity.response.coupon.*;
import com.ly.titc.pms.spm.dubbo.interfaces.CouponCommonDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-11-12
 */
@Slf4j
@Component
public class CouponCommonDecorator {


    @DubboReference(group = "${spm-dsf-dubbo-group}")
    private CouponCommonDubboService couponCommonDubboService;

    /**
     * 优惠券类型
     */
    public List<CouponTypeResp> getCouponTypeInfo(){
        Response<List<CouponTypeResp>> response = couponCommonDubboService.getCouponTypeInfo();
        return Response.getValidateData(response);
    }

    /**
     * 优惠券模版状态
     */
    public List<CouponTemplateStateResp> getCouponTemplateStateInfo(){
        Response<List<CouponTemplateStateResp>> response = couponCommonDubboService.getCouponTemplateStateInfo();
        return Response.getValidateData(response);
    }

    /**
     * 来源客户端信息
     */
    public List<CouponSourceClientResp> getCouponSourceClientInfo(){
        Response<List<CouponSourceClientResp>> response = couponCommonDubboService.getCouponSourceClientInfo();
        return Response.getValidateData(response);
    }

    /**
     * 券值类型信息
     */
    public List<CouponValueTypeResp> getCouponValueTypeInfo(){
        Response<List<CouponValueTypeResp>> response = couponCommonDubboService.getCouponValueTypeInfo();
        return Response.getValidateData(response);
    }

    /**
     * 入账项目类型
     */
    public List<CouponPostingTypeResp> getCouponPostingTypeInfo(){
        Response<List<CouponPostingTypeResp>> response = couponCommonDubboService.getCouponPostingTypeInfo();
        return Response.getValidateData(response);
    }

    /**
     * 核销方式
     */
    public List<CouponRedeemTypeResp> getCouponRedeemTypeInfo(){
        Response<List<CouponRedeemTypeResp>> response = couponCommonDubboService.getCouponRedeemTypeInfo();
        return Response.getValidateData(response);
    }

}
