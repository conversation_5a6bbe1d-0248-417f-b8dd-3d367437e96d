package com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.spm.dubbo.enums.ActivityGearTypeEnum;
import lombok.Data;

import java.util.List;

/**
 * 活动档位
 * @Description
 * <AUTHOR>
 * @Date 2024-12-13 11:32
 */
@Data
public class ActivityGearsDto {

    /**
     * 档位类型
     */
    @LegalEnum(target = ActivityGearTypeEnum.class, message = "档位类型不合法",methodName = "getCode")
    private String gearType;
    /**
     * 虚拟载体档位
     */
    private List<BaseGearInfoDto> carrierDtoList;


}
