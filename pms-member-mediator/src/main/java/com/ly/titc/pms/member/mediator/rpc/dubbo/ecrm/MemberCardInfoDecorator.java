package com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm;

import com.alibaba.fastjson.JSON;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.BaseReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.ListMemberCardReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.*;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.*;
import com.ly.titc.pms.ecrm.dubbo.interfaces.MemberCardConfigDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @title: MemberCardInfoDecorator
 * @projectName pms-member
 * @description: 会员卡服务
 * @date 2023/11/16 15:49
 */
@Slf4j
@Component
public class MemberCardInfoDecorator {

    @DubboReference(group = "${ecrm-dsf-group}", check = false)
    private MemberCardConfigDubboService memberCardConfigDubboService;


    /**
     * 根据会员卡ID获取会员卡信息
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     * @return
     */
    public MemberCardConfigResp getMemberCardConfigInfo(Integer masterType, String masterCode, Long cardId){
        GetMemberCardConfigReq req = new GetMemberCardConfigReq();
        req.setMasterType(masterType);
        req.setMasterCode(masterCode);
        req.setCardId(cardId);
        req.setTrackingId(TraceNoUtil.getTraceNo());
        return Response.getValidateData(memberCardConfigDubboService.getCardConfigAndDetail(req));
    }


    /**
     * 保存会员卡模板信息
     *
     * @param req 请求体
     * @return id
     */
    public Long saveMemberCardConfig(SaveCardConfigReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.saveMemberCardConfig(req));
    }

    /**
     * 删除会员卡
     */
    public String deleteMemberCardConfig(DeleteBaseReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.deleteMemberCardConfig(req));
    }

    /**
     * 保存会员卡升级规则
     *
     * @param req 请求体
     * @return id
     */
    public Long saveCardLevelUpgradeRule(SaveCardLevelUpgradeRuleReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.saveCardLevelUpgradeRule(req));
    }

    /**
     * 保存会员卡降级规则
     *
     * @param req 请求体
     * @return id
     */
    public Long saveCardLevelRelegationRule(SaveCardLevelRelegationRuleReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.saveCardLevelRelegationRule(req));
    }

    /**
     * 保存会员卡等级
     *
     * @param req 请求体
     * @return id
     */
    public Long saveCardLevelConfig(SaveCardLevelConfigInfoReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.saveCardLevelConfig(req));
    }

    /**
     * 分页查询会员等级
     *
     * @param req
     * @return
     */
    public Pageable<MemberCardLevelConfigInfoResp> pageCardLevelConfig(PageMemberCardLevelConfigInfoReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.pageCardLevelConfig(req));
    }

    /**
     * 删除会员卡等级
     *
     * @param req
     * @return
     */
    public String deleteCardLevelConfig(DeleteMemberCardLevelConfigReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.deleteCardLevelConfig(req));
    }

    /**
     * 启停操作会员卡等级
     */
    public String actionCardLevel(ActionBaseReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.actionCardLevel(req));
    }

    /**
     * 根据归属查询会员卡模板
     *
     * @return 会员卡模板
     */
    public List<MemberCardConfigResp> listCardConfig(Integer masterType, String masterCode, String trackingId) {
        ListMemberCardConfigReq req = new ListMemberCardConfigReq();
        req.setMasterType(masterType);
        req.setMasterCode(masterCode);
        req.setTrackingId(trackingId);
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.listCardConfig(req));
    }

    public MemberCardConfigResp getById(Long id, String trackingId) {
        GetDetailBaseReq req = new GetDetailBaseReq();
        req.setId(id);
        req.setTrackingId(trackingId);
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.getCardConfig(req));
    }


    /**
     * 根据卡和等级查询升级规则
     *
     * @param req 请求体
     * @return 升级规则
     */
    public MemberCardLevelUpgradeRuleResp getCardLevelUpgradeRule(GetCardLevelUpgradeRuleReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.getCardLevelUpgradeRule(req));
    }

    /**
     * 根据卡和等级查询降级规则
     *
     * @param req 请求体
     * @return 降级规则
     */
    public MemberCardLevelRelegationRuleResp getCardLevelRelegationRule(GetCardLevelRelegationRuleReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.getCardLevelRelegationRule(req));
    }

    /**
     * 分页查询会员卡升级规则
     *
     * @param req req
     * @return 分页结果
     */
    public Pageable<MemberCardLevelUpgradeRuleResp> pageUpgradeRule(PageUpgradeRuleReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.pageUpgradeRule(req));
    }

    /**
     * 升级规则详情
     *
     * @param id         id
     * @param trackingId trace id
     * @return
     */
    public MemberCardLevelUpgradeRuleResp getUpgradeRule(Long id, String trackingId) {
        log.info("请求参数：{}", id);
        GetDetailBaseReq req = new GetDetailBaseReq();
        req.setId(id);
        req.setTrackingId(trackingId);
        return Response.getValidateData(memberCardConfigDubboService.getUpgradeRule(req));
    }

    /**
     * 分页查询会员卡降级规则
     *
     * @param req req
     * @return 分页结果
     */
    public Pageable<MemberCardLevelRelegationRuleResp> pageRelegationRule(PageRelegationRuleReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.pageRelegationRule(req));
    }

    /**
     * 保级规则详情
     *
     * @param id
     * @param trackingId
     * @return
     */
    public MemberCardLevelRelegationRuleResp getRelegationRule(Long id, String trackingId) {
        GetDetailBaseReq req = new GetDetailBaseReq();
        req.setId(id);
        req.setTrackingId(trackingId);
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.getRelegationRule(req));
    }

    /**
     * 分页查询标签
     */
    public Pageable<MemberTagConfigInfoResp> pageTagConfig(PageMemberTagConfigInfoReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.pageTagConfig(req));
    }

    /**
     * 查询标签信息
     */
    public List<MemberTagConfigInfoResp> listTagConfig(Integer masterType, String masterCode, List<Long> tagIdList, String trackingId) {
        ListMemberTagConfigInfoReq req = new ListMemberTagConfigInfoReq();
        req.setMasterType(masterType);
        req.setMasterCode(masterCode);
        req.setTrackingId(trackingId);
        req.setTagIdList(tagIdList);
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.listTagConfig(req));
    }

    /**
     * 查询标签列表
     */
    public List<MemberTagConfigInfoResp> listTagConfig(Integer masterType, String masterCode, String name, String trackingId) {
        ListMemberTagConfigInfoReq req = new ListMemberTagConfigInfoReq();
        req.setMasterType(masterType);
        req.setMasterCode(masterCode);
        req.setTrackingId(trackingId);
        req.setName(name);
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.listTagConfig(req));
    }

    /**
     * 查询标签详情
     *
     * @param id         id
     * @param trackingId trackingId
     * @return 详情
     */
    public MemberTagConfigInfoResp getTagConfig(Long id, String trackingId) {
        GetDetailBaseReq req = new GetDetailBaseReq();
        req.setId(id);
        req.setTrackingId(trackingId);
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.getTagConfig(req));
    }

    /**
     * 保存标签
     *
     * @param req 请求体
     * @return id
     */
    public Long saveTagConfig(SaveMemberTagConfigReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.saveTagConfig(req));
    }

    /**
     * 删除标签
     *
     * @param id         id
     * @param operator   操作人
     * @param trackingId trackingId
     */
    public String deleteTagConfig(Long id, String operator, String trackingId) {
        DeleteBaseReq req = new DeleteBaseReq();
        req.setId(id);
        req.setOperator(operator);
        req.setTrackingId(trackingId);
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.deleteTagConfig(req));
    }

    /**
     * 保存会员卡销售规则
     *
     * @param req req
     * @return id
     */
    public Long saveMemberCardSaleRule(SaveMemberCardSaleRuleReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(memberCardConfigDubboService.saveMemberCardSaleRule(req));
    }


    /**
     * 查询会员卡等级列表
     *
     * @param cardIdList cardIdList
     * @param trackingId trackingId
     * @return 会员卡等级列表
     */
    public List<MemberCardLevelConfigInfoResp> listMemberCardLevel(List<Long> cardIdList, String trackingId) {
        ListMemberCardLevelReq req = new ListMemberCardLevelReq();
        req.setCardIdList(cardIdList);
        req.setTrackingId(trackingId);
        return Response.getValidateData(memberCardConfigDubboService.listMemberCardLevel(req));
    }

    /**
     * 查询会员卡升级规则列表
     *
     * @param cardIdList cardIdList
     * @param trackingId trackingId
     * @return
     */
    public List<MemberCardLevelUpgradeRuleResp> listCardLevelUpgradeRule(List<Long> cardIdList, String trackingId) {
        ListCardLevelUpgradeRuleReq req = new ListCardLevelUpgradeRuleReq();
        req.setCardIdList(cardIdList);
        req.setTrackingId(trackingId);
        return Response.getValidateData(memberCardConfigDubboService.listCardLevelUpgradeRule(req));
    }

    public List<MemberCardLevelUpgradeRuleResp> listAllValidCardLevelUpgradeRule(Integer state, String trackingId) {
        ListCardLevelUpgradeRuleReq req = new ListCardLevelUpgradeRuleReq();
        req.setState(state);
        req.setTrackingId(trackingId);
        return Response.getValidateData(memberCardConfigDubboService.listCardLevelUpgradeRule(req));
    }

    /**
     * 查询会员卡降级规则列表
     *
     * @param cardIdList cardIdList
     * @param trackingId trackingId
     * @return 降级规则列表
     */
    public List<MemberCardLevelRelegationRuleResp> listCardLevelRelegationRule(List<Long> cardIdList, String trackingId) {
        ListCardLevelRelegationRuleReq req = new ListCardLevelRelegationRuleReq();
        req.setCardIdList(cardIdList);
        req.setTrackingId(trackingId);
        return Response.getValidateData(memberCardConfigDubboService.listCardLevelRelegationRule(req));
    }

    public List<MemberCardLevelRelegationRuleResp> listAllValidCardLevelRelegationRule(Integer state, String trackingId) {
        ListCardLevelRelegationRuleReq req = new ListCardLevelRelegationRuleReq();
        req.setState(state);
        req.setTrackingId(trackingId);
        return Response.getValidateData(memberCardConfigDubboService.listCardLevelRelegationRule(req));
    }

    /**
     * 查询默认会员卡
     *
     * @param masterCode 主体code
     * @param trackingId trackingId
     * @param masterType
     * @return 默认卡详情
     */
    public MemberCardConfigResp getDefaultMemberCardConfigInfo(String masterCode, String trackingId, Integer masterType) {
        GetDefaultCardReq req = new GetDefaultCardReq();
        req.setMasterType(masterType);
        req.setMasterCode(masterCode);
        req.setTrackingId(trackingId);
        return Response.getValidateData(memberCardConfigDubboService.getDefaultCard(req));
    }

    /**
     * 删除升级规则
     *
     * @param id
     * @param trackingId
     * @param operator
     */
    public void deleteCardLevelUpgradeRule(Long id, String trackingId, String operator) {
        DeleteBaseReq req = new DeleteBaseReq();
        req.setId(id);
        req.setTrackingId(trackingId);
        req.setOperator(operator);
        Response.getValidateData(memberCardConfigDubboService.deleteCardLevelUpgradeRule(req));
    }

    /**
     * 删除保级规则
     *
     * @param id
     * @param trackingId
     * @param operator
     */
    public void deleteCardLevelRelegationRule(Long id, String trackingId, String operator) {
        DeleteBaseReq req = new DeleteBaseReq();
        req.setId(id);
        req.setTrackingId(trackingId);
        req.setOperator(operator);
        Response.getValidateData(memberCardConfigDubboService.deleteCardLevelRelegationRule(req));
    }

    /**
     * 启停升级规则
     *
     * @param id
     * @param state
     * @param trackingId
     * @param operator
     */
    public void actionCardLevelUpgradeRule(Long id, Integer state, String trackingId, String operator) {
        ActionBaseReq req = new ActionBaseReq();
        req.setId(id);
        req.setState(state);
        req.setTrackingId(trackingId);
        req.setOperator(operator);
        Response.getValidateData(memberCardConfigDubboService.actionCardLevelUpgradeRule(req));
    }

    /**
     * 启停降级规则
     *
     * @param id
     * @param state
     * @param trackingId
     * @param operator
     */
    public void actionCardLevelRelegationRule(Long id, Integer state, String trackingId, String operator) {
        ActionBaseReq req = new ActionBaseReq();
        req.setId(id);
        req.setState(state);
        req.setTrackingId(trackingId);
        req.setOperator(operator);
        Response.getValidateData(memberCardConfigDubboService.actionCardLevelRelegationRule(req));
    }

    public MemberCardLevelConfigInfoResp getCardLevelConfig(Long id, String trackingId) {
        GetDetailBaseReq req = new GetDetailBaseReq();
        req.setId(id);
        req.setTrackingId(trackingId);
        return Response.getValidateData(memberCardConfigDubboService.getCardLevelConfig(req));
    }

    public List<MemberTagConfigInfoResp> listAllTags(String trackingId) {
        BaseReq req = new BaseReq();
        req.setTrackingId(trackingId);
        return Response.getValidateData(memberCardConfigDubboService.listAllTagConfigList(req));
    }

    public void init(Integer masterType, String masterCode, String trackingId) {
        InitReq req = new InitReq();
        req.setMasterType(masterType);
        req.setMasterCode(masterCode);
        req.setTrackingId(trackingId);
        memberCardConfigDubboService.init(req);
    }

    public MemberRelatedConfigResp getMemberRelatedConfig(Integer masterType, String masterCode, Integer type, String trackingId) {
        GetMemberRelatedConfigReq req = new GetMemberRelatedConfigReq();
        req.setMasterType(masterType);
        req.setMasterCode(masterCode);
        req.setType(type);
        req.setTrackingId(trackingId);
        return Response.getValidateData(memberCardConfigDubboService.getMemberRelatedConfig(req));
    }

    public void saveMemberRelatedConfig(Integer masterType, String masterCode, List<String> params, String trackingId, String operator, Long id, Integer type) {
        SaveMemberRelatedConfigReq req = new SaveMemberRelatedConfigReq();
        req.setMasterType(masterType);
        req.setMasterCode(masterCode);
        req.setContent(JSON.toJSONString(params));
        req.setTrackingId(trackingId);
        req.setOperator(operator);
        req.setId(id);
        req.setType(type);
        Response.getValidateData(memberCardConfigDubboService.saveMemberRelatedConfig(req));
    }

    public List<MemberCardConfigResp> listMemberCard(List<Long> cardIdList, String trackingId) {
        ListMemberCardReq req = new ListMemberCardReq();
        req.setTrackingId(trackingId);
        req.setIdList(cardIdList);
        return Response.getValidateData(memberCardConfigDubboService.listMemberCard(req));
    }

}
