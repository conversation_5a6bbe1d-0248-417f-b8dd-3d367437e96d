package com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-1-3 17:59
 */
@Data
@Accessors(chain = true)
public class QueryMemberActiveActivityDto {

    private Boolean isMember;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 平台渠道
     */
    private String platformChannel;

    /**
     * 集团编号
     */
    private String blocCode;

    /**
     * 活动关注事件类型不能为空
     */
    private String eventType;

    /**
     * 事件来源
     */
    private String scopeSource;

    /**
     * 会员卡ID
     */
    private String cardId;
    /**
     * 当前用户会员等级
     */
    private String memberLevel;



}
