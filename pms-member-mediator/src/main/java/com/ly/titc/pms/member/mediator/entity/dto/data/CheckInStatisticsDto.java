package com.ly.titc.pms.member.mediator.entity.dto.data;

import com.ly.titc.springboot.dcdb.dal.core.annotation.PrimaryKey;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * @Author：rui
 * @name：CheckInStatisticsDto
 * @Date：2024-12-12 11:45
 * @Filename：CheckInStatisticsDto
 */
@Data
public class CheckInStatisticsDto {

    /**
     * 会员编号
     */
    @PrimaryKey(column = "member_no", value = 1)
    private String memberNo;

    /**
     * 总入住次数
     */
    private int checkInCount;

    /**
     * 总入住间夜
     */
    private int checkInNights;

    /**
     * 总消费金额
     */
    private int expenseAmount;

    /**
     * 间夜均价
     */
    private BigDecimal averagePrice = BigDecimal.ZERO;

    /**
     * 上一次房价
     */
    private BigDecimal lastPrice = BigDecimal.ZERO;

    /**
     * 上一次离店时间
     */
    private String lastCheckOutDate;

    /**
     * 创建时间
     */
    private LocalDateTime gmtCreate;

    /**
     * 修改时间
     */
    private LocalDateTime gmtModified;
}
