package com.ly.titc.pms.member.mediator.handler.pay;

import com.ly.titc.cashier.dubbo.enums.PayChannelEnum;
import com.ly.titc.common.util.LocalDateTimeUtil;
import com.ly.titc.pms.account.dubbo.enums.pay.PayTradeStateEnum;
import com.ly.titc.pms.ecrm.dubbo.enums.StateEnum;
import com.ly.titc.pms.member.biz.MemberOrderPayInfoBiz;
import com.ly.titc.pms.member.com.enums.RouteEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import com.ly.titc.pms.member.mediator.converter.MemberOrderMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import com.ly.titc.pms.member.service.MemberOrderService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-9-11 19:47
 */
@Component
@Slf4j
public class OfflinePayHandler  extends AbstractPayHandler{
    @Resource
    private MemberOrderMedConverter converter;

    @Resource
    private MemberOrderService orderService;
    @Resource
    private MemberOrderPayInfoBiz payInfoBiz;

    /**
     * 单个支付
     */
    @Override
    public PayOrderResultDto pay(PayOrderDto dto) {
//        MemberOrderPayInfo payInfo =  payInfoBiz.getByPayNo(dto.getMemberOrderNo(), dto.getMemberOrderPayNo());
        PayOrderResultDto resultDto = converter.convertResult(dto);
        resultDto.setPayChannel(PayChannelEnum.CASH.getCode());
        resultDto.setIsOnlinePay(StateEnum.NO_VALID.getCode());
        resultDto.setPayedTime(LocalDateTimeUtil.now().toString());
        resultDto.setPayState(PayTradeStateEnum.SUCCESS.getState());
        return resultDto;
    }



    /**
     * 查询单条支付结果
     * @param dto
     * @return
     */
    @Override
    public PayOrderResultDto getPayState(GetPayStateDto dto){
        MemberOrderPayInfo payInfo =  payInfoBiz.getByPayNo(dto.getMemberOrderNo(), dto.getMemberOrderPayNo());
        return converter.convertPayInfo(payInfo);
    }

    @Override
    public String getRoute() {
        return RouteEnum.OFFLINE.getRoute();
    }
}
