package com.ly.titc.pms.member.mediator.service.impl;

import com.ly.titc.cashier.dubbo.enums.CashierHotelScopeEnum;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.enums.MasterTypeEnum;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.mdm.entity.request.selector.ListSelectorComponentInfoReq;
import com.ly.titc.mdm.entity.request.selector.SaveSelectorComponentReq;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.mdm.entity.response.hotel.SelectHotelResp;
import com.ly.titc.mdm.entity.response.selector.SelectorComponentInfoResp;
import com.ly.titc.mdm.enums.DBKeyEnum;
import com.ly.titc.mdm.enums.HotelOpenStateEnum;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.usageRule.*;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberStoreUsageRuleResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberUsageRuleSaveResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.usageRule.MemberUsageRuleSaveResultResp;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.mediator.converter.HotelSelectorMedConverter;
import com.ly.titc.pms.member.mediator.converter.MemberUsageMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.BaseDto;
import com.ly.titc.pms.member.mediator.entity.dto.usage.*;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.SelectorComponentDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberStoreUsageDecorator;
import com.ly.titc.pms.member.mediator.service.MemberStoreUsageMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-12 11:49
 */
@Slf4j
@Service
public class MemberStoreUsageMedServiceImpl implements MemberStoreUsageMedService {
    @Resource
    private MemberStoreUsageDecorator storeUsageDecorator;
    @Resource(type = HotelDecorator.class)
    private HotelDecorator hotelDecorator;
    @Resource
    private MemberUsageMedConverter medConverter;
    @Resource
    private HotelSelectorMedConverter selectorMedConverter;

    @Resource
    private SelectorComponentDecorator componentDecorator;

    @Override
    public Pageable<MemberStoreUsageRuleDto> page(QueryMemberMasterUsageDto dto) {
        QueryMemberMasterUsageReq req =medConverter.convert(dto);
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(dto.getBlocCode());
        Pageable<MemberStoreUsageRuleResp> resp =  storeUsageDecorator.page(req);
//        List<SelectHotelResp> respList=  hotelDecorator.selectHotels(dto.getBlocCode(),null,"",null, dto.getTrackingId());
//        Map<Long,SelectHotelResp> hotelRespMap =  respList.stream().collect(Collectors.toMap(SelectHotelResp::getHotelVid, Function.identity()));
        List<Long> ruleIds = resp.getDatas().stream().map(MemberStoreUsageRuleResp::getId).collect(Collectors.toList());
        ListSelectorComponentInfoReq  infoReq = selectorMedConverter.convert(ruleIds, DBKeyEnum.TETitcPmsECRM.getCode());
        List<SelectorComponentInfoResp> componentInfoResps =  componentDecorator.listSelector(infoReq);
        Map<String,ScopeHotelsDto> hotelsDtoMap =  selectorMedConverter.convert(componentInfoResps);
        return PageableUtil.convert(resp, d->medConverter.convert(d,hotelsDtoMap));
    }

    @Override
    public List<MemberUsageRuleSaveResultDto> save(MemberStoreUsageRuleConfigSaveDto dto) {
        MemberStoreUsageRuleConfigSaveReq req = medConverter.convert(dto);
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(dto.getBlocCode());
        List<HotelBaseInfoResp> respList=  hotelDecorator.listHotelBaseInfos(dto.getBlocCode(),null, dto.getTrackingId());
        if(dto.getScopeSources().contains(ScopeSourceEnum.HOTEL.getCode())){
            if(dto.getScopeHotelRange().equals(CashierHotelScopeEnum.ALL.getState())) {
                req.setScopeHotelCodes(respList.stream().map(h -> {
                    return String.valueOf(h.getHotelVid());
                }).collect(Collectors.toList()));
            }else{
                List<HotelBaseInfoResp>  infoResps=  componentDecorator.filterHotel(respList,dto.getApplicableHotelsDto());
                if(CollectionUtils.isEmpty(infoResps)){
                    throw  new ServiceException(RespCodeEnum.NO_HOTEL);
                }
                req.setScopeHotelCodes(infoResps.stream().map(h -> {
                    return String.valueOf(h.getHotelVid());
                }).collect(Collectors.toList()));
            }
        }
        MemberUsageRuleSaveResp resp =  storeUsageDecorator.save(req);
        //指定门店保存组件选择器
        if(CollectionUtils.isEmpty(resp.getRepeatedList())) {
            if (dto.getScopeHotelRange().equals(CashierHotelScopeEnum.SOME.getState())) {
                Long ruleId = resp.getRuleId();
                SaveSelectorComponentReq componentReq = selectorMedConverter.convert(dto.getApplicableHotelsDto(), String.valueOf(ruleId), DBKeyEnum.TETitcPmsECRM.getCode());
                componentDecorator.saveSelector(componentReq);
            }
        }

        Map<Long,HotelBaseInfoResp> hotelRespMap =  respList.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelVid, Function.identity()));
        return medConverter.convert(resp.getRepeatedList(),hotelRespMap);
    }

    @Override
    public Boolean updateState(UpdateMemberUsageStateDto dto) {
        UpdateMemberUsageStateReq req = medConverter.convert(dto);
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(dto.getBlocCode());
        return storeUsageDecorator.updateState(req);
    }

    @Override
    public Boolean delete(DeleteMemberUsageDto dto) {
        DeleteMemberUsageReq req = medConverter.convert(dto);
        req.setMasterType(MasterTypeEnum.BLOC.getType());
        req.setMasterCode(dto.getBlocCode());
        return  storeUsageDecorator.delete(req);
    }

    @Override
    public List<MemberUsageRuleSaveResultDto> remind(BaseDto dto) {
        //获取全部的酒店信息
        List<SelectHotelResp> respList=  hotelDecorator.selectHotels(dto.getBlocCode(),null,"",null, dto.getTrackingId());
        //过滤掉未知 筹建中，已停业的状态
        List<SelectHotelResp> hotelResps =  respList.stream().filter(resp -> {return (resp.getOpenState().equals(HotelOpenStateEnum.SOFT_OPENING.getOpenState()) ||
                resp.getOpenState().equals(HotelOpenStateEnum.OPENING.getOpenState())
        );}).collect(Collectors.toList());
        List<MemberUsageRuleSaveResultDto> allNeedConfigs = medConverter.convertConfig(hotelResps);
        List<MemberUsageRuleSaveResultResp> resps =  storeUsageDecorator.listBlocScopeUsageRule(medConverter.convert(dto));
        List<MemberUsageRuleSaveResultDto> unConfigs = new ArrayList<>();

        allNeedConfigs.forEach(config -> {
            Boolean isConfig= resps.stream().anyMatch(resp -> {
                if(config.getScopeSource().equals(ScopeSourceEnum.BLOC.getCode())){
                    return config.getScopePlatformChannel().equals(resp.getScopePlatformChannel());
                }else if(config.getScopeSource().equals(ScopeSourceEnum.HOTEL.getCode())){
                    return config.getScopePlatformChannel().equals(resp.getScopePlatformChannel())
                            && config.getScopeHotelCode().equals(resp.getScopeHotelCode());
                }

                return false;
            });
            if(!isConfig){
                unConfigs.add(config);
            }
        });
        return unConfigs;
    }
}
