package com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm;

import com.ly.titc.common.entity.Response;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.pms.ecrm.dubbo.entity.BaseMasterReq;
import com.ly.titc.pms.ecrm.dubbo.entity.request.MemberPointSysConfigReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.MemberPointSysConfigResp;
import com.ly.titc.pms.ecrm.dubbo.enums.MemberDateUnitEnum;
import com.ly.titc.pms.ecrm.dubbo.interfaces.MemberSysConfigDubboService;
import com.ly.titc.pms.member.com.constant.SystemConstant;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.time.LocalDate;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-24 22:48
 */
@Slf4j
@Component
public class MemberSysConfigDecorator {
    @DubboReference(group = "${ecrm-dsf-group}", check = false)

    private MemberSysConfigDubboService configDubboService;

    /**
     * 保存积分设置
     */
    public boolean savePointConfig(MemberPointSysConfigReq req){
        Response<Boolean> response= configDubboService.savePointConfig(req);
        return Response.getValidateData(response);
    }

    /**
     * 获取积分设置
     */
    public MemberPointSysConfigResp getPointConfig(BaseMasterReq req) {
        Response<MemberPointSysConfigResp> response = configDubboService.getPointConfig(req);
        return Response.getValidateData(response);
    }

    /**
     * 根据当前时间计算积分的过期时间
     */
    public String getPointExpireDate(BaseMasterReq req) {
        LocalDate now = LocalDate.now();
        MemberPointSysConfigResp resp=  getPointConfig(req);
        //如果是长期有效
        if(resp.getPointLimitLong()==1){
            return SystemConstant.PERPETUAL_EFFECT_DATE;
        }else{
            if(resp.getPointLimit()<=0){
                throw new ServiceException("积分过期时间设置错误", RespCodeEnum.CODE_400.getCode());
            }
            if(resp.getPointLimitUnit().equals(MemberDateUnitEnum.YEAR.getCode())){
                return now.plusYears(resp.getPointLimit()).toString();
            }else if(resp.getPointLimitUnit().equals(MemberDateUnitEnum.MONTH.getCode())){
                return now.plusMonths(resp.getPointLimit()).toString();
            }else{
                return now.plusDays(resp.getPointLimit()).toString();
            }
        }

    }
}
