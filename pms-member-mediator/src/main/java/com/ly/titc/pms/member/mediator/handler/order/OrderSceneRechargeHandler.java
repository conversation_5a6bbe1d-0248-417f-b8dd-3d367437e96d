package com.ly.titc.pms.member.mediator.handler.order;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.common.enums.ScopeSourceEnum;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.MemberStoreRechargeReq;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.MemberStoreRechargeRollBackReq;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreRecordOPResultResp;
import com.ly.titc.pms.member.biz.MemberOrderDetailInfoBiz;
import com.ly.titc.pms.member.biz.MemberOrderInfoBiz;
import com.ly.titc.pms.member.biz.MemberOrderRechargeSceneInfoBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.constant.TurboMqTopic;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRechargeSceneInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.converter.MemberOrderMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.order.CreateOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.OrderPostResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.recharge.MemberStoreRechargeDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.MemberStoreOpDecorator;
import com.ly.titc.pms.spm.dubbo.enums.ActivityDisplayTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.ReceiverTypeEnum;
import com.ly.titc.pms.spm.dubbo.mq.message.RollbackEventActivityBizMsg;
import com.ly.titc.pms.spm.dubbo.mq.message.TriggerEventActivityBizMsg;
import com.ly.titc.springboot.mq.producer.TurboMQProducer;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.SPM_GRANT;
import static com.ly.titc.pms.member.com.constant.TurboMqTopicTag.SPM_ROLLBACK;
import static com.ly.titc.pms.member.com.enums.RespCodeEnum.MEMBER_10011;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-26 17:41
 */
@Slf4j
@Component
public class OrderSceneRechargeHandler extends AbstractOrderSceneHandler<MemberStoreRechargeDto> {

    @Resource
    private MemberOrderRechargeSceneInfoBiz rechargeSceneInfoBiz;
    @Resource
    private MemberOrderMedConverter orderMedConverter;
    @Resource
    private MemberOrderDetailInfoBiz detailInfoBiz;
    @Resource
    private MemberOrderInfoBiz memberOrderInfoBiz;
    @Resource
    private MemberStoreOpDecorator rechargeDecorator;
    @Resource(type = TurboMQProducer.class)
    private TurboMQProducer producer;


    @Override
    public CreateOrderDto<MemberStoreRechargeDto> doPreCheck(CreateOrderDto<MemberStoreRechargeDto> dto) {
        return dto;
    }

    @Override
    public String doGetLockKey(MemberStoreRechargeDto dto) {
        return CommonConstant.CREATE_ORDER_LOCK_KEY_PREFIX + String.format("%s_%s", getScene(), dto.getMemberNo());

    }

    @Override
    public void saveSceneOrder(CreateOrderDto<MemberStoreRechargeDto> dto) {
        MemberStoreRechargeDto rechargeDto = dto.getMemberSceneNoteDto();
        rechargeDto.setMasterCode(dto.getMasterCode());
        MemberOrderRechargeSceneInfo sceneInfo = orderMedConverter.convert(rechargeDto);
        sceneInfo.setMemberOrderNo(dto.getMemberOrderNo());
        rechargeSceneInfoBiz.add(sceneInfo);
    }

    @Override
    public OrderPostResultDto postHandle(MemberOrderInfo orderInfo) {
        //支付成功后置处理，调用会员资产服务充值
        String memberOrderNo = orderInfo.getMemberOrderNo();
        MemberOrderInfo memberOrderInfo = memberOrderInfoBiz.getByOrderNo(memberOrderNo);
        MemberOrderDetailInfo detailInfo = detailInfoBiz.getByOrderNo(memberOrderNo);
        MemberOrderRechargeSceneInfo rechargeSceneInfo = rechargeSceneInfoBiz.getByOrderNo(memberOrderNo);
        MemberStoreRechargeReq req = orderMedConverter.convert(rechargeSceneInfo, memberOrderInfo, detailInfo);
        rechargeDecorator.recharge(req);

        OrderPostResultDto resultDto = new OrderPostResultDto();
        resultDto.setMemberNo(rechargeSceneInfo.getMemberNo());
        resultDto.setMemberOrderNo(memberOrderNo);
        // 发放礼包
        try {
            TriggerEventActivityBizMsg msg = buildSendGIftMsg(orderInfo, detailInfo);
            String msgJson = JSONObject.toJSONString(msg);
            log.info("储值活动发放礼包,msg:{}", msgJson);
            producer.sendMsgWithTag(TurboMqTopic.PMS_SPM_EVENT_TOPIC, SPM_GRANT, msgJson);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        return resultDto;

    }

    @Override
    public void refundHandle(MemberOrderRefundInfo orderInfo) {
        MemberStoreRechargeRollBackReq rollBackReq = orderMedConverter.convert(orderInfo, TraceNoUtil.getTraceNo());
        rollBackReq.setOperator(orderInfo.getModifyUser());
        rollBackReq.setTradeNo(orderInfo.getMemberOrderNo());
        MemberStoreRecordOPResultResp memberStoreRecordOPResultResp = rechargeDecorator.rechargeRollback(rollBackReq);
        log.info("储值已会退，recordNo:{}", memberStoreRecordOPResultResp.getRecordNo());

        // 礼包退回
        RollbackEventActivityBizMsg msg = new RollbackEventActivityBizMsg();
        msg.setTriggerBizNo(orderInfo.getMemberOrderNo());
        msg.setTraceId(TraceNoUtil.getTraceNo());
        try {
            producer.sendMsgWithTag(TurboMqTopic.PMS_SPM_EVENT_TOPIC, SPM_ROLLBACK, JSONObject.toJSONString(msg));
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    public String getScene() {
        return MemberSceneEnum.RECHARGE.getScene();
    }

    private static TriggerEventActivityBizMsg buildSendGIftMsg(MemberOrderInfo orderInfo, MemberOrderDetailInfo detailInfo) {
        TriggerEventActivityBizMsg msg = new TriggerEventActivityBizMsg();
        msg.setActivityCode(detailInfo.getActivityCode());
        msg.setMatchCondition(buildMatchCondition(orderInfo));
        msg.setTriggerBizNo(orderInfo.getMemberOrderNo());
        TriggerEventActivityBizMsg.BizParamData bizParamData = new TriggerEventActivityBizMsg.BizParamData();
        bizParamData.setParamName(ActivityDisplayTypeEnum.STORE_GIFT.getGearParamName());
        bizParamData.setParamValue(detailInfo.getGearCode());
        msg.setBizParamData(bizParamData);
        msg.setReceiverType(ReceiverTypeEnum.MEMBER.getCode());
        msg.setReceiverCode(orderInfo.getMemberNo());
        msg.setTraceId(TraceNoUtil.getTraceNo());
        return msg;
    }

    private static TriggerEventActivityBizMsg.MatchCondition buildMatchCondition(MemberOrderInfo orderInfo) {
        TriggerEventActivityBizMsg.MatchCondition matchCondition = new TriggerEventActivityBizMsg.MatchCondition();
        matchCondition.setBlocCode(orderInfo.getBlocCode());
        matchCondition.setHotelCode(orderInfo.getHotelCode());
        matchCondition.setEventType(ActivityDisplayTypeEnum.STORE_GIFT.getCode());
        matchCondition.setPlatformChannel(orderInfo.getPlatformChannel());
        matchCondition.setScopeSource(ScopeSourceEnum.BLOC.getCode());
        return matchCondition;
    }

}
