package com.ly.titc.pms.member.mediator.handler.pay;

import com.alibaba.fastjson.JSON;
import com.ly.titc.cashier.dubbo.entity.request.CashierPayGetOrderDetailReq;
import com.ly.titc.cashier.dubbo.entity.request.onlinePay.CashierOnlinePayReq;
import com.ly.titc.cashier.dubbo.entity.response.CashierPayDetailInfoResp;
import com.ly.titc.cashier.dubbo.entity.response.onlinePay.CashierOnlinePayResp;
import com.ly.titc.cashier.dubbo.enums.CashierBusinessTypeEnum;
import com.ly.titc.pms.account.dubbo.entity.response.pay.AccountPayTradeResp;
import com.ly.titc.pms.member.com.enums.RouteEnum;
import com.ly.titc.pms.member.mediator.converter.MemberOrderMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.order.GetPayStateDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PayOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PayOrderResultDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.cashier.OnlinePayDecorator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;


/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-9-11 19:47
 */
@Component
@Slf4j
public class CashierPayHandler extends AbstractPayHandler {
    @Resource
    private OnlinePayDecorator onlinePayDecorator;

    @Resource
    private MemberOrderMedConverter orderMedConverter;

//    @Resource
//    private AccountPayTradeInfoDecorator tradeInfoDecorator;



    @Override
    public PayOrderResultDto pay(PayOrderDto dto) {
        //todo 根据收银配置选择账户
        //3.参数转换
        CashierOnlinePayReq onlinePayReq = orderMedConverter.convertOnline(dto);
        onlinePayReq.setBusinessType(CashierBusinessTypeEnum.PMSPAY.getType());
        onlinePayReq.setBusinessNote(JSON.toJSONString(dto.getAccountItem()));
        //4.调用收银台支付
        CashierOnlinePayResp resp =  onlinePayDecorator.onlinePay(onlinePayReq);
        //5.处理支付结果
        GetPayStateDto getDto =  orderMedConverter.convert(resp);
        getDto.setMemberOrderNo(dto.getMemberOrderNo());
        getDto.setOperator(dto.getOperator());
        return  getPayState(getDto);
    }



    /**
     * 查询单条支付结果
     */
    @Override
    public PayOrderResultDto getPayState(GetPayStateDto payStateDto){
        //1.参数转换
        CashierPayGetOrderDetailReq req = orderMedConverter.convertGetDetail(payStateDto);
        req.setBusinessType(CashierBusinessTypeEnum.PMSPAY.getType());
        //2.调用收银台查询支付结果
        CashierPayDetailInfoResp detailResp= onlinePayDecorator.getPayInfo(req);
        //3调用账务交易处理
//        SaveAccountPayTradeReq payTradeReq = orderMedConverter.convertPay(detailResp);
        //todo 判断是否入账
//        AccountPayTradeResp tradeResp= tradeInfoDecorator.save(payTradeReq);
        AccountPayTradeResp tradeResp = orderMedConverter.convertTradeResp(detailResp);
        return orderMedConverter.convertResult(tradeResp);

    }

    @Override
    public String getRoute() {
        return RouteEnum.CASHIER.getRoute();
    }
}
