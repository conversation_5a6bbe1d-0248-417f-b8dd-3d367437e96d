package com.ly.titc.pms.member.mediator.rpc.dubbo.cdm;

import com.ly.titc.cdm.dubbo.entity.request.plan.RatePlanListReq;
import com.ly.titc.cdm.dubbo.entity.response.plan.HotelRatePlanListResp;
import com.ly.titc.cdm.dubbo.interfaces.RatePlanInfoDubboService;
import com.ly.titc.common.entity.Response;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;
import java.util.UUID;

/**
 * @Description:
 * @Author: huaizhu.zhong
 * @Date: 2024-12-13
 */
@Slf4j
@Component
public class RatePlanDecorator {

    @DubboReference(group = "${core-dsf-dubbo-group}")
    private RatePlanInfoDubboService ratePlanInfoDubboService;

    public List<HotelRatePlanListResp> listRatePlanByBlocCode(String blocCode,Integer state){
        return this.listRatePlan(blocCode,null,null,state);
    }


    /**
     * 查询方案列表
     */
    public List<HotelRatePlanListResp> listRatePlan(String blocCode,String lkNameOrCode,List<String> hotelCodes,Integer state){
        RatePlanListReq req = new RatePlanListReq();
        req.setLkNameOrCode(lkNameOrCode);
        req.setBlocCode(blocCode);
        req.setHotelCodeList(hotelCodes);
        req.setState(state);
        req.setTrackingId(UUID.randomUUID().toString());
        Response<List<HotelRatePlanListResp>> listResponse = ratePlanInfoDubboService.listRatePlan(req);
        return Response.getValidateData(listResponse);
    }

}
