package com.ly.titc.pms.member.mediator.service.impl;

import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.enums.PlatformChannelEnum;
import com.ly.titc.pms.member.com.lang.TwoTuple;
import com.ly.titc.pms.member.com.utils.ConfigCenterUtil;
import com.ly.titc.pms.member.mediator.converter.ActivityMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity.QueryMemberActiveActivityDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.EventActivityDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.activity.MemberActivityDecorator;
import com.ly.titc.pms.member.mediator.service.ActivityMedService;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.ApplicableCarrierDto;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.QueryEventActivitiesReq;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityResp;
import com.ly.titc.pms.spm.dubbo.enums.ActivityCarrierTypeEnum;
import com.ly.titc.pms.spm.dubbo.enums.ActivityDisplayTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-1-3 17:58
 */
@Slf4j
@Service
public class ActivityMedServiceImpl implements ActivityMedService {
    @Resource
    private EventActivityDecorator activityDecorator;

    @Resource
    private MemberMedService memberMedService;
    @Resource
    private ActivityMedConverter converter;
    @Override
    public EventActivityResp getHighestPriorityEventActivity(QueryMemberActiveActivityDto dto) {
        QueryEventActivitiesReq request = converter.convert(dto);
        if(dto.getEventType().equals(ActivityDisplayTypeEnum.MEMBER_CARD_SALE.getCode()) || dto.getEventType().equals(ActivityDisplayTypeEnum.MEMBER_PAID_UPGRADE.getCode())){
            ApplicableCarrierDto applicableCarrierDto = new ApplicableCarrierDto();
            applicableCarrierDto.setCarrierType(ActivityCarrierTypeEnum.MEMBER_CARD.getCode());
            applicableCarrierDto.setCarrierBizVal(dto.getCardId());
            request.setApplicableCarrierDto(applicableCarrierDto);
        }
        request.setTrackingId(UUID.randomUUID().toString());
        TwoTuple<Integer, String> masterTuple = ConfigCenterUtil.getMasterByBloc(request.getBlocCode());
        if(StringUtils.isNotEmpty(dto.getMemberNo())) {
            MemberDetailDto detailDto= memberMedService.getDetailByMemberNo(masterTuple.getFirst(), masterTuple.getSecond(), dto.getMemberNo());
            QueryEventActivitiesReq.UserInfo userInfo = new QueryEventActivitiesReq.UserInfo();
            userInfo.setMemberPlatformChannel(PlatformChannelEnum.CRM.getPlatformChannel());
            userInfo.setIsMember(dto.getIsMember());
            userInfo.setMemberTags(detailDto.getTagNames());
            List<QueryEventActivitiesReq.MemberCardInfo> memberCardInfos  = new ArrayList<>();
            detailDto.getMemberCardInfos().forEach(cardInfo -> {
                QueryEventActivitiesReq.MemberCardInfo memberCardInfo = new QueryEventActivitiesReq.MemberCardInfo();
                memberCardInfo.setMemberCardCode(cardInfo.getCardId() !=null?cardInfo.getCardId().toString():"");
                memberCardInfo.setMemberLevel(cardInfo.getCardLevel()!=null? cardInfo.getCardLevel().toString() :"");
                memberCardInfos.add(memberCardInfo);
            });
            userInfo.setMemberCardInfos(memberCardInfos);
        }else{
           QueryEventActivitiesReq.UserInfo userInfo = new QueryEventActivitiesReq.UserInfo();
           if(StringUtils.isNotEmpty(dto.getCardId()) || StringUtils.isNotEmpty(dto.getMemberLevel())){
               List<QueryEventActivitiesReq.MemberCardInfo> memberCardInfos = new ArrayList<>();
               QueryEventActivitiesReq.MemberCardInfo memberCardInfo = new QueryEventActivitiesReq.MemberCardInfo();
               memberCardInfo.setMemberCardCode(dto.getCardId());
               memberCardInfo.setMemberLevel(dto.getMemberLevel());
               memberCardInfos.add(memberCardInfo);
               userInfo.setMemberCardInfos(memberCardInfos);
               userInfo.setIsMember(dto.getIsMember());
               request.setUserInfo(userInfo);
           }
        }

        return activityDecorator.getHighestPriorityEventActivity(request);
    }
}
