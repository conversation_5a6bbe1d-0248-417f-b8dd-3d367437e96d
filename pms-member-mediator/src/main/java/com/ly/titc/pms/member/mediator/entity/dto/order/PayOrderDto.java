package com.ly.titc.pms.member.mediator.entity.dto.order;

import lombok.Data;
import lombok.experimental.Accessors;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-27 11:53
 */
@Data
@Accessors(chain = true)
public class PayOrderDto {
    /**
     * 会员订单号
     */
    private String memberOrderNo;

    /**
     * 支付渠道
     */
    private String payChannel;

    /**
     * 支付产品
     */
    private String payProduct;

    /**
     * 售价类型
     */
    private String amountType;

    /**
     * 金额
     */
    private BigDecimal amount;

    /**
     *卡号（银行卡号/会员卡号）
     * 信用卡，银行卡，会员卡支付时传入
     */
    private String cardNo;


    /**
     *扫码支付授权码，设备读取用户的条码或者二维码信息（非pos机场景下必传）
     */
    private String authCode;

    /**
     * 终端ID
     * POS支付场景下必传
     */
    private String termId;
    /**
     * 是否是POS机支付，默认0
     * 1：POS机支付 0：非POS机支付
     */
    private Integer isUsePos =0;
    /**
     * 银行卡类型 POS机支付银卡卡方式支付必传
     * 银行卡类型 unioncard(国内卡)；wildcard(外卡)，预授权交易只支持国内卡刷卡预授权
     */
    private String cardType;

    /**
     * 账务信息
     */
    private PayAccountItemDto accountItem;

    /**
     * 商品描述
     */
    private String goodsDes;

    /**
     * 会员支付申请单号
     */
    private String memberOrderPayNo;

    /**
     * 酒店code
     */
    private String hotelCode;

    /**
     * 集团code
     */
    private String blocCode;

    /**
     * 操作人
     */
    private String operator;
}
