package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.cashier.dubbo.entity.request.CashierRefundDetailReq;
import com.ly.titc.cashier.dubbo.entity.request.CashierRefundReq;
import com.ly.titc.cashier.dubbo.entity.response.CashierRefundDetailResp;
import com.ly.titc.cashier.dubbo.entity.response.CashierRefundResp;
import com.ly.titc.cashier.dubbo.enums.CashierBusinessTypeEnum;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.pms.member.com.enums.OrderRefundStateEnum;
import com.ly.titc.pms.member.com.utils.GenerateUtils;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderPayInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.entity.dto.order.*;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Mappings;

import java.math.BigDecimal;

/**
 * 退款单Converter
 *
 * <AUTHOR>
 * @date 2024/12/12 13:42
 */
@Mapper(componentModel = "spring")
public interface MemberRefundOrderMedConverter {

    default MemberOrderRefundInfo convertRefundOrderInfo(MemberOrderInfo memberOrderInfo,
                                                         MemberOrderDetailInfo memberOrderDetailInfo,
                                                         MemberOrderPayInfo memberOrderPayInfo,
                                                         RefundOrderDto dto){
        MemberOrderRefundInfo memberOrderRefundInfo = new MemberOrderRefundInfo();
        memberOrderRefundInfo.setClubCode(memberOrderDetailInfo.getClubCode());
        memberOrderRefundInfo.setBlocCode(memberOrderDetailInfo.getBlocCode());
        memberOrderRefundInfo.setHotelCode(memberOrderDetailInfo.getHotelCode());
        memberOrderRefundInfo.setMemberRefundNo(GenerateUtils.generateRefundOrderNo());
        memberOrderRefundInfo.setMemberOrderNo(memberOrderInfo.getMemberOrderNo());
        memberOrderRefundInfo.setPlatformChannel(dto.getPlatformChannel());
        memberOrderRefundInfo.setMemberNo(memberOrderInfo.getMemberNo());
        memberOrderRefundInfo.setMasterType(memberOrderInfo.getMasterType());
        memberOrderRefundInfo.setMasterCode(memberOrderInfo.getMasterCode());
        memberOrderRefundInfo.setMemberScene(memberOrderInfo.getMemberScene());
        memberOrderRefundInfo.setRefundState(OrderRefundStateEnum.REFUNDING.getState());
        memberOrderRefundInfo.setRefundAmount(dto.getRefundAmount());
        memberOrderRefundInfo.setReason(dto.getReason());
        memberOrderRefundInfo.setCreateUser(dto.getOperator());
        memberOrderRefundInfo.setModifyUser(dto.getOperator());
        memberOrderRefundInfo.setTermId(dto.getTermId());
        memberOrderRefundInfo.setMemberOrderPayNo(memberOrderPayInfo.getMemberOrderPayNo());
        return memberOrderRefundInfo;
    }

    default CashierRefundReq convertRefundReq(MemberOrderRefundInfo processRefundOrder, MemberOrderPayInfo memberOrderPayInfo){
        CashierRefundReq req = new CashierRefundReq();
        req.setOnlinePayNo(memberOrderPayInfo.getOnlinePayNo());
        req.setTransactionId(memberOrderPayInfo.getTransactionId());
        req.setBusinessNo(processRefundOrder.getMemberRefundNo());
        req.setReason(processRefundOrder.getReason());
        req.setTermId(processRefundOrder.getTermId());
        req.setBlocCode(processRefundOrder.getBlocCode());
        req.setHotelCode(processRefundOrder.getHotelCode());
        req.setOperator(processRefundOrder.getCreateUser());
        req.setBusinessType(CashierBusinessTypeEnum.PMSPAY.getType());
        req.setAmount(processRefundOrder.getRefundAmount());
        return req;
    }

    @Mappings({
            @Mapping(target = "refundPayNo",source = "onlineRefundPayNo"),
            @Mapping(target = "refundTransactionId",source = "transactionId")
    })
    RefundResultDto convertRefundResultDto(MemberOrderRefundInfo processRefundOrder);

    @Mappings({
            @Mapping(target = "refundPayNo",source = "memberOrderRefundInfo.onlineRefundPayNo")
    })
    CashierRefundDetailReq convertRefundDetailReq(MemberOrderRefundInfo memberOrderRefundInfo, String operator);

    CashierRefundResp convertRefundResultResp(CashierRefundDetailResp refundResult);

}
