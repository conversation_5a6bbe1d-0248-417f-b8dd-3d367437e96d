package com.ly.titc.pms.member.mediator.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSONArray;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberTradeConsumeRecordResp;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.mediator.converter.MemberAssetMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberOrderDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.MemberOrderDto;
import com.ly.titc.pms.member.mediator.rpc.dubbo.asset.AssetDecorator;
import com.ly.titc.pms.member.mediator.service.MemberAssetMedService;
import com.ly.titc.pms.spm.dubbo.entity.dto.activity.DiscountBenefitDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberAssetMedServiceImpl
 * @Date：2024-12-5 15:32
 * @Filename：MemberAssetMedServiceImpl
 */
@Slf4j
@Service
public class MemberAssetMedServiceImpl implements MemberAssetMedService {

    @Resource
    private MemberOrderInfoBiz memberOrderInfoBiz;

    @Resource
    private MemberOrderPayInfoBiz orderPayInfoBiz;

    @Resource
    private MemberOrderRefundInfoBiz orderRefundInfoBiz;

    @Resource
    private MemberOrderDetailInfoBiz memberOrderDetailInfoBiz;

    @Resource
    private MemberAssetMedConverter converter;

    @Resource
    private AssetDecorator assetDecorator;

    @Resource
    private MemberOrderRechargeSceneInfoBiz rechargeSceneInfoBiz;


    @Override
    public Pageable<MemberOrderDto> pageMemberStoredRechargeRecord(Integer masterType, String masterCode, String memberNo, Integer pageIndex, Integer pageSize, Integer state, String beginTime, String endTime, String trackingId) {
        Pageable<MemberOrderInfo> page = memberOrderInfoBiz.pageMemberStoredRechargeRecord(masterType, masterCode, memberNo, pageIndex, pageSize, state, beginTime, endTime);
        List<MemberOrderInfo> dataList = page.getDatas();
        if (CollectionUtil.isEmpty(dataList)) {
            return Pageable.empty();
        }
        List<String> tradeNoList = dataList.stream().map(MemberOrderInfo::getMemberOrderNo).collect(Collectors.toList());
        List<MemberOrderRechargeSceneInfo> sceneInfos = rechargeSceneInfoBiz.listByOrderNos(tradeNoList);
        List<MemberTradeConsumeRecordResp> list = assetDecorator.listRechargeConsumeRecord(memberNo, tradeNoList, trackingId);
        return Pageable.convert(page, converter.convert(dataList, list,sceneInfos));
    }

    @Override
    public MemberOrderDetailDto getMemberStoredRechargeRecord(String memberOrderNo) {
        MemberOrderInfo orderInfo = memberOrderInfoBiz.getByOrderNo(memberOrderNo);
        MemberOrderDetailInfo memberOrderDetailInfo = memberOrderDetailInfoBiz.getByOrderNo(memberOrderNo);
        //查询支付信息
        List<MemberOrderPayInfo> orderPayInfos = orderPayInfoBiz.listByOrderNo(memberOrderNo);
        MemberOrderRechargeSceneInfo sceneInfo = rechargeSceneInfoBiz.getByOrderNo(memberOrderNo);
        MemberOrderPayInfo payInfo = null;
        if(CollectionUtil.isNotEmpty(orderPayInfos)){
            //根据创建时间排序
            orderPayInfos.sort(Comparator.comparing(MemberOrderPayInfo::getGmtCreate).reversed());
            payInfo = orderPayInfos.get(0);
        }
        //查询退款信息
        List<MemberOrderRefundInfo> refundInfos = orderRefundInfoBiz.listByMemberOrderNo(orderInfo.getMasterType(),orderInfo.getMasterCode(),memberOrderNo);

        MemberOrderRefundInfo refundInfo = null;
        if(CollectionUtil.isNotEmpty(refundInfos)){
            refundInfos.sort(Comparator.comparing(MemberOrderRefundInfo::getGmtCreate).reversed());
            refundInfo = refundInfos.get(0);
        }

        return converter.convert(orderInfo, memberOrderDetailInfo,payInfo,refundInfo,sceneInfo);
    }
}
