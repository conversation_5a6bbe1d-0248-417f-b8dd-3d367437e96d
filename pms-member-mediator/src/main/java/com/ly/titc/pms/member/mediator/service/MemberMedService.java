package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员Med服务
 *
 * <AUTHOR>
 * @date 2024/10/28 19:50
 */
public interface MemberMedService {

    /**
     * 会员注册
     *
     * @param dto
     * @return
     */
    RegisterMemberResultDto register(RegisterMemberDto dto);

    /**
     * 公共前置校验
     *
     * @param dto
     */
    void registerCheck(RegisterMemberDto dto);

    /**
     * 根据手机号查询会员
     *
     * @param masterType
     * @param masterCode
     * @param mobiles
     * @return
     */
    List<MemberDetailDto> listByMobiles(Integer masterType, String masterCode, List<String> mobiles, Integer state);

    /**
     * 根据证件号查询会员
     *
     * @param masterType
     * @param masterCode
     * @param idType
     * @param idNos
     * @param state
     * @return
     */
    List<MemberDetailDto> listByIdNos(Integer masterType, String masterCode, Integer idType, List<String> idNos, Integer state);

    /**
     * 根据会员卡号查询会员
     *
     * @param masterType
     * @param masterCode
     * @param cardNos
     * @param state
     * @return
     */
    List<MemberDetailDto> listByCardNos(Integer masterType, String masterCode, List<String> cardNos, Integer state);

    /**
     * 根据会员号查询会员信息
     *
     * @param masterType
     * @param masterCode
     * @param memberNo
     * @return
     */
    MemberDetailDto getDetailByMemberNo(Integer masterType, String masterCode, String memberNo);

    /**
     * 根据会员号查询会员信息
     *
     * @param masterType
     * @param masterCode
     * @param memberNo
     * @return
     */
    MemberInfoDto getByMemberNo(Integer masterType, String masterCode, String memberNo);

    /**
     * 根据会员号查询会员信息
     *
     * @param memberNo
     * @return
     */
    MemberInfoDto getByMemberNo(String memberNo);

    /**
     * 根据会员号查询会员信息
     *
     * @param masterType
     * @param masterCode
     * @param memberNos
     * @return
     */
    List<MemberDetailDto> listByMemberNos(Integer masterType, String masterCode, List<String> memberNos);

    /**
     * 分页查询会员（强一致性查询模式）
     *
     * @param pageMemberParamBo
     * @return
     */
    Pageable<MemberDetailDto> pageMemberByStrongMode(PageMemberParamDto pageMemberParamBo);

    /**
     * 分页查询会员（最终一致性查询模式）
     *
     * @param pageMemberParamBo
     * @return
     */
    Pageable<MemberDetailDto> pageMemberByFinalMode(PageMemberParamDto pageMemberParamBo);

    /**
     * 会员注销
     *
     * @param masterType
     * @param masterCode
     * @param memberNo
     */
    void cancelMember(Integer masterType, String masterCode, String memberNo, String operator);

    /**
     * 会员恢复
     * @param masterType
     * @param masterCode
     * @param memberNo
     * @param operator
     */
    void recoverMember(Integer masterType, String masterCode, String memberNo, String operator);

    /**
     * 更新会员信息
     *
     * @param dto
     */
    void updateMember(UpdateMemberInfoDto dto);

    /**
     * 校验会员状态
     *
     * @param memberNo
     */
    void checkMemberState(String memberNo);

    /**
     * 更换密码
     *
     * @param memberNo
     * @param oldPassword
     * @param newPassword
     * @param operator
     */
    void changePassword(String memberNo, String oldPassword, String newPassword, String operator);

    /**
     * 根据手机号查询会员
     *
     * @param masterCode
     * @param masterType
     * @param mobile
     * @param memberNo
     * @return
     */
    MemberInfoDto getByMobile(Integer masterCode, String masterType, String mobile, String memberNo);


    /**
     * 根据手机号查询会员号
     *
     * @param masterType
     * @param masterCode
     * @param mobile
     * @return
     */
    String getMemberNoByMobile(Integer masterType, String masterCode, String mobile);

    /**
     * 发放会员卡
     *
     * @param dto
     * @return
     */
    IssueMemberCardResultDto issueMemberCard(@Valid IssueMemberCardRequestDto dto);
}
