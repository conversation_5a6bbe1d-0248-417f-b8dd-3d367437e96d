package com.ly.titc.pms.member.mediator.rpc.dubbo.asset;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.member.asset.dubbo.entity.request.stored.*;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreConsumeCalResp;
import com.ly.titc.pms.member.asset.dubbo.entity.response.store.MemberStoreRecordOPResultResp;
import com.ly.titc.pms.member.asset.dubbo.interfaces.MemberStoreOpDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-12-30 15:45
 */
@Slf4j
@Service
public class MemberStoreOpDecorator {

    @DubboReference(group = "${asset-dsf-dubbo-group}")
    private MemberStoreOpDubboService storeOpDubboService;

    /**
     * 充值
     */
    public MemberStoreRecordOPResultResp recharge(MemberStoreRechargeReq req){
        Response<MemberStoreRecordOPResultResp> response = storeOpDubboService.recharge(req);
        return Response.getValidateData(response);
    }

    /**
     * 充值后退款
     */
    public MemberStoreRecordOPResultResp rechargeRollback(MemberStoreRechargeRollBackReq req) {
        Response<MemberStoreRecordOPResultResp> response = storeOpDubboService.rechargeRollback(req);
        return Response.getValidateData(response);
    }

    /**
     * 储值消费
     */
    public MemberStoreRecordOPResultResp consumeStore(MemberStoreConsumeReq req) {
        Response<MemberStoreRecordOPResultResp> response = storeOpDubboService.consumeStore(req);
        return Response.getValidateData(response);
    }

    /**
     * 消费金额预计算
     */
    public MemberStoreConsumeCalResp consumeStoreCal(MemberStoreConsumeReq req) {
        Response<MemberStoreConsumeCalResp> response = storeOpDubboService.consumeStoreCal(req);
        return Response.getValidateData(response);
    }

    /**
     * 冻结
     */
    public MemberStoreRecordOPResultResp freeze(MemberStoreFreezeReq req){
        Response<MemberStoreRecordOPResultResp> response =  storeOpDubboService.freeze(req);
        return Response.getValidateData(response);
    }

    /**
     * 解冻
     */
    public MemberStoreRecordOPResultResp unfreeze(UnfreezeConsumeRecordNoReq req){
        Response<MemberStoreRecordOPResultResp> response = storeOpDubboService.unFreeze(req);
        return Response.getValidateData(response);
    }

}
