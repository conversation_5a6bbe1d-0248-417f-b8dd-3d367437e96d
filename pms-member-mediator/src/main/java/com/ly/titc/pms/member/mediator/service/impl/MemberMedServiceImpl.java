package com.ly.titc.pms.member.mediator.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.ly.titc.common.constants.Constant;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.common.util.LocalDateUtil;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.mdm.entity.response.region.RegionResp;
import com.ly.titc.oauth.client.threadlocal.UserThreadHolder;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.BindMemberReq;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.GetByCustomerNoReq;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerDetailInfoResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.member.biz.MemberCardInfoBiz;
import com.ly.titc.pms.member.biz.MemberContactInfoBiz;
import com.ly.titc.pms.member.biz.MemberExtendInfoBiz;
import com.ly.titc.pms.member.biz.MemberInfoBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.IdTypeEnum;
import com.ly.titc.pms.member.com.enums.MemberRegisterCheckEnum;
import com.ly.titc.pms.member.com.enums.MemberStateEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.dal.entity.bo.PageMemberParamBo;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;
import com.ly.titc.pms.member.dubbo.enums.LevelChangeTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MasterTypeEnum;
import com.ly.titc.pms.member.dubbo.enums.MemberEventEnum;
import com.ly.titc.pms.member.entity.wrapper.MemberIssueCardWrapper;
import com.ly.titc.pms.member.entity.wrapper.MemberRegisterWrapper;
import com.ly.titc.pms.member.entity.wrapper.MemberSaveWrapper;
import com.ly.titc.pms.member.mediator.converter.MemberCardMedConverter;
import com.ly.titc.pms.member.mediator.converter.MemberMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.MemberDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.es.member.PageMemberDocumentDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import com.ly.titc.pms.member.mediator.entity.dto.profile.MemberProfileTagInfoDto;
import com.ly.titc.pms.member.mediator.handler.check.AbstractRegisterCheckHandler;
import com.ly.titc.pms.member.mediator.manager.RegisterCheckHandlerManager;
import com.ly.titc.pms.member.mediator.handler.es.ElasticsearchHandler;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.RegionDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.customer.CustomerDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.rpc.dubbo.message.MessageDecorator;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import com.ly.titc.pms.member.mediator.service.MemberMedService;
import com.ly.titc.pms.member.mediator.service.MemberProfileMedService;
import com.ly.titc.pms.member.mediator.service.MessageMedService;
import com.ly.titc.pms.member.service.MemberService;
import com.ly.titc.springboot.dcdb.dal.core.encrypt.AESEncryptService;
import com.ly.titc.springboot.dcdb.dal.core.encrypt.EncryptService;
import com.ly.titc.springboot.dcdb.dal.core.service.CombinedService;
import com.ly.titc.springboot.elasticsearch.client.ElasticsearchClient;
import com.ly.titc.springboot.elasticsearch.entity.request.SearchRequest;
import com.ly.titc.springboot.elasticsearch.entity.result.DataResult;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.RandomStringUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.ObjectUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.util.Comparator;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ly.titc.pms.member.com.constant.EsTemplateConstants.PAGE_MEMBER_TEMPLATE;
import static com.ly.titc.pms.member.com.enums.RespCodeEnum.MEMBER_10011;
import static com.ly.titc.pms.member.com.enums.RespCodeEnum.MEMBER_10033;

/**
 * 会员Med服务实现
 *
 * <AUTHOR>
 * @date 2024/10/28 19:52
 */
@Slf4j
@Service
public class MemberMedServiceImpl implements MemberMedService {

    @Resource
    private MemberInfoBiz memberInfoBiz;
    @Resource
    private MemberExtendInfoBiz memberExtendInfoBiz;
    @Resource
    private MemberContactInfoBiz memberContactInfoBiz;
    @Resource
    private MemberCardInfoBiz memberCardInfoBiz;
    @Resource
    private MemberMedConverter memberMedConverter;
    @Resource
    private MemberCardMedConverter memberCardMedConverter;
    @Resource
    private MemberCardMedService memberCardMedService;
    @Resource
    private MessageMedService messageMedService;
    @Resource
    private MemberService memberService;
    @Resource
    private ElasticsearchHandler elasticsearchHandler;
    @Resource
    private RedisFactory redisFactory;
    @Resource(type = RegionDecorator.class)
    private RegionDecorator regionDecorator;
    @Resource
    private MemberProfileMedService memberProfileMedService;
    @Resource
    private CustomerDecorator customerDecorator;
    @Resource
    private MemberCardInfoDecorator memberCardInfoDecorator;
    @Resource(type = CombinedService.class)
    private CombinedService combinedService;

    @Resource
    private EncryptService encryptService;

    @Override
    public RegisterMemberResultDto register(RegisterMemberDto dto) {

        Integer masterType = dto.getMasterType();
        String masterCode = dto.getMasterCode(), traceNo = TraceNoUtil.getTraceNo(), mobile = dto.getMobile();
        String idempotentKey = CommonUtil.concat(CommonConstant.REGISTER_IDEMPOTENT_PREFIX, mobile);
        Boolean result = redisFactory.setNx(idempotentKey, 6, mobile);
        //处理中...
        if (!result) {
            log.warn("this mobile register is processing...trackingId:{};mobile:{}", traceNo, mobile);
            throw new ServiceException(RespCodeEnum.MEMBER_10000);
        }
        try {
            // 注册校验
            registerCheck(dto);

            // 注册方式的参数校验
            List<MemberInfo> memberInfos = memberInfoBiz.listByMobiles(masterType, masterCode, mobile, MemberStateEnum.VALID.getState());
            if (CollectionUtils.isNotEmpty(memberInfos)) {
                throw new ServiceException(String.format("手机号【%s】已注册！", mobile), RespCodeEnum.CODE_400.getCode());
            }

            String memberNo = memberService.getMemberNo(masterType, masterCode);
            combinedService.newTransactionWrapper((arg) -> {
                // 注册会员
                MemberRegisterWrapper wrapper = memberMedConverter.convertDtoToWrapper(dto, memberNo);
                fillRegionInfo(wrapper.getMemberContactInfo());
                memberService.register(wrapper);
                // 发放会员卡
                MemberIssueCardWrapper issueWrapper = assembleIssueCardWrapper(dto, memberNo);
                memberService.issueCard(issueWrapper);
                // 如果客户转会员，则调用客户服务关连会员编号及合并资产
                CustomerInfoDto customerInfo = dto.getCustomerInfo();
                if (customerInfo != null) {
                    BindMemberReq req = memberMedConverter.convertDtoToReq(customerInfo, memberNo);
                    customerDecorator.bindMember(req);
                }
                return null;
            });

            // 发送事件消息
            MemberInfo memberInfo = memberInfoBiz.getByMemberNo(masterType, masterCode, memberNo);
            MemberEventMsg memberEventMsg = memberMedConverter.convertPoToEventMsg(memberInfo, MemberEventEnum.REGISTER);
            messageMedService.sendMemberEventMsg(traceNo, memberEventMsg);

            return new RegisterMemberResultDto().setMemberNo(memberNo).setMemberCardNo(dto.getMemberCardInfo().getMemberCardNo());
        } catch (ServiceException e) {
            log.error("会员注册失败, 归属类型:{}, 归属值:{}, 手机号:{}", masterType, masterCode, mobile, e);
            throw e;
        } catch (Exception e) {
            log.error("会员注册失败, 归属类型:{}, 归属值:{}, 手机号:{}", masterType, masterCode, mobile, e);
            sendRegisterFailOaContext(traceNo, masterType, masterCode, mobile, e.getMessage());
            throw new ServiceException(RespCodeEnum.MEMBER_10016);
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public void registerCheck(RegisterMemberDto dto) {
        for (MemberRegisterCheckEnum value : MemberRegisterCheckEnum.values()) {
            AbstractRegisterCheckHandler handler = RegisterCheckHandlerManager.getHandler(value.getAction());
            if (Objects.nonNull(handler)) {
                handler.check(dto);
            }
        }
    }

    public List<MemberDetailDto> listByMobiles(Integer masterType, String masterCode, List<String> mobiles, Integer state) {
        List<MemberInfo> memberInfos = memberInfoBiz.listByMobiles(masterType, masterCode, mobiles, state);
        if (CollectionUtils.isEmpty(memberInfos)) {
            return Lists.newArrayList();
        }
        List<String> memberNos = memberInfos.stream().map(MemberInfo::getMemberNo).collect(Collectors.toList());
        return listByMemberNos(masterType, masterCode, memberNos);
    }

    @Override
    public List<MemberDetailDto> listByIdNos(Integer masterType, String masterCode, Integer idType, List<String> idNos, Integer state) {
        List<MemberInfo> memberInfos = memberInfoBiz.listByIdNos(masterType, masterCode, idType, idNos, state);
        if (CollectionUtils.isEmpty(memberInfos)) {
            return Lists.newArrayList();
        }
        List<String> memberNos = memberInfos.stream().map(MemberInfo::getMemberNo).collect(Collectors.toList());
        return listByMemberNos(masterType, masterCode, memberNos);
    }

    @Override
    public List<MemberDetailDto> listByCardNos(Integer masterType, String masterCode, List<String> cardNos, Integer state) {
        List<MemberCardInfoDto> memberCardInfos = memberCardMedService.listByCardNos(masterType, masterCode, cardNos);
        if (CollectionUtils.isEmpty(memberCardInfos)) {
            return Lists.newArrayList();
        }
        List<String> memberNos = memberCardInfos.stream().map(MemberCardInfoDto::getMemberNo).collect(Collectors.toList());
        List<MemberDetailDto> memberInfos = listByMemberNos(masterType, masterCode, memberNos);
        if (state != null) {
            return memberInfos.stream().filter(member -> member.getState().equals(state)).collect(Collectors.toList());
        }
        return memberInfos;
    }

    @Override
    public MemberDetailDto getDetailByMemberNo(Integer masterType, String masterCode, String memberNo) {
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(masterType, masterCode, memberNo);
        if (memberInfo == null) {
            return null;
        }
        MemberExtendInfo memberExtendInfo = memberExtendInfoBiz.getByMemberNo(memberNo);
        MemberContactInfo memberContactInfo = memberContactInfoBiz.getByMemberNo(memberNo);
        List<MemberProfileTagInfoDto> memberProfileTagInfos = memberProfileMedService.listMemberTag(masterType, masterCode, memberNo);
        List<String> tagNames = memberProfileTagInfos.stream().map(MemberProfileTagInfoDto::getTagName).collect(Collectors.toList());
        List<MemberCardInfoDto> memberCardInfos = memberCardMedService.listByMemberNo(masterType, masterCode, memberNo);
        return memberMedConverter.convertPoToDto(memberInfo, memberExtendInfo, memberContactInfo, tagNames, memberCardInfos);
    }

    @Override
    public MemberInfoDto getByMemberNo(Integer masterType, String masterCode, String memberNo) {
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(masterType, masterCode, memberNo);
        return memberMedConverter.convertPoToDto(memberInfo);
    }

    @Override
    public MemberInfoDto getByMemberNo(String memberNo) {
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(memberNo);
        return memberMedConverter.convertPoToDto(memberInfo);
    }

    @Override
    public List<MemberDetailDto> listByMemberNos(Integer masterType, String masterCode, List<String> memberNos) {
        List<MemberInfo> memberInfos = memberInfoBiz.listByMemberNos(masterType, masterCode, memberNos);
        if (CollectionUtils.isEmpty(memberInfos)) {
            return Lists.newArrayList();
        }
        // 拓展信息
        List<MemberExtendInfo> memberExtendInfos = memberExtendInfoBiz.listByMemberNos(memberNos);
        Map<String, MemberExtendInfo> memberExtendInfoMap = memberExtendInfos.stream().collect(Collectors.toMap(MemberExtendInfo::getMemberNo, Function.identity()));
        // 联系方式
        List<MemberContactInfo> memberContactInfos = memberContactInfoBiz.listByMemberNo(memberNos);
        Map<String, MemberContactInfo> contactInfoMap = memberContactInfos.stream().collect(Collectors.toMap(MemberContactInfo::getMemberNo, Function.identity()));
        // 会员标签
        List<MemberProfileTagInfoDto> memberProfileTagInfos = memberProfileMedService.listMemberTag(memberNos);
        Map<String, List<String>> memberTagNameMap = memberProfileTagInfos.stream().collect(Collectors.groupingBy(MemberProfileTagInfoDto::getMemberNo, Collectors.mapping(MemberProfileTagInfoDto::getTagName, Collectors.toList())));
        // 会员卡
        List<MemberCardInfoDto> memberCardInfos = memberCardMedService.listByMemberNos(masterType, masterCode, memberNos);
        Map<String, List<MemberCardInfoDto>> memberCardInfnMap = memberCardInfos.stream().collect(Collectors.groupingBy(MemberCardInfoDto::getMemberNo));
        return memberInfos.stream().map(memberInfo -> {
            String memberNo = memberInfo.getMemberNo();
            MemberExtendInfo memberExtendInfo = memberExtendInfoMap.get(memberNo);
            MemberContactInfo memberContactInfo = contactInfoMap.get(memberNo);
            List<String> memberTags = memberTagNameMap.getOrDefault(memberNo, Lists.newArrayList());
            List<MemberCardInfoDto> cardInfos = memberCardInfnMap.get(memberNo);
            return memberMedConverter.convertPoToDto(memberInfo, memberExtendInfo, memberContactInfo, memberTags, cardInfos);
        }).collect(Collectors.toList());
    }

    @Override
    public Pageable<MemberDetailDto> pageMemberByStrongMode(PageMemberParamDto pageMemberParamDto) {

        PageMemberParamBo pageMemberParamBo = memberMedConverter.convertDtoToBo(pageMemberParamDto);
        IPage<String> iPage = memberInfoBiz.pageMember(pageMemberParamBo);
        Pageable<MemberDetailDto> pageable = Pageable.empty();
        pageable.setTotalPage((int) iPage.getPages()).setTotal((int) iPage.getTotal())
                .setCurrPage((int) iPage.getCurrent()).setDatas(Lists.newArrayList());

        List<String> memberNos = iPage.getRecords();
        if (!CollectionUtils.isEmpty(iPage.getRecords())) {
            List<MemberDetailDto> memberInfos = listByMemberNos(pageMemberParamDto.getMasterType(), pageMemberParamDto.getMasterCode(), memberNos);
            pageable.setDatas(memberInfos);
        }
        return pageable;
    }

    @Override
    public Pageable<MemberDetailDto> pageMemberByFinalMode(PageMemberParamDto pageMemberParamDto) {

        Integer pageIndex = pageMemberParamDto.getPageIndex();
        Integer pageSize = pageMemberParamDto.getPageSize();

        PageMemberDocumentDto request = new PageMemberDocumentDto();
        request.setClauses(buildClauses(pageMemberParamDto))
                .setCardClauses(buildCardClauses(pageMemberParamDto))
                .setTagClauses(buildTagClauses(pageMemberParamDto))
                .setFrom((pageIndex - 1) * pageSize).setSize(pageSize);
        Pageable<MemberDetailDto> pageable = new Pageable<>();
        pageable.setCurrPage(pageIndex);
        String data = JSON.toJSONString(request);
        log.info("pageByCondition, templateName:{}, condition:{}", PAGE_MEMBER_TEMPLATE, data);
        SearchRequest<MemberDocumentDto> searchRequest = elasticsearchHandler.getSearchRequest(PAGE_MEMBER_TEMPLATE, data);
        DataResult<MemberDocumentDto> dataResult = ElasticsearchClient.search(searchRequest);
        if (null == dataResult || dataResult.getCount() == 0) {
            return pageable;
        }
        int total = (int) dataResult.getCount();
        pageable.setTotal(total).setTotalPage(Pageable.getTotalPage(pageSize, total));
        List<String> memberNos = dataResult.getList().stream().map(MemberDocumentDto::getMemberNo).collect(Collectors.toList());
        List<MemberDetailDto> memberInfos = listByMemberNos(pageMemberParamDto.getMasterType(), pageMemberParamDto.getMasterCode(), memberNos);
        pageable.setDatas(memberInfos);
        return pageable;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void cancelMember(Integer masterType, String masterCode, String memberNo, String operator) {
        // 校验会员状态
        checkMemberState(memberNo);
        memberInfoBiz.cancelMember(masterType, masterCode, memberNo, operator);

        // TODO 是否清空会员资产（储值、积分、优惠券）

        // 发送事件消息
        MemberEventMsg memberEventMsg = new MemberEventMsg();
        memberEventMsg.setMasterType(masterType).setMasterCode(masterCode)
                .setMemberNo(memberNo).setEventType(MemberEventEnum.CANCEL);
        messageMedService.sendMemberEventMsg(TraceNoUtil.getTraceNo(), memberEventMsg);
    }

    @Override
    public void recoverMember(Integer masterType, String masterCode, String memberNo, String operator) {
        MemberInfoDto memberInfo = getByMemberNo(masterType, masterCode, memberNo);
        if (Objects.isNull(memberInfo)) {
            log.info("this member is not exist!!memberNo:{}", memberNo);
            throw new ServiceException(MEMBER_10011);
        }
        if (memberInfo.getState().equals(MemberStateEnum.VALID.getState())) {
            log.info("this member state is invalid!!memberNo:{}", memberNo);
            throw new ServiceException(RespCodeEnum.MEMBER_10035);
        }
        if (memberInfoBiz.existByMobile(masterType, masterCode, memberInfo.getMobile(), memberNo)) {
            log.info("this mobile has been used !!memberNo:{}", memberNo);
            throw new ServiceException(RespCodeEnum.MEMBER_10036);
        }
        memberInfoBiz.recoverMember(masterType, masterCode, memberNo, operator);

        // 发送事件消息
        MemberEventMsg memberEventMsg = memberMedConverter.convertPoToEventMsg(memberInfo);
        memberEventMsg.setEventType(MemberEventEnum.RECOVER);
        messageMedService.sendMemberEventMsg(TraceNoUtil.getTraceNo(), memberEventMsg);
    }

    @Override
    public void updateMember(UpdateMemberInfoDto dto) {

        MemberSaveWrapper wrapper = memberMedConverter.convertDtoToWrapper(dto);
        fillRegionInfo(wrapper.getMemberContactInfo());
        memberService.updateMember(wrapper);

        // 发送事件消息
        MemberEventMsg memberEventMsg = memberMedConverter.convertPoToEventMsg(dto.getMemberInfo(), MemberEventEnum.MODIFY);
        messageMedService.sendMemberEventMsg(TraceNoUtil.getTraceNo(), memberEventMsg);
    }

    @Override
    public void checkMemberState(String memberNo) {
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(memberNo);
        if (Objects.isNull(memberInfo)) {
            log.info("this member is not exist!!memberNo:{}", memberNo);
            throw new ServiceException(MEMBER_10011);
        }
        if (memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
            log.info("this member state is invalid!!memberNo:{}", memberNo);
            throw new ServiceException(MEMBER_10033);
        }
    }

    @Override
    public void changePassword(String memberNo, String newPassword, String confirmPassword, String operator) {

        AbstractRegisterCheckHandler handler = RegisterCheckHandlerManager.getHandler(MemberRegisterCheckEnum.PASSWORD.getAction());
        RegisterMemberDto dto = new RegisterMemberDto();
        dto.setPassword(newPassword);
        dto.setConfirmPassword(confirmPassword);
        handler.check(dto);

        // 查询现有的会员信息
        MemberInfo memberInfo = memberInfoBiz.getByMemberNo(memberNo);
        memberInfo.setPassword(encryptService.encrypt(newPassword));
        memberInfo.setModifyUser(operator);
        memberInfoBiz.update(memberInfo);

        // 发送事件消息
        MemberEventMsg memberEventMsg = memberMedConverter.convertPoToEventMsg(memberInfo, MemberEventEnum.MODIFY);
        messageMedService.sendMemberEventMsg(UserThreadHolder.getTrackingId(), memberEventMsg);

    }

    @Override
    public MemberInfoDto getByMobile(Integer masterCode, String masterType, String mobile, String memberNo) {
        return memberMedConverter.convertPoToDto(memberInfoBiz.getByMobile(masterCode, masterType, mobile, memberNo));
    }

    @Override
    public String getMemberNoByMobile(Integer masterType, String masterCode, String mobile) {
        MemberInfo memberInfo = memberInfoBiz.getByMobile(masterType, masterCode, mobile);
        return memberInfo == null ? null : memberInfo.getMemberNo();
    }

    @Override
    public IssueMemberCardResultDto issueMemberCard(IssueMemberCardRequestDto dto) {
        try {
            // 业务逻辑校验
            if (StringUtils.isBlank(dto.getCustomerNo()) && StringUtils.isBlank(dto.getMemberNo())) {
                return IssueMemberCardResultDto.failure("客户编号和会员号不能同时为空");
            }
            if (StringUtils.isNotBlank(dto.getCustomerNo()) && StringUtils.isNotBlank(dto.getMemberNo())) {
                return IssueMemberCardResultDto.failure("客户编号和会员号不能同时传入");
            }
            // 校验卡信息
            if (dto.getDefaultCard() == null || dto.getDefaultCard() == 0) {
                // 非默认卡需要传入cardId和cardLevel
                if (dto.getCardId() == null) {
                    return IssueMemberCardResultDto.failure("非默认卡时会员卡ID不能为空");
                }
            }
            // 根据入参判断是客户转会员还是会员升级
            if (StringUtils.isNotBlank(dto.getCustomerNo())) {
                // 客户转会员逻辑
                return handleCustomerToMember(dto);
            } else if (StringUtils.isNotBlank(dto.getMemberNo())) {
                // 会员升级/发放卡逻辑
                return handleMemberUpgrade(dto);
            } else {
                return IssueMemberCardResultDto.failure("客户编号和会员编号不能同时为空");
            }
        } catch (ServiceException e) {
            log.error("发放会员卡失败: {}", e.getMessage(), e);
            return IssueMemberCardResultDto.failure(e.getMessage());
        } catch (Exception e) {
            log.error("发放会员卡异常: {}", e.getMessage(), e);
            return IssueMemberCardResultDto.failure("系统异常，请稍后重试");
        }
    }

    /**
     * build card clauses
     *
     * @param bo
     * @return
     */
    protected List<JSONObject> buildTagClauses(PageMemberParamDto bo) {

        List<JSONObject> clauses = new ArrayList<>();

        List<Long> tagIds = bo.getTagIds();
        if (!CollectionUtils.isEmpty(tagIds)) {
            clauses.add(elasticsearchHandler.buildTerms("memberTagInfos.tagId", tagIds));
        }

        return clauses;
    }

    /**
     * build card clauses
     *
     * @param bo
     * @return
     */
    protected List<JSONObject> buildCardClauses(PageMemberParamDto bo) {

        List<JSONObject> clauses = new ArrayList<>();

        Integer cardType = bo.getCardType();
        if (!ObjectUtils.isEmpty(cardType)) {
            clauses.add(elasticsearchHandler.buildTerm("memberCardInfos.cardType", cardType));
        }

        String memberCardNo = bo.getMemberCardNo();
        if (!ObjectUtils.isEmpty(memberCardNo)) {
            clauses.add(elasticsearchHandler.buildTerm("memberCardInfos.memberCardNo", memberCardNo));
        }

//        cardInfo es里未存储memberNo，挪至memberInfo
//        String memberNo = bo.getMemberNo();
//        if (!ObjectUtils.isEmpty(memberNo)) {
//            clauses.add(elasticsearchHandler.buildTerm("memberCardInfos.memberNo", memberNo));
//        }

        Long cardId = bo.getCardId();
        if (!ObjectUtils.isEmpty(cardId)) {
            clauses.add(elasticsearchHandler.buildMatchPhase("memberCardInfos.cardId", cardId));
        }

        Integer cardLevel = bo.getCardLevel();
        if (!ObjectUtils.isEmpty(cardLevel)) {
            clauses.add(elasticsearchHandler.buildTerm("memberCardInfos.cardLevel", cardLevel));
        }

        Integer cardState = bo.getCardState();
        if (!Objects.isNull(cardState)) {
            clauses.add(elasticsearchHandler.buildTerm("memberCardInfos.cardState", cardState));
        }

        String cardEffectBeginDate = bo.getCardEffectBeginDate();
        if (StringUtils.isNotBlank(cardEffectBeginDate)) {
            clauses.add(elasticsearchHandler.buildRange("memberCardInfos.cardEffectBeginDate", "gte", Timestamp.valueOf(LocalDateUtil.parseByNormalDate(cardEffectBeginDate).atTime(0, 0)).getTime()));
        }

        String cardEffectEndDate = bo.getCardEffectEndDate();
        if (!Objects.isNull(cardEffectEndDate)) {
            clauses.add(elasticsearchHandler.buildRange("memberCardInfos.cardEffectEndDate", "lte", Timestamp.valueOf(LocalDateUtil.parseByNormalDate(cardEffectEndDate).atTime(0, 0)).getTime()));
        }

        return clauses;
    }


    /**
     * build clauses
     *
     * @param bo
     * @return
     */
    protected List<JSONObject> buildClauses(PageMemberParamDto bo) {

        List<JSONObject> clauses = new ArrayList<>();

        Integer masterType = bo.getMasterType();
        if (!ObjectUtils.isEmpty(masterType)) {
            clauses.add(elasticsearchHandler.buildTerm("masterType", masterType));
        }

        String masterCode = bo.getMasterCode();
        if (!ObjectUtils.isEmpty(masterCode)) {
            clauses.add(elasticsearchHandler.buildTerm("masterCode", masterCode));
        }

        String memberNo = bo.getMemberNo();
        if (!ObjectUtils.isEmpty(memberNo)) {
            clauses.add(elasticsearchHandler.buildTerm("memberNo", memberNo));
        }

        String realName = bo.getRealName();
        if (!ObjectUtils.isEmpty(realName)) {
            clauses.add(elasticsearchHandler.buildMatchPhase("realName", realName));
        }

        String mobile = bo.getMobile();
        if (!ObjectUtils.isEmpty(mobile)) {
            clauses.add(elasticsearchHandler.buildTerm("mobile", mobile));
        }

        Integer idType = bo.getIdType();
        if (!Objects.isNull(idType)) {
            clauses.add(elasticsearchHandler.buildTerm("idType", idType));
        }

        String idNo = bo.getIdNo();
        if (!ObjectUtils.isEmpty(idNo)) {
            clauses.add(elasticsearchHandler.buildTerm("idNo", idNo));
        }

        Integer state = bo.getState();
        if (!Objects.isNull(state)) {
            clauses.add(elasticsearchHandler.buildTerm("state", state));
        }

        String source = bo.getSource();
        if (!ObjectUtils.isEmpty(source)) {
            clauses.add(elasticsearchHandler.buildTerm("source", source));
        }

        String registerHotel = bo.getRegisterHotel();
        if (!ObjectUtils.isEmpty(registerHotel)) {
            clauses.add(elasticsearchHandler.buildTerm("registerHotel", registerHotel));
        }

        String registerHotelType = bo.getRegisterHotelType();
        if (!ObjectUtils.isEmpty(registerHotelType)) {
            clauses.add(elasticsearchHandler.buildTerm("registerHotelType", registerHotelType));
        }

        String salesman = bo.getSalesman();
        if (!ObjectUtils.isEmpty(salesman)) {
            clauses.add(elasticsearchHandler.buildTerm("salesman", salesman));
        }

        String memberRegisterBeginDate = bo.getMemberRegisterBeginDate();
        if (StringUtils.isNotEmpty(memberRegisterBeginDate)) {
            clauses.add(elasticsearchHandler.buildRange("registerDate", "gte", Timestamp.valueOf(LocalDateUtil.parseByNormalDate(memberRegisterBeginDate).atTime(0, 0,0)).getTime()));
        }

        String memberRegisterEndDate = bo.getMemberRegisterEndDate();
        if (StringUtils.isNotEmpty(memberRegisterEndDate)) {
            clauses.add(elasticsearchHandler.buildRange("registerDate", "lte", Timestamp.valueOf(LocalDateUtil.parseByNormalDate(memberRegisterEndDate).atTime(23, 59, 59)).getTime()));
        }

        Integer blackFlag = bo.getBlackFlag();
        if (blackFlag != null) {
            clauses.add(elasticsearchHandler.buildTerm("blackFlag", blackFlag));
        }
        return clauses;
    }

    /**
     * 注册失败通知
     *
     * @param trackingId
     * @param masterType
     * @param masterCode
     * @param mobile
     * @param message
     */
    private void sendRegisterFailOaContext(String trackingId, Integer masterType, String masterCode, String mobile, String message) {
        try {
            String title = "【会员注册失败通知】";
            String content = title + Constant.HTML_RETURN_NEWLINE +
                    "trackingId:" + trackingId + Constant.HTML_RETURN_NEWLINE +
                    "会员归属:" + MasterTypeEnum.getDesc(masterType) + Constant.HTML_RETURN_NEWLINE +
                    "会员归属值:" + masterCode + Constant.HTML_RETURN_NEWLINE +
                    "手机号:" + mobile + Constant.HTML_RETURN_NEWLINE +
                    "失败原因" + message + Constant.HTML_RETURN_NEWLINE;
            messageMedService.sendOaAlertMsg(trackingId, title, content);
        } catch (Exception e) {
            log.error("告警消息发送失败：{}", trackingId, e);
        }
    }

    /**
     * 填充扩展信息
     *
     * @param contactInfo
     */
    private void fillRegionInfo(MemberContactInfo contactInfo) {

        //TODO 后期如果有性能问题，可以封装一个接口或者并行
        Integer countryId = contactInfo.getCountryId();
        if (!Objects.isNull(countryId)) {
            RegionResp resp = regionDecorator.getById(countryId);
            if (!Objects.isNull(resp)) {
                contactInfo.setCountry(resp.getName());
            }
        }
        Integer provinceId = contactInfo.getProvinceId();
        if (!Objects.isNull(provinceId)) {
            RegionResp resp = regionDecorator.getById(provinceId);
            if (!Objects.isNull(resp)) {
                contactInfo.setProvince(resp.getName());
            }
        }
        Integer cityId = contactInfo.getCityId();
        if (!Objects.isNull(cityId)) {
            RegionResp resp = regionDecorator.getById(cityId);
            if (!Objects.isNull(resp)) {
                contactInfo.setCity(resp.getName());
            }
        }
        Integer districtId = contactInfo.getDistrictId();
        if (!Objects.isNull(districtId)) {
            RegionResp resp = regionDecorator.getById(districtId);
            if (!Objects.isNull(resp)) {
                contactInfo.setDistrict(resp.getName());
            }
        }
    }

    private MemberCardLevelChangeRecord assembleChangeRecord(IssueMemberCardDto memberCardInfo) {
        MemberCardLevelChangeRecord record = memberCardMedConverter.convertDtoToPo(LevelChangeTypeEnum.REGISTER.getType(), "注册会员发放会员卡", memberCardInfo);
        record.setPreLevel(0).setPreLevelName("").setAfterLevel(memberCardInfo.getCardLevel()).setAfterLevelName(memberCardInfo.getCardLevelName());
        return record;
    }

    private MemberIssueCardWrapper assembleIssueCardWrapper(RegisterMemberDto dto, String memberNo) {
        IssueMemberCardDto memberCardInfo = dto.getMemberCardInfo();
        memberCardInfo.setMemberNo(memberNo);
        MemberIssueCardWrapper issueWrapper = new MemberIssueCardWrapper();
        issueWrapper.setMemberCardInfo(memberCardMedConverter.convertDtoToPo(memberCardInfo, LocalDateTime.now()));
        issueWrapper.setMemberCardLevelChangeRecord(assembleChangeRecord(memberCardInfo));
        return issueWrapper;
    }



    /**
     * 处理客户转会员逻辑
     *
     * @param dto
     * @return
     */
    private IssueMemberCardResultDto handleCustomerToMember(IssueMemberCardRequestDto dto) {
        String customerNo = dto.getCustomerNo();
        // 查询客户信息
        GetByCustomerNoReq customerReq = new GetByCustomerNoReq();
        customerReq.setHotelCode(dto.getHotelCode());
        customerReq.setCustomerNo(customerNo);
        CustomerDetailInfoResp customerInfo = customerDecorator.getDetailByCustomerNo(customerReq);
        if (customerInfo == null) {
            return IssueMemberCardResultDto.failure("客户不存在");
        }
        // 检查客户信息中是否已经有会员号
        if (StringUtils.isNotBlank(customerInfo.getMemberNo())) {
            // 客户已经是会员，走升级逻辑
            log.info("客户{}已经是会员，会员号：{}，走升级逻辑", customerNo, customerInfo.getMemberNo());
            // 获取会员信息
            MemberInfo memberInfo = memberInfoBiz.getByMemberNo(dto.getMasterType(), dto.getMasterCode(), customerInfo.getMemberNo());
            if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
                log.info("会员不存在或者已经被注销，则还是走客户转会员注册的逻辑, customer: {}, memberNo: {}", customerNo, customerInfo.getMemberNo());
                return registerCustomerAsMember(dto, customerInfo);
            }
            // 走会员升级逻辑
            return handleExistingMemberUpgrade(dto, memberInfo);
        }
        // 客户转会员注册逻辑
        log.info("客户{}不是会员，走注册逻辑", customerNo);
        return registerCustomerAsMember(dto, customerInfo);
    }

    /**
     * 处理已存在会员的升级逻辑
     *
     * @param dto
     * @param existingMember
     * @return
     */
    private IssueMemberCardResultDto handleExistingMemberUpgrade(IssueMemberCardRequestDto dto, MemberInfo existingMember) {
        String memberNo = existingMember.getMemberNo();
        // 获取会员当前所有卡信息
        List<MemberCardInfo> memberCards = memberCardInfoBiz.listByMemberNo(dto.getMasterType(), dto.getMasterCode(), memberNo);
        // 如果是发放默认卡
        if (dto.getDefaultCard() != null && dto.getDefaultCard() == 1) {
            log.info("会员{}发放默认卡等级{}", memberNo, dto.getCardLevel());
            // 默认卡也需要检查等级，找到会员当前最高等级
            if (CollectionUtils.isNotEmpty(memberCards)) {
                Integer currentMaxLevel = memberCards.stream()
                        .filter(card -> card.getState().equals(1)) // 有效状态
                        .mapToInt(MemberCardInfo::getCardLevel)
                        .max()
                        .orElse(0);

                if (dto.getCardLevel() <= currentMaxLevel) {
                    return IssueMemberCardResultDto.failure(String.format("目标等级[%d]不能小于或等于当前最高等级[%d]", dto.getCardLevel(), currentMaxLevel));
                }
            }
            return upgradeMemberCard(dto, memberNo, null);
        }
        // 非默认卡，需要cardId
        if (dto.getCardId() == null) {
            return IssueMemberCardResultDto.failure("非默认卡时，卡ID不能为空");
        }
        // 查找指定cardId的卡
        if (CollectionUtils.isNotEmpty(memberCards)) {
            MemberCardInfo existingCard = memberCards.stream()
                    .filter(card -> card.getState().equals(1)) // 有效状态
                    .filter(card -> card.getCardId().equals(dto.getCardId()))
                    .findFirst()
                    .orElse(null);

            if (existingCard != null) {
                // 检查等级是否需要升级
                if (dto.getCardLevel() <= existingCard.getCardLevel()) {
                    return IssueMemberCardResultDto.failure(String.format("目标等级[%d]不能小于或等于当前等级[%d]", dto.getCardLevel(), existingCard.getCardLevel()));
                }
                log.info("会员{}升级卡{}从等级{}到等级{}", memberNo, dto.getCardId(), existingCard.getCardLevel(), dto.getCardLevel());
                return upgradeMemberCard(dto, memberNo, existingCard);
            } else {
                // 会员没有这种类型的卡，相当于新发放
                log.info("会员{}新发放卡{}等级{}", memberNo, dto.getCardId(), dto.getCardLevel());
                return upgradeMemberCard(dto, memberNo, null);
            }
        } else {
            // 会员没有任何卡，新发放
            log.info("会员{}首次发放卡{}等级{}", memberNo, dto.getCardId(), dto.getCardLevel());
            return upgradeMemberCard(dto, memberNo, null);
        }
    }

    /**
     * 客户转会员注册逻辑
     *
     * @param dto
     * @param customerInfo
     * @return
     */
    private IssueMemberCardResultDto registerCustomerAsMember(IssueMemberCardRequestDto dto, CustomerDetailInfoResp customerInfo) {
        try {
            // 构建注册会员DTO
            RegisterMemberDto registerDto = buildRegisterMemberDto(dto, customerInfo);
            // 执行会员注册
            RegisterMemberResultDto registerResult = register(registerDto);
            // 获取等级名称
            String cardLevelName = getCardLevelName(dto);
            return IssueMemberCardResultDto.success(
                    registerResult.getMemberNo(),
                    registerResult.getMemberCardNo(),
                    dto.getCardLevel(),
                    cardLevelName,
                    "REGISTER"
            );
        } catch (Exception e) {
            log.error("客户转会员注册失败: customerNo={}, error={}", dto.getCustomerNo(), e.getMessage(), e);
            return IssueMemberCardResultDto.failure("客户转会员注册失败: " + e.getMessage());
        }
    }

    /**
     * 处理会员升级逻辑
     *
     * @param dto
     * @return
     */
    private IssueMemberCardResultDto handleMemberUpgrade(IssueMemberCardRequestDto dto) {
        String memberNo = dto.getMemberNo();

        // 校验会员状态
        checkMemberState(memberNo);

        // 检查目标等级参数
        if (dto.getCardLevel() == null) {
            return IssueMemberCardResultDto.failure("卡等级不能为空");
        }

        // 获取会员当前所有卡信息
        List<MemberCardInfo> memberCards = memberCardInfoBiz.listByMemberNo(dto.getMasterType(), dto.getMasterCode(), memberNo);

        // 如果是发放默认卡
        if (dto.getDefaultCard() != null && dto.getDefaultCard() == 1) {
            log.info("会员{}发放默认卡等级{}", memberNo, dto.getCardLevel());

            // 获取默认卡配置
            MemberCardConfigResp defaultCardConfig = memberCardInfoDecorator.getDefaultMemberCardConfigInfo(dto.getMasterCode(), TraceNoUtil.getTraceNo(), dto.getMasterType());
            if (defaultCardConfig == null) {
                return IssueMemberCardResultDto.failure("未找到默认卡配置");
            }

            // 查找会员是否已有默认卡
            MemberCardInfo defaultCard = null;
            if (CollectionUtils.isNotEmpty(memberCards)) {
                Long defaultCardId = defaultCardConfig.getId();
                defaultCard = memberCards.stream()
                        .filter(card -> card.getState().equals(1)) // 有效状态
                        .filter(card -> card.getCardId().equals(defaultCardId)) // 默认卡ID
                        .findFirst()
                        .orElse(null);
            }

            if (defaultCard != null) {
                // 已有默认卡，走升级逻辑（等级必须更高）
                if (dto.getCardLevel() <= defaultCard.getCardLevel()) {
                    return IssueMemberCardResultDto.failure(String.format("目标等级[%d]不能小于或等于当前默认卡等级[%d]", dto.getCardLevel(), defaultCard.getCardLevel()));
                }
                log.info("会员{}升级默认卡从等级{}到等级{}", memberNo, defaultCard.getCardLevel(), dto.getCardLevel());
                return upgradeMemberCard(dto, memberNo, defaultCard);
            } else {
                // 无默认卡，新发放默认卡
                log.info("会员{}新发放默认卡等级{}", memberNo, dto.getCardLevel());
                return upgradeMemberCard(dto, memberNo, null);
            }
        }

        // 非默认卡，需要cardId
        if (dto.getCardId() == null) {
            return IssueMemberCardResultDto.failure("非默认卡时，卡ID不能为空");
        }

        // 查找指定cardId的卡
        MemberCardInfo existingCard = null;
        if (CollectionUtils.isNotEmpty(memberCards)) {
            existingCard = memberCards.stream()
                    .filter(card -> card.getState().equals(1)) // 有效状态
                    .filter(card -> card.getCardId().equals(dto.getCardId()))
                    .findFirst()
                    .orElse(null);
        }

        if (existingCard != null) {
            // 有该类型卡，走升级逻辑（等级必须更高）
            if (dto.getCardLevel() <= existingCard.getCardLevel()) {
                return IssueMemberCardResultDto.failure(String.format("目标等级[%d]不能小于或等于当前等级[%d]", dto.getCardLevel(), existingCard.getCardLevel()));
            }
            log.info("会员{}升级卡{}从等级{}到等级{}", memberNo, dto.getCardId(), existingCard.getCardLevel(), dto.getCardLevel());
            return upgradeMemberCard(dto, memberNo, existingCard);
        } else {
            // 无该类型卡，新发放该类型卡（不用管等级）
            log.info("会员{}新发放卡{}等级{}", memberNo, dto.getCardId(), dto.getCardLevel());
            return upgradeMemberCard(dto, memberNo, null);
        }
    }

    /**
     * 升级会员卡
     *
     * @param dto
     * @param memberNo
     * @param currentCard
     * @return
     */
    private IssueMemberCardResultDto upgradeMemberCard(IssueMemberCardRequestDto dto, String memberNo, MemberCardInfo currentCard) {
        try {
            // 构建升级DTO
            UpdateCardLevelDto updateDto = buildUpdateCardLevelDto(dto, memberNo, currentCard);
            // 执行升级
            memberCardMedService.updateCardLevel(updateDto);
            // 获取等级名称
            String cardLevelName = getCardLevelName(dto);
            return IssueMemberCardResultDto.success(
                    memberNo,
                    currentCard.getMemberCardNo(),
                    dto.getCardLevel(),
                    cardLevelName,
                    "UPGRADE"
            );
        } catch (Exception e) {
            log.error("会员升级失败: memberNo={}, error={}", memberNo, e.getMessage(), e);
            return IssueMemberCardResultDto.failure("会员升级失败: " + e.getMessage());
        }
    }

    /**
     * 构建注册会员DTO
     *
     * @param dto
     * @param customerInfo
     * @return
     */
    private RegisterMemberDto buildRegisterMemberDto(IssueMemberCardRequestDto dto, CustomerDetailInfoResp customerInfo) {
        RegisterMemberDto registerDto = new RegisterMemberDto();
        registerDto.setMasterType(dto.getMasterType());
        registerDto.setMasterCode(dto.getMasterCode());
        registerDto.setMobile(customerInfo.getMobile());
        registerDto.setRealName(customerInfo.getRealName());
        registerDto.setNickName(customerInfo.getNickName());
        registerDto.setEnName(customerInfo.getEnName());
        registerDto.setGender(customerInfo.getGender());
        registerDto.setBirthday(customerInfo.getBirthday());
        registerDto.setIdType(customerInfo.getIdType());
        registerDto.setIdNo(customerInfo.getIdNo());
        registerDto.setRegisterHotelType("HOTEL");
        registerDto.setRegisterHotel(dto.getHotelCode());
        registerDto.setOperator(dto.getBizId()); // 使用bizId作为操作人
        registerDto.setSource("PMS");

        // 构建会员卡信息
        IssueMemberCardDto memberCardInfo = buildMemberCardInfo(dto);
        registerDto.setMemberCardInfo(memberCardInfo);

        // 构建客户信息
        CustomerInfoDto customerInfoDto = new CustomerInfoDto();
        customerInfoDto.setBlocCode(dto.getMasterCode()); // 使用masterCode作为blocCode
        customerInfoDto.setHotelCode(dto.getHotelCode());
        customerInfoDto.setCustomerNo(dto.getCustomerNo());
        registerDto.setCustomerInfo(customerInfoDto);

        return registerDto;
    }

    /**
     * 构建升级DTO
     *
     * @param dto
     * @param memberNo
     * @param currentCard
     * @return
     */
    private UpdateCardLevelDto buildUpdateCardLevelDto(IssueMemberCardRequestDto dto, String memberNo, MemberCardInfo currentCard) {
        UpdateCardLevelDto updateDto = new UpdateCardLevelDto();
        updateDto.setMasterType(dto.getMasterType());
        updateDto.setMasterCode(dto.getMasterCode());
        updateDto.setMemberNo(memberNo);
        updateDto.setMemberCardNo(currentCard.getMemberCardNo());
        updateDto.setPreLevel(currentCard.getCardLevel());
        // 获取当前等级名称
        String preLevelName = getCurrentCardLevelName(dto.getMasterType(), dto.getMasterCode(), currentCard.getCardId(), currentCard.getCardLevel());
        updateDto.setPreLevelName(preLevelName);

        updateDto.setAfterLevel(dto.getCardLevel());
        // 获取目标等级名称
        String afterLevelName = getCardLevelName(dto);
        updateDto.setAfterLevelName(afterLevelName);
        updateDto.setReason(StringUtils.isNotBlank(dto.getReason()) ? dto.getReason() : "手动发放会员卡");
        updateDto.setOperator(dto.getBizId()); // 使用bizId作为操作人
        updateDto.setChangeType(LevelChangeTypeEnum.MANUAL_OPERATION.getType());

        return updateDto;
    }

    /**
     * 获取当前会员卡等级名称
     *
     * @param masterType
     * @param masterCode
     * @param cardId
     * @param cardLevel
     * @return
     */
    private String getCurrentCardLevelName(Integer masterType, String masterCode, Long cardId, Integer cardLevel) {
        try {
            MemberCardConfigResp memberCardConfig = memberCardInfoDecorator.getMemberCardConfigInfo(masterType, masterCode, cardId);
            if (memberCardConfig != null && CollectionUtils.isNotEmpty(memberCardConfig.getCardLevelConfigs())) {
                return memberCardConfig.getCardLevelConfigs().stream()
                        .filter(config -> config.getCardLevel().equals(cardLevel))
                        .map(MemberCardLevelConfigInfoResp::getCardLevelName)
                        .findFirst()
                        .orElse("未知等级");
            }
        } catch (Exception e) {
            log.warn("获取会员卡等级名称失败: cardId={}, cardLevel={}", cardId, cardLevel, e);
        }
        return "未知等级";
    }

    /**
     * 获取会员卡等级名称
     *
     * @param dto
     * @return
     */
    private String getCardLevelName(IssueMemberCardRequestDto dto) {
        // 如果是发放默认卡，需要根据cardId和cardLevel获取等级名称
        if (dto.getDefaultCard() != null && dto.getDefaultCard() == 1) {
            // 发放默认卡逻辑，需要获取默认卡的等级名称
            return getDefaultCardLevelName(dto.getMasterType(), dto.getMasterCode());
        } else if (dto.getCardId() != null && dto.getCardLevel() != null) {
            // 指定卡ID和等级，获取等级名称
            return getCurrentCardLevelName(dto.getMasterType(), dto.getMasterCode(), dto.getCardId(), dto.getCardLevel());
        }
        return "未知等级";
    }

    /**
     * 获取默认卡等级名称
     *
     * @param masterType
     * @param masterCode
     * @return
     */
    private String getDefaultCardLevelName(Integer masterType, String masterCode) {
        try {
            // 这里需要根据业务逻辑获取默认卡的等级名称
            // 可能需要调用相关服务获取默认卡配置
            return "默认等级";
        } catch (Exception e) {
            log.warn("获取默认卡等级名称失败: masterType={}, masterCode={}", masterType, masterCode, e);
            return "默认等级";
        }
    }

    /**
     * 构建会员卡信息
     *
     * @param dto
     * @return
     */
    private IssueMemberCardDto buildMemberCardInfo(IssueMemberCardRequestDto dto) {
        IssueMemberCardDto memberCardInfo = new IssueMemberCardDto();
        memberCardInfo.setMasterType(dto.getMasterType());
        memberCardInfo.setMasterCode(dto.getMasterCode());

        if (dto.getDefaultCard() != null && dto.getDefaultCard() == 1) {
            // 发放默认卡逻辑
            // 需要获取默认卡的配置信息
            // 这里暂时使用固定值，实际应该从配置中获取
            memberCardInfo.setCardId(1L); // 默认卡ID
            memberCardInfo.setCardLevel(1); // 默认等级
        } else {
            memberCardInfo.setCardId(dto.getCardId());
            memberCardInfo.setCardLevel(dto.getCardLevel());
        }

        String cardLevelName = getCardLevelName(dto);
        memberCardInfo.setCardLevelName(cardLevelName);
        memberCardInfo.setIssueHotelType("HOTEL");
        memberCardInfo.setIssueHotel(dto.getHotelCode());
        memberCardInfo.setIssueUser(dto.getBizId()); // 使用bizId作为发放人

        return memberCardInfo;
    }

}
