package com.ly.titc.pms.member.mediator.rpc.dubbo.account;

import com.google.common.collect.Lists;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.account.dubbo.entity.response.ExpenseInfoResp;
import com.ly.titc.pms.account.dubbo.interfaces.ExpenseDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 消费项
 *
 * <AUTHOR>
 * @date 2024/12/28 16:04
 */
@Slf4j
@Component
public class AccountExpenseDecorator {

    @DubboReference(group = "${account-dsf-dubbo-group}",timeout = 30000)
    private ExpenseDubboService expenseDubboService;


    public List<ExpenseInfoResp> listExpense(){

//        return Response.getValidateData(expenseDubboService.listUseHotel())
        return Lists.newArrayList();
    }

}
