package com.ly.titc.pms.member.mediator.service.impl;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.util.PageableUtil;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.mdm.entity.response.hotel.HotelBaseInfoResp;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.mediator.converter.MemberDataRecordConverter;
import com.ly.titc.pms.member.mediator.entity.dto.data.*;
import com.ly.titc.pms.member.mediator.rpc.dsf.mdm.HotelDecorator;
import com.ly.titc.pms.member.mediator.service.MemberDataRecordMedService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberDataRecordMedServiceImp
 * @Date：2024-12-11 21:43
 * @Filename：MemberDataRecordMedServiceImp
 */
@Slf4j
@Service
public class MemberDataRecordMedServiceImp implements MemberDataRecordMedService {

    @Resource
    private MemberDataRecordConverter converter;

    @Resource
    private MemberCheckInRecordBiz checkInRecordBiz;

    @Resource
    private MemberCheckInStatisticsBiz checkInStatisticsBiz;

    @Resource
    private MemberOrderInfoBiz memberOrderInfoBiz;

    @Resource
    private MemberOrderDetailInfoBiz memberOrderDetailInfoBiz;

    @Resource
    private MemberCardLevelChangeRecordBiz memberCardLevelChangeRecordBiz;

    @Resource
    private HotelDecorator hotelDecorator;

    @Override
    public Pageable<CheckinRecordDto> pageCheckinRecord(PageCheckinRecordDto dto) {
        Page<MemberCheckInRecord> page = checkInRecordBiz.page(dto.getMemberNo(), dto.getHotelCode(), dto.getCheckInBeginTime(), dto.getCheckInEndTime(),
                dto.getCheckOutBeginTime(), dto.getCheckOutEndTime(), dto.getPageIndex(), dto.getPageSize());
        List<MemberCheckInRecord> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return Pageable.empty();
        }
        List<String> hotelCodes = page.getRecords().stream().map(MemberCheckInRecord::getHotelCode).collect(Collectors.toList());
        List<HotelBaseInfoResp> hotelBaseInfos = hotelDecorator.listHotelBaseInfos(dto.getBlocCode(), hotelCodes, TraceNoUtil.getTraceNo());
        Map<String, String> hotelNameMap = hotelBaseInfos.stream().collect(Collectors.toMap(HotelBaseInfoResp::getHotelCode, HotelBaseInfoResp::getHotelName));
        return PageableUtil.convert(page, converter.convertPoToDto(records, hotelNameMap));
    }

    @Override
    public CheckInStatisticsDto queryMemberCheckInStatistics(String memberNo) {
        MemberCheckInStatistics statistics = checkInStatisticsBiz.getByMemberNo(memberNo);
        if (statistics == null) {
            return new CheckInStatisticsDto();
        }
        return converter.convertPoToDto(statistics);
    }

    @Override
    public List<CheckInStatisticsDto> listByMemberNos(List<String> memberNos) {
        List<MemberCheckInStatistics> memberCheckInStatistics = checkInStatisticsBiz.listByMemberNo(memberNos);
        return memberCheckInStatistics.stream().map(converter::convertPoToDto).collect(Collectors.toList());
    }

    @Override
    public Pageable<PurchaseCardRecordDto> pagePurchaseCardRecordDto(PagePurchaseCardRecordDto dto) {
        Pageable<MemberOrderInfo> pageable = memberOrderInfoBiz.pageMemberPurchaseCardRecord(dto.getMasterType(), dto.getMasterCode(), dto.getMemberNo(), dto.getPageIndex(), dto.getPageSize(), dto.getBeginTime(), dto.getEndTime(), dto.getPlatformChannel());
        List<MemberOrderInfo> dataLst = pageable.getDatas();
        if (CollectionUtils.isEmpty(dataLst)) {
            return Pageable.empty();
        }
        List<String> memberOrderNoList = dataLst.stream().map(MemberOrderInfo::getMemberOrderNo).collect(Collectors.toList());
        List<MemberOrderDetailInfo> detailInfoList = memberOrderDetailInfoBiz.listByOrderNoList(memberOrderNoList);
        return PageableUtil.convert(pageable, converter.convertPoToPurchaseCardRecordDto(dataLst, detailInfoList));
    }

    @Override
    public Pageable<MemberLevelChangeRecordDto> pageMemberLevelChangeRecord(PageLevelChangeRecordDto dto) {
        Page<MemberCardLevelChangeRecord> page = memberCardLevelChangeRecordBiz.page(dto.getPageIndex(), dto.getPageSize(), dto.getMemberNo(), dto.getBeginTime(), dto.getEndTime());
        List<MemberCardLevelChangeRecord> records = page.getRecords();
        if (CollectionUtils.isEmpty(records)) {
            return Pageable.empty();
        }
        return PageableUtil.convert(page, converter.convertPoToMemberLevelChangeRecordDto(records));
    }

}
