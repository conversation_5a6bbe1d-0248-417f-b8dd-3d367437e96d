package com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm;

import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.QueryMemberCardLevelReq;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardLevelConfigInfoResp;
import com.ly.titc.pms.ecrm.dubbo.interfaces.MemberCardLevelDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberCardLevelDecorator
 * @Date：2024-12-4 14:13
 * @Filename：MemberCardLevelDecorator
 */
@Slf4j
@Component
public class MemberCardLevelDecorator {

    @DubboReference(group = "${ecrm-dsf-group}", check = false)
    private MemberCardLevelDubboService memberCardLevelDubboService;

    public List<MemberCardLevelConfigInfoResp> queryMemberCardLevel(Integer masterType, String masterCode, String name ,String trackingId) {
        QueryMemberCardLevelReq queryMemberCardLevel = new QueryMemberCardLevelReq();
        queryMemberCardLevel.setMasterType(masterType);
        queryMemberCardLevel.setMasterCode(masterCode);
        queryMemberCardLevel.setName(name);
        queryMemberCardLevel.setTrackingId(trackingId);
        return Response.getValidateData(memberCardLevelDubboService.queryMemberCardLevel(queryMemberCardLevel));
    }
}
