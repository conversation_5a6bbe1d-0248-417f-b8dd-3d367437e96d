package com.ly.titc.pms.member.mediator.entity.dto.member;

import lombok.Data;

/**
 * 会员升级DTO
 *
 * <AUTHOR>
 * @date 2024/12/10 17:34
 */
@Data
public class UpgradeMemberDto {

    /**
     * 归属（1:集团;2:门店;3:艺龙）
     */
    private Integer masterType;

    /**
     * 归属值
     */
    private String masterCode;

    /**
     * 会员号
     */
    private String memberNo;

    /**
     * 卡ID
     */
    private Long cardId;

    /**
     * 会员卡号
     */
    private String memberCardNo;

    /**
     * 变更前等级
     */
    private Integer preLevel;

    /**
     * 变更前等级名称
     */
    private String preLevelName;

    /**
     * 变更后等级
     */
    private Integer afterLevel;

    /**
     * 变更后等级名称
     */
    private String afterLevelName;

    /**
     * 原因
     */
    private String reason;

    /**
     * 操作人
     */
    private String operator;


}
