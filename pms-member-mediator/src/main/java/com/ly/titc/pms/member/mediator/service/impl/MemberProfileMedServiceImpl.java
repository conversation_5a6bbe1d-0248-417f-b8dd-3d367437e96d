package com.ly.titc.pms.member.mediator.service.impl;

import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.google.common.collect.Lists;
import com.ly.titc.common.exceptions.ServiceException;
import com.ly.titc.common.factory.RedisFactory;
import com.ly.titc.common.util.TraceNoUtil;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberTagConfigInfoResp;
import com.ly.titc.pms.member.biz.*;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.MemberStateEnum;
import com.ly.titc.pms.member.com.enums.RespCodeEnum;
import com.ly.titc.pms.member.com.utils.CommonUtil;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileAddressInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileInvoiceHeaderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileOccupantsInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberProfileTagInfo;
import com.ly.titc.pms.member.dubbo.entity.message.MemberEventMsg;
import com.ly.titc.pms.member.dubbo.enums.MemberEventEnum;
import com.ly.titc.pms.member.mediator.converter.MemberProfileMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.BatchAddMemberTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.BatchDeleteMemberTagDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberDetailDto;
import com.ly.titc.pms.member.mediator.entity.dto.member.MemberInfoDto;
import com.ly.titc.pms.member.mediator.entity.dto.profile.*;
import com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm.MemberCardInfoDecorator;
import com.ly.titc.pms.member.mediator.service.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.ly.titc.pms.member.com.enums.BlacklistSceneEnum.FORBID_OPERATION;

/**
 * 会员档案Med服务实现
 *
 * <AUTHOR>
 * @date 2024/11/5 19:56
 */
@Slf4j
@Service
public class MemberProfileMedServiceImpl implements MemberProfileMedService {

    @Resource(type = MemberProfileAddressInfoBiz.class)
    private MemberProfileAddressInfoBiz memberProfileAddressInfoBiz;
    @Resource(type = MemberProfileInvoiceHeaderInfoBiz.class)
    private MemberProfileInvoiceHeaderInfoBiz memberProfileInvoiceHeaderInfoBiz;
    @Resource(type = MemberProfileOccupantsInfoBiz.class)
    private MemberProfileOccupantsInfoBiz memberProfileOccupantsInfoBiz;
    @Resource(type = MemberProfileTagInfoBiz.class)
    private MemberProfileTagInfoBiz memberProfileTagInfoBiz;
    @Resource(type = MemberProfileMedConverter.class)
    private MemberProfileMedConverter memberProfileMedConverter;
    @Resource(type = RedisFactory.class)
    private RedisFactory redisFactory;
    @Resource(type = MemberCardInfoDecorator.class)
    private MemberCardInfoDecorator memberCardInfoDecorator;
    @Resource(type = MessageMedService.class)
    private MessageMedService messageMedService;
    @Resource
    private MemberBlacklistMedService memberBlacklistMedService;
    @Resource
    private MemberMedService memberMedService;

    @Override
    public void saveCommonAddress(SaveMemberProfileAddressDto info) {
        String traceNo = TraceNoUtil.getTraceNo();
        String memberNo = info.getMemberNo();
        String idempotentKey = CommonUtil.concat(CommonConstant.ADDRESS_IDEMPOTENT_PREFIX, memberNo);
        Boolean result = redisFactory.setNx(idempotentKey, 6, traceNo);
        //处理中...
        if (!result) {
            log.warn("this address save is processing...trackingId:{};memberNo:{}", traceNo, memberNo);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            MemberInfoDto memberInfo = memberMedService.getByMemberNo(info.getMasterType(), info.getMasterCode(), memberNo);
            if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
                log.error("保存常用地址失败，会员不存在或会员已注销，memberNo:{}", memberNo);
                throw new ServiceException(RespCodeEnum.MEMBER_10032);
            }

            MemberProfileAddressInfo entity = memberProfileMedConverter.convertDtoToPo(info);
            if (info.getAddressNo() != null) {
                memberProfileAddressInfoBiz.update(entity);
            } else {
                memberProfileAddressInfoBiz.add(entity);
            }
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public void deleteCommonAddress(Integer masterType, String masterCode, String memberNo, Long addressNo, String operator) {
        String traceNo = TraceNoUtil.getTraceNo();
        String idempotentKey = CommonUtil.concat(CommonConstant.ADDRESS_IDEMPOTENT_PREFIX, memberNo, addressNo);
        Boolean result = redisFactory.setNx(idempotentKey, 6, traceNo);
        //处理中...
        if (!result) {
            log.warn("this address delete is processing...trackingId:{};memberNo:{}", traceNo, memberNo);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
            if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
                log.error("删除常用地址失败，会员不存在或会员已注销，memberNo:{}", memberNo);
                throw new ServiceException(RespCodeEnum.MEMBER_10032);
            }

            memberProfileAddressInfoBiz.delete(memberNo, addressNo, operator);
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public List<MemberProfileAddressInfoDto> listCommonAddress(Integer masterType, String masterCode, String memberNo, String name) {
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
        if (memberInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }
        List<MemberProfileAddressInfo> memberProfileAddressInfos = memberProfileAddressInfoBiz.listByMemberNo(memberNo, name);
        return memberProfileAddressInfos.stream().map(memberProfileMedConverter::convertPoToDto).collect(Collectors.toList());
    }

    @Override
    public MemberProfileAddressInfoDto getCommonAddressByNo(Integer masterType, String masterCode, String memberNo, Long addressNo) {
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
        if (memberInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }
        return memberProfileMedConverter.convertPoToDto(memberProfileAddressInfoBiz.getByNo(memberNo, addressNo));
    }

    @Override
    public void saveCommonInvoiceHeader(SaveMemberProfileInvoiceHeaderDto info) {
        String traceNo = TraceNoUtil.getTraceNo();
        String memberNo = info.getMemberNo();
        String idempotentKey = CommonUtil.concat(CommonConstant.INVOICE_HEADER_IDEMPOTENT_PREFIX, memberNo);
        Boolean result = redisFactory.setNx(idempotentKey, 6, traceNo);
        //处理中...
        if (!result) {
            log.warn("this invoice header save is processing...trackingId:{};memberNo:{}", traceNo, memberNo);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            MemberInfoDto memberInfo = memberMedService.getByMemberNo(info.getMasterType(), info.getMasterCode(), memberNo);
            if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
                log.error("保存常用发票失败，会员不存在或会员已注销，memberNo:{}", memberNo);
                throw new ServiceException(RespCodeEnum.MEMBER_10032);
            }

            MemberProfileInvoiceHeaderInfo entity = memberProfileMedConverter.convertDtoToPo(info);
            if (info.getInvoiceHeaderNo() != null) {
                memberProfileInvoiceHeaderInfoBiz.update(entity);
            } else {
                memberProfileInvoiceHeaderInfoBiz.add(entity);
            }
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public void deleteCommonInvoiceHeader(Integer masterType, String masterCode, String memberNo, Long invoiceHeaderNo, String operator) {
        String traceNo = TraceNoUtil.getTraceNo();
        String idempotentKey = CommonUtil.concat(CommonConstant.INVOICE_HEADER_IDEMPOTENT_PREFIX, memberNo, invoiceHeaderNo);
        Boolean result = redisFactory.setNx(idempotentKey, 6, traceNo);
        //处理中...
        if (!result) {
            log.warn("this invoice header delete is processing...trackingId:{};memberNo:{}", traceNo, memberNo);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
            if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
                log.error("删除常用发票失败，会员不存在或会员已注销，memberNo:{}", memberNo);
                throw new ServiceException(RespCodeEnum.MEMBER_10032);
            }

            memberProfileInvoiceHeaderInfoBiz.delete(memberNo, invoiceHeaderNo, operator);
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public List<MemberProfileInvoiceHeaderInfoDto> listCommonInvoiceHeader(Integer masterType, String masterCode, String memberNo, String headerName) {
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
        if (memberInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }
        List<MemberProfileInvoiceHeaderInfo> memberProfileInvoiceHeaderInfos = memberProfileInvoiceHeaderInfoBiz.listByMemberNo(memberNo, headerName);
        return memberProfileInvoiceHeaderInfos.stream().map(memberProfileMedConverter::convertPoToDto).collect(Collectors.toList());
    }

    @Override
    public MemberProfileInvoiceHeaderInfoDto getCommonInvoiceHeaderByNo(Integer masterType, String masterCode, String memberNo, Long invoiceHeaderNo) {
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
        if (memberInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }
        return memberProfileMedConverter.convertPoToDto(memberProfileInvoiceHeaderInfoBiz.getByNo(memberNo, invoiceHeaderNo));
    }

    @Override
    public void saveCommonOccupants(SaveMemberProfileOccupantsDto info) {
        String traceNo = TraceNoUtil.getTraceNo();
        String memberNo = info.getMemberNo();
        String idempotentKey = CommonUtil.concat(CommonConstant.OCCUPANTS_IDEMPOTENT_PREFIX, memberNo);
        Boolean result = redisFactory.setNx(idempotentKey, 6, traceNo);
        //处理中...
        if (!result) {
            log.warn("this occupants save is processing...trackingId:{};memberNo:{}", traceNo, memberNo);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            MemberInfoDto memberInfo = memberMedService.getByMemberNo(info.getMasterType(), info.getMasterCode(), memberNo);
            if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
                log.error("保存入住人失败，会员不存在或会员已注销，memberNo:{}", memberNo);
                throw new ServiceException(RespCodeEnum.MEMBER_10032);
            }

            MemberProfileOccupantsInfo entity = memberProfileMedConverter.convertDtoToPo(info);
            if (info.getOccupantsNo() != null) {
                memberProfileOccupantsInfoBiz.update(entity);
            } else {
                memberProfileOccupantsInfoBiz.add(entity);
            }
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public void deleteCommonOccupants(Integer masterType, String masterCode, String memberNo, Long occupantsNo, String operator) {
        String traceNo = TraceNoUtil.getTraceNo();
        String idempotentKey = CommonUtil.concat(CommonConstant.OCCUPANTS_IDEMPOTENT_PREFIX, memberNo, occupantsNo);
        Boolean result = redisFactory.setNx(idempotentKey, 6, traceNo);
        //处理中...
        if (!result) {
            log.warn("this occupants delete is processing...trackingId:{};memberNo:{}", traceNo, memberNo);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
            if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
                log.error("删除入住人失败，会员不存在或会员已注销，memberNo:{}", memberNo);
                throw new ServiceException(RespCodeEnum.MEMBER_10032);
            }

            memberProfileOccupantsInfoBiz.delete(memberNo, occupantsNo, operator);
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public List<MemberProfileOccupantsInfoDto> listCommonOccupants(Integer masterType, String masterCode, String memberNo, String name) {
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
        if (memberInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }
        List<MemberProfileOccupantsInfo> memberProfileOccupantsInfos = memberProfileOccupantsInfoBiz.listByMemberNo(memberNo, name);
        return memberProfileOccupantsInfos.stream().map(memberProfileMedConverter::convertPoToDto).collect(Collectors.toList());
    }

    @Override
    public MemberProfileOccupantsInfoDto getCommonOccupantsByNo(Integer masterType, String masterCode, String memberNo, Long occupantsNo) {
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
        if (memberInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }
        return memberProfileMedConverter.convertPoToDto(memberProfileOccupantsInfoBiz.getByNo(memberNo, occupantsNo));
    }

    @Override
    public void addMemberTag(SaveMemberProfileTagDto info) {
        String traceNo = TraceNoUtil.getTraceNo();
        String memberNo = info.getMemberNo();
        String idempotentKey = CommonUtil.concat(CommonConstant.TAG_IDEMPOTENT_PREFIX, memberNo);
        Boolean result = redisFactory.setNx(idempotentKey, 6, traceNo);
        //处理中...
        if (!result) {
            log.warn("this member tag save is processing...trackingId:{};memberNo:{}", traceNo, memberNo);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            MemberInfoDto memberInfo = memberMedService.getByMemberNo(info.getMasterType(), info.getMasterCode(), memberNo);
            if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
                log.error("添加会员标签失败，会员不存在或会员已注销，memberNo:{}", memberNo);
                throw new ServiceException(RespCodeEnum.MEMBER_10037);
            }
            if (memberBlacklistMedService.checkWhetherBlacklist(info.getMemberNo(), FORBID_OPERATION.getType(), info.getPlatformChannel())) {
                log.error("添加会员标签失败，会员已被拉黑，memberNo:{}", memberNo);
                throw new ServiceException(RespCodeEnum.MEMBER_10037);
            }
           ;
            if (memberProfileTagInfoBiz.checkMemberTag(info.getMemberNo(), info.getTagId())) {
                return;
            }
            MemberProfileTagInfo entity = memberProfileMedConverter.convertDtoToPo(info);
            memberProfileTagInfoBiz.add(entity);

            // 发送事件消息
            MemberEventMsg memberEventMsg = memberProfileMedConverter.convertDtoToMsg(info);
            memberEventMsg.setEventType(MemberEventEnum.MARK);
            messageMedService.sendMemberEventMsg(traceNo, memberEventMsg);
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public void deleteMemberTag(Integer masterType, String masterCode, String memberNo, Long tagNo, String operator) {
        String traceNo = TraceNoUtil.getTraceNo();
        String idempotentKey = CommonUtil.concat(CommonConstant.TAG_IDEMPOTENT_PREFIX, memberNo, tagNo);
        Boolean result = redisFactory.setNx(idempotentKey, 6, traceNo);
        //处理中...
        if (!result) {
            log.warn("this tag delete is processing...trackingId:{};memberNo:{}", traceNo, memberNo);
            throw new ServiceException(RespCodeEnum.CODE_1000);
        }
        try {
            MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
            if (memberInfo == null || memberInfo.getState().equals(MemberStateEnum.CANCEL.getState())) {
                log.error("删除会员标签失败，会员不存在或会员已注销，memberNo:{}", memberNo);
                throw new ServiceException(RespCodeEnum.MEMBER_10032);
            }

            memberProfileTagInfoBiz.delete(memberNo, tagNo, operator);
            // 发送事件消息
            MemberEventMsg memberEventMsg = new MemberEventMsg();
            memberEventMsg.setMasterType(masterType).setMasterCode(masterCode)
                    .setMemberNo(memberNo).setEventType(MemberEventEnum.MARK_DELETE);
            messageMedService.sendMemberEventMsg(traceNo, memberEventMsg);
        } finally {
            redisFactory.del(idempotentKey);
        }
    }

    @Override
    public List<MemberProfileTagInfoDto> listMemberTag(Integer masterType, String masterCode, String memberNo) {
        MemberInfoDto memberInfo = memberMedService.getByMemberNo(masterType, masterCode, memberNo);
        if (memberInfo == null) {
            throw new ServiceException(RespCodeEnum.MEMBER_10011);
        }
        List<MemberProfileTagInfo> memberProfileTagInfos = memberProfileTagInfoBiz.listMemberTag(memberNo);
        return memberProfileTagInfos.stream().map(memberProfileMedConverter::convertPoToDto).collect(Collectors.toList());
    }

    @Override
    public List<MemberProfileTagInfoDto> listMemberTag(List<String> memberNos) {
        List<MemberProfileTagInfo> memberProfileTagInfos = memberProfileTagInfoBiz.listMemberTag(memberNos);
        return memberProfileTagInfos.stream().map(memberProfileMedConverter::convertPoToDto).collect(Collectors.toList());
    }

    @Override
    public void batchAddMemberTag(BatchAddMemberTagDto dto) {
        String traceNo = TraceNoUtil.getTraceNo();
        Integer masterType = dto.getMasterType();
        String masterCode = dto.getMasterCode();
        List<Long> tagIds = dto.getTagIds();
        List<String> memberNos = dto.getMemberNos();

        List<MemberTagConfigInfoResp> memberTagConfigs = memberCardInfoDecorator.listTagConfig(masterType, masterCode, tagIds, TraceNoUtil.getTraceNo());
        if (CollectionUtils.isEmpty(memberTagConfigs)) {
            throw new ServiceException(RespCodeEnum.MEMBER_10029);
        }

        List<MemberProfileTagInfo> memberProfileTagInfos = memberProfileTagInfoBiz.listMemberTags(memberNos, tagIds);
        List<String> existMemberTags = memberProfileTagInfos.stream().map(e -> String.format("%s_%s", e.getMemberNo(), e.getTagId())).collect(Collectors.toList());

        List<MemberProfileTagInfo> addTagList = new ArrayList<>();
        for (MemberTagConfigInfoResp tagConfig : memberTagConfigs) {
            for (String memberNo : memberNos) {
                String existMemberTag = String.format("%s_%s", memberNo, tagConfig.getId());
                if (!existMemberTags.contains(existMemberTag)) {
                    MemberProfileTagInfo entity = memberProfileMedConverter.convertPoToPo(memberNo, tagConfig, dto);
                    addTagList.add(entity);
                }
            }
        }

        if (CollectionUtils.isNotEmpty(addTagList)) {
            memberProfileTagInfoBiz.batchAdd(addTagList);
            addTagList.forEach(e -> {
                MemberEventMsg memberEventMsg = new MemberEventMsg();
                memberEventMsg.setMasterType(masterType).setMasterCode(masterCode)
                        .setMemberNo(e.getMemberNo()).setEventType(MemberEventEnum.MARK);
                messageMedService.sendMemberEventMsg(traceNo, memberEventMsg);
            });
        }
    }

    @Override
    public void batchDeleteMemberTag(BatchDeleteMemberTagDto dto) {
        if (dto.isAll()) {
            memberProfileTagInfoBiz.batchDelete(null, dto.getTagIds(), dto.getOperator());
        } else {
            memberProfileTagInfoBiz.batchDelete(dto.getMemberNos(), dto.getTagIds(), dto.getOperator());
        }
    }
}
