package com.ly.titc.pms.member.mediator.handler.order;

import com.alibaba.fastjson.JSONObject;
import com.ly.titc.pms.member.biz.MemberOrderDetailInfoBiz;
import com.ly.titc.pms.member.com.constant.CommonConstant;
import com.ly.titc.pms.member.com.enums.MemberSceneEnum;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderDetailInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberOrderRefundInfo;
import com.ly.titc.pms.member.mediator.converter.MemberCardMedConverter;
import com.ly.titc.pms.member.mediator.entity.dto.member.IssueMemberCardDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.CreateOrderDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.OrderPostResultDto;
import com.ly.titc.pms.member.mediator.entity.dto.order.PurchaseCardDto;
import com.ly.titc.pms.member.mediator.service.MemberCardMedService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2024-11-26 17:41
 */
@Slf4j
@Component
public class OrderScenePurchaseCardHandler extends AbstractOrderSceneHandler<PurchaseCardDto> {

    @Resource
    private MemberCardMedService memberCardMedService;

    @Resource
    private MemberOrderDetailInfoBiz detailInfoBiz;

    @Resource
    private MemberCardMedConverter memberCardMedConverter;


    @Override
    public CreateOrderDto<PurchaseCardDto> doPreCheck(CreateOrderDto<PurchaseCardDto> dto) {
        PurchaseCardDto memberDto =dto.getMemberSceneNoteDto();
        IssueMemberCardDto issueMemberCardDto = memberCardMedConverter.convertDtoToDto(memberDto);
        memberCardMedService.checkIssueCard(issueMemberCardDto);
        return dto;
    }

    @Override
    public String doGetLockKey(PurchaseCardDto dto) {
        return CommonConstant.CREATE_ORDER_LOCK_KEY_PREFIX + String.format("%s_%s_%s", getScene(), dto.getMemberNo(), dto.getCardId());
    }

    @Override
    public void saveSceneOrder(CreateOrderDto<PurchaseCardDto> dto) {
    }

    @Override
    public OrderPostResultDto postHandle(MemberOrderInfo orderInfo) {
        MemberOrderDetailInfo detailInfo =  detailInfoBiz.getByOrderNo(orderInfo.getMemberOrderNo());
        PurchaseCardDto purchaseCardDto = JSONObject.parseObject(detailInfo.getMemberSceneNote(), PurchaseCardDto.class);

        IssueMemberCardDto issueMemberCardDto = memberCardMedConverter.convertDtoToDto(purchaseCardDto);
        issueMemberCardDto.setIssueUser(detailInfo.getCreateUser());
        memberCardMedService.issueCard(issueMemberCardDto);
        OrderPostResultDto resultDto = new OrderPostResultDto();
        resultDto.setMemberNo(purchaseCardDto.getMemberNo());
        resultDto.setMemberOrderNo(orderInfo.getMemberOrderNo());
        return resultDto;
        // TODO 发放礼包
    }

    @Override
    public void refundHandle(MemberOrderRefundInfo orderInfo) {

    }

    public String getScene() {
        return MemberSceneEnum.PURCHASECARD.getScene();
    }
}
