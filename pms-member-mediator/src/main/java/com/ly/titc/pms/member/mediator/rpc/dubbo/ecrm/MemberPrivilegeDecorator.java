package com.ly.titc.pms.member.mediator.rpc.dubbo.ecrm;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.ecrm.dubbo.entity.request.member.*;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberCardPrivilegeConfigResp;
import com.ly.titc.pms.ecrm.dubbo.entity.response.member.MemberPrivilegeConfigResp;
import com.ly.titc.pms.ecrm.dubbo.interfaces.MemberPrivilegeDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @Author：rui
 * @name：MemberPrivileteDecorator
 * @Date：2024-11-12 17:08
 * @Filename：MemberPrivileteDecorator
 */
@Slf4j
@Component
public class MemberPrivilegeDecorator {

    @DubboReference(group = "${ecrm-dsf-group}", check = false)
    private MemberPrivilegeDubboService service;

    /**
     * 保存
     *
     * @param req req
     * @return id
     */
    public Long saveMemberPrivilege(SaveMemberPrivilegeConfigInfoReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(service.save(req));
    }

    /**
     * 删除
     *
     */
    public void deleteMemberPrivilege(Long id, String trackingId, String operator) {
        DeleteBaseReq req = new DeleteBaseReq();
        req.setId(id);
        req.setTrackingId(trackingId);
        req.setOperator(operator);
        log.info("请求参数：{}", req);
        service.delete(req);
    }

    /**
     * 权益详情查询
     *
     * @param id id
     * @param trackingId trackingId
     * @return 详情
     */
    public MemberPrivilegeConfigResp getMemberPrivilegeConfigDetail(Long id, String trackingId) {
        GetDetailBaseReq req = new GetDetailBaseReq();
        req.setId(id);
        req.setTrackingId(trackingId);
        log.info("请求参数：{}", req);
        return Response.getValidateData(service.detail(req));
    }

    /**
     * 根据归属查询会员权益
     *
     * @param req req
     * @return 会员权益配置
     */
    public List<MemberPrivilegeConfigResp> listByMasterTypeAndCode(QueryMemberPrivilegeConfigReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(service.listByMasterTypeAndCode(req));
    }


    /**
     * 分页查询权益
     *
     * @param req 请求体
     * @return 分页结果
     */
    public Pageable<MemberPrivilegeConfigResp> page(QueryMemberPrivilegeConfigReq req) {
        log.info("请求参数：{}", req);
        return Response.getValidateData(service.page(req));
    }

    /**
     * 查询多卡相关权益
     */
    public List<MemberCardPrivilegeConfigResp> listCardRelatedPrivileges(List<Long> cardIdList, String trackingId) {
        ListMemberCardPrivilegeLevelReq req = new ListMemberCardPrivilegeLevelReq();
        req.setCardIdList(cardIdList);
        req.setTrackingId(trackingId);
        log.info("请求参数：{}", req);
        return Response.getValidateData(service.listCardRelatedPrivileges(req));
    }

    /**
     * 根据卡id，等级查询权益
     * @return 等级相关权力列表
     */
    public List<MemberCardPrivilegeConfigResp> listMemberCardPrivilegeLevel(Long id, Integer level, String trackingId) {
        ListMemberCardPrivilegeLevelReq req = new ListMemberCardPrivilegeLevelReq();
        req.setCardId(id);
        req.setCardLevel(level);
        req.setTrackingId(trackingId);
        log.info("请求参数：{}", req);
        return Response.getValidateData(service.listMemberCardPrivilegeLevel(req));
    }

    /**
     * 启停权益
     * @param id id
     * @param state 状态
     * @param trackingId 追踪id
     * @param operator 操作人
     */
    public void action(Long id, Integer state, String trackingId, String operator) {
        ActionBaseReq req = new ActionBaseReq();
        req.setId(id);
        req.setState(state);
        req.setTrackingId(trackingId);
        req.setOperator(operator);
        log.info("请求参数：{}", req);
        service.action(req);
    }

    public Boolean checkPrivilegeUsed(Long id, String trackingId) {
        GetDetailBaseReq req = new GetDetailBaseReq();
        req.setId(id);
        req.setTrackingId(trackingId);
        log.info("请求参数：{}", req);
        return Response.getValidateData(service.checkPrivilegeUsed(req));
    }
}
