package com.ly.titc.pms.member.mediator.rpc.dubbo.customer;

import com.ly.titc.common.entity.Pageable;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.customer.dubbo.entity.request.customer.*;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerDetailInfoResp;
import com.ly.titc.pms.customer.dubbo.entity.response.customer.CustomerInfoResp;
import com.ly.titc.pms.customer.dubbo.interfaces.CustomerDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * 客户服务
 *
 * <AUTHOR>
 * @date 2024/12/11 18:55
 */
@Slf4j
@Component
public class CustomerDecorator {

    @DubboReference(group = "${customer-dsf-dubbo-group}",timeout = 30000)
    private CustomerDubboService customerDubboService;


    /**
     * 分页查询在住客户
     *
     * @param req
     * @return
     */
    public Pageable<CustomerInfoResp> pageStayCustomer(PageCustomerReq req){
        return Response.getValidateData(customerDubboService.pageCustomer(req));
    }

    /**
     * 分页查询客户
     *
     * @param req
     * @return
     */
    public Pageable<CustomerInfoResp> pageCustomer(PageCustomerReq req){
        return Response.getValidateData(customerDubboService.pageCustomer(req));
    }

    /**
     * 查询客户详细信息
     *
     * @param req
     * @return
     */
    public CustomerInfoResp getByCustomerNo(GetByCustomerNoReq req){
        return Response.getValidateData(customerDubboService.getInfoByCustomerNo(req));
    }

    /**
     * 查询客户详细信息
     *
     * @param req
     * @return
     */
    public CustomerDetailInfoResp getDetailByCustomerNo(GetByCustomerNoReq req){
        return Response.getValidateData(customerDubboService.getDetailByCustomerNo(req));
    }

    /**
     * 校验手机号是否被占用
     *
     * @param req
     * @return
     */
    public boolean checkExistByMobile(CheckExistByMobileReq req){
        return Response.getValidateData(customerDubboService.checkExistByMobile(req));
    }

    /**
     * 客户注册
     *
     * @param req
     * @return 客户编号
     */
    public String register(RegisterCustomerReq req){
        return Response.getValidateData(customerDubboService.register(req));
    }

    /**
     * 更新客户信息
     *
     * @param req
     * @return 客户编号
     */
    public String updateCustomer(UpdateCustomerReq req){
        return Response.getValidateData(customerDubboService.updateCustomer(req));
    }

    /**
     * 绑定会员
     *
     * @param req
     */
    public void bindMember(BindMemberReq req){
        Response.getValidateData(customerDubboService.bindMember(req));
    }
}
