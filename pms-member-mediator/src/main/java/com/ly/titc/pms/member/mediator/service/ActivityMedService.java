package com.ly.titc.pms.member.mediator.service;

import com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity.QueryMemberActiveActivityDto;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.EventActivityResp;

/**
 * <AUTHOR>
 * @classname
 * @descrition
 * @since 2025-1-3 17:58
 */
public interface ActivityMedService {

    EventActivityResp  getHighestPriorityEventActivity(QueryMemberActiveActivityDto dto);
}
