package com.ly.titc.pms.member.mediator.entity.dto.spm.acitvity;

import com.ly.titc.common.annotation.LegalEnum;
import com.ly.titc.pms.spm.dubbo.enums.ActivityCarrierTypeEnum;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * @Description
 * <AUTHOR>
 * @Date 2024-12-13 11:13
 */
@Data
public class ApplicableCarrierDto {

    /**
     * 载体类型
     */
    @LegalEnum(target = ActivityCarrierTypeEnum.class, message = "载体类型不合法",methodName = "getCode")
    private String carrierType;

    /**
     * 载体业务值
     */
    @NotBlank
    private String carrierBizVal;
}
