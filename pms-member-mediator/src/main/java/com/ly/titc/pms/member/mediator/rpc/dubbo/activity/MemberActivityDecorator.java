package com.ly.titc.pms.member.mediator.rpc.dubbo.activity;

import com.alibaba.fastjson.JSON;
import com.ly.titc.common.entity.Response;
import com.ly.titc.pms.spm.dubbo.entity.request.activity.member.*;
import com.ly.titc.pms.spm.dubbo.entity.response.activity.member.*;
import com.ly.titc.pms.spm.dubbo.interfaces.MemberActivityDubboService;
import lombok.extern.slf4j.Slf4j;
import org.apache.dubbo.config.annotation.DubboReference;
import org.springframework.stereotype.Component;

import javax.validation.Valid;
import java.util.List;

/**
 * 会员活动
 *
 * <AUTHOR>
 * @date 2024/12/31 14:19
 */
@Slf4j
@Component
public class MemberActivityDecorator {

    @DubboReference(group = "${spm-dsf-dubbo-group}")
    private MemberActivityDubboService memberActivityDubboService;

    /**
     * 保存售卡活动
     *
     * @param req
     * @return
     */
    public String saveSaleCardActivity(SaveSaleCardActivityReq req){
        log.info("保存售卡活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.saveSaleCardActivity(req));
    }

    /**
     * 查询售卡活动
     *
     * @param req
     * @return
     */
    public MemberCardSaleActivityDetailResp getSaleCardActivity(GetMemberActivityReq req){
        log.info("查询售卡活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.getSaleCardActivity(req));
    }

    /**
     * 保存升级活动
     *
     * @param req
     * @return
     */
    public String saveUpgradeActivity(@Valid SaveUpgradeActivityReq req){
        log.info("保存升级活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.saveUpgradeActivity(req));
    }

    /**
     * 查询升级活动
     *
     * @param req
     * @return
     */
    public MemberUpgradeActivityDetailResp getUpgradeActivity(@Valid GetMemberActivityReq req){
        log.info("查询升级活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.getUpgradeActivity(req));
    }

    /**
     * 保存储值活动
     *
     * @param req
     * @return
     */
    public String saveRechargeActivity(@Valid SaveRechargeActivityReq req){
        log.info("保存储值活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.saveRechargeActivity(req));
    }


    /**
     * 查询充值活动
     *
     * @param req
     * @return
     */
    public MemberRechargeActivityDetailResp getRechargeActivity(GetMemberActivityReq req){
        log.info("查询储值活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.getRechargeActivity(req));
    }

    /**
     * 保存积分活动
     *
     * @param req
     * @return
     */
    public String savePointsActivity(SavePointsActivityReq req){
        log.info("保存积分活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.savePointsActivity(req));
    }

    /**
     * 查询积分活动
     *
     * @param req
     * @return
     */
    public MemberPointsActivityDetailResp getPointsActivity(@Valid GetMemberActivityReq req){
        log.info("查询积分活动入参数:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.getPointsActivity(req));
    }

    /**
     * 查询售卡活动可售卡
     *
     * @param req
     * @return
     */
    public List<MemberSellableCardResp> listSellableCard(QuerySellableCardReq req){
        log.info("查询售卡活动可售卡入参:{}", JSON.toJSONString(req));
        return Response.getValidateData(memberActivityDubboService.listSellableCard(req));
    }
}
