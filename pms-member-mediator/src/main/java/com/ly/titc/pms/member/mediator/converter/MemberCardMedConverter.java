package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.member.dal.entity.po.MemberCardInfo;
import com.ly.titc.pms.member.dal.entity.po.MemberCardLevelChangeRecord;
import com.ly.titc.pms.member.mediator.entity.dto.member.*;
import com.ly.titc.pms.member.mediator.entity.dto.order.PurchaseCardDto;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.Mappings;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 会员卡MedConverter
 *
 * <AUTHOR>
 * @date 2024/10/31 11:27
 */
@Mapper(componentModel = "spring")
public interface MemberCardMedConverter {

    @Mappings({
            @Mapping(target = "createUser", source = "operator"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    MemberCardLevelChangeRecord convertDtoToPo(UpdateCardLevelDto updateCardLevelDto);

    MemberCardInfoDto convertPoToDto(MemberCardInfo memberCardInfo);

    List<MemberCardInfoDto> convertPoToDto(List<MemberCardInfo> memberCardInfo);

    MemberCardInfo convertDtoToPo(MemberCardInfoDto memberCardInfo);

    @Mappings({
            @Mapping(target = "createUser", source = "memberCardInfo.issueUser"),
            @Mapping(target = "modifyUser", source = "memberCardInfo.issueUser"),
            @Mapping(target = "state", constant = "1"),
            @Mapping(target = "lastLevelChangeDate", source = "lastLevelChangeDate")
    })
    MemberCardInfo convertDtoToPo(IssueMemberCardDto memberCardInfo, LocalDateTime lastLevelChangeDate);

    @Mappings({
            @Mapping(target = "changeType", source = "changeType"),
            @Mapping(target = "reason", source = "reason"),
            @Mapping(target = "createUser", source = "memberCardInfo.issueUser"),
            @Mapping(target = "modifyUser", source = "memberCardInfo.issueUser")
    })
    MemberCardLevelChangeRecord convertDtoToPo(Integer changeType, String reason, IssueMemberCardDto memberCardInfo);

    @Mappings({
            @Mapping(target = "masterType", source = "dto.masterType"),
            @Mapping(target = "masterCode", source = "dto.masterCode"),
            @Mapping(target = "memberNo", source = "entity.memberNo"),
            @Mapping(target = "memberCardNo", source = "entity.memberCardNo"),
            @Mapping(target = "preLevel", source = "entity.level"),
            @Mapping(target = "preLevelName", source = "entity.levelName"),
            @Mapping(target = "effectBeginDate", source = "dto.effectBeginDate"),
            @Mapping(target = "effectEndDate", source = "dto.effectEndDate"),
            @Mapping(target = "changeType", source = "dto.changeType"),
            @Mapping(target = "reason", source = "dto.reason"),
            @Mapping(target = "createUser", source = "dto.operator"),
            @Mapping(target = "modifyUser", source = "dto.operator")
    })
    MemberCardLevelChangeRecord convertDtoToPo(BatchUpdateCardLevelDto dto, MemberCardLevelInfo entity);

    default void fillMemberCard(MemberCardInfo memberCardInfo, BatchUpdateCardLevelDto batchUpdateCardLevelDto){
        memberCardInfo.setCardLevel(batchUpdateCardLevelDto.getAfterLevel());
        memberCardInfo.setEffectBeginDate(batchUpdateCardLevelDto.getEffectBeginDate());
        memberCardInfo.setEffectEndDate(batchUpdateCardLevelDto.getEffectEndDate());
        memberCardInfo.setLastLevelChangeDate(LocalDateTime.now());
        memberCardInfo.setModifyUser(batchUpdateCardLevelDto.getOperator());
    }

    @Mappings({
            @Mapping(target = "issueUser", source = "operator")
    })
    IssueMemberCardDto convertDtoToDto(PurchaseCardDto purchaseCardDto);

    @Mappings({
            @Mapping(target = "cardLevel", source = "afterLevel"),
            @Mapping(target = "modifyUser", source = "operator")
    })
    void convertMemberCardInfo(@MappingTarget MemberCardInfo memberCardInfo, UpdateCardLevelDto updateCardLevelDto);

    UpdateCardLevelDto convertDtoToDto(UpgradeMemberDto dto);
}
