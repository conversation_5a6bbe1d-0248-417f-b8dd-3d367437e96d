package com.ly.titc.pms.member.mediator.converter;

import com.ly.titc.pms.member.dal.entity.po.*;
import com.ly.titc.pms.member.mediator.entity.dto.data.*;
import org.mapstruct.Mapper;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @Author：rui
 * @name：MemberDataRecordConverter
 * @Date：2024-12-11 21:43
 * @Filename：MemberDataRecordConverter
 */
@Mapper(componentModel = "spring", imports = {Arrays.class})
public interface MemberDataRecordConverter {

    default List<CheckinRecordDto> convertPoToDto(List<MemberCheckInRecord> memberCheckInRecords, Map<String, String> hotelNameMap) {
        return memberCheckInRecords.stream().map(e -> {
            CheckinRecordDto checkinRecordDto = convertPoToDto(e);
            checkinRecordDto.setHotelName(hotelNameMap.get(e.getHotelCode()));
            return checkinRecordDto;
        }).collect(Collectors.toList());
    }

    CheckinRecordDto convertPoToDto(MemberCheckInRecord memberCheckInRecord);

    CheckInStatisticsDto convertPoToDto(MemberCheckInStatistics memberCheckInStatistics);

    List<PurchaseCardRecordDto> convertPoToPurchaseCardRecordDto(List<MemberOrderInfo> memberCheckInStatistics);
    default List<PurchaseCardRecordDto> convertPoToPurchaseCardRecordDto(List<MemberOrderInfo> orderInfoList, List<MemberOrderDetailInfo> memberOrderDetailInfo) {
        List<PurchaseCardRecordDto> result = convertPoToPurchaseCardRecordDto(orderInfoList);
        Map<String, MemberOrderDetailInfo> map = memberOrderDetailInfo.stream().collect(java.util.stream.Collectors.toMap(MemberOrderDetailInfo::getMemberOrderNo, detailInfo -> detailInfo));
        for (PurchaseCardRecordDto purchaseCardRecordDto : result) {
            purchaseCardRecordDto.setDetail(convertPoToPurchaseCardRecordDetailDto(map.get(purchaseCardRecordDto.getMemberOrderNo())));
        }
        return result;
    }

    PurchaseCardRecordDetailDto convertPoToPurchaseCardRecordDetailDto(MemberOrderDetailInfo memberOrderDetailInfo);

    List<MemberLevelChangeRecordDto> convertPoToMemberLevelChangeRecordDto(List<MemberCardLevelChangeRecord> memberOrderInfo);
}
